# ProofPay Local Setup Guide

This guide will help you set up and run the ProofPay application on your local machine.

## Prerequisites

- Node.js v18+ (recommended)
- PostgreSQL installed and running locally
- Git (to clone the repository)

## Setup Steps

### 1. Create a local PostgreSQL database

```bash
# Create the database (run in terminal)
createdb proofpay
```

### 2. Create an .env file

Create a file named `.env` in the project root with the following content:

```
DATABASE_URL=postgres://postgres:postgres@localhost:5432/proofpay
VITE_BLS_PRIVATE_KEY=0x0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef
SIMULATOR_ENABLED=false
```

Adjust the PostgreSQL connection string if your local setup has different credentials.

### 3. Install dependencies

```bash
npm install
```

### 4. Run the database setup

```bash
npm run setup:db
```

This will:
- Verify database connectivity
- Create necessary database tables
- Create a `files` directory for storing documents

### 5. Start the development server

```bash
npm run dev:local
```

This will start:
- The backend server on port 5000
- The Vite development server with HMR

### 6. Access the application

Open your browser and go to:

```
http://localhost:5173
```

## Troubleshooting

### Port Binding Issues

If you encounter `Error: listen ENOTSUP: operation not supported on socket 0.0.0.0:5000`, the application is trying to bind to all interfaces which may not be supported on your system. The fix is included in this codebase, which automatically uses a simpler binding method on macOS.

### Database Connection Issues

- Ensure PostgreSQL is running
- Verify the connection string in `.env` is correct
- Check that the database exists: `psql -l` should list 'proofpay'

### Node.js Version Issues

If you encounter compatibility issues, ensure you're using Node.js v18:

```bash
# Using nvm to switch to Node.js v18
nvm install 18
nvm use 18
```

### File Storage Issues

If you encounter file storage errors:
- Ensure the `files` directory exists in the project root
- Check file permissions - the application needs write access

## Application Features

Once running, you can:

1. **Accounts Payable**:
   - Import payment files
   - Approve payments with cryptographic signatures
   - Send payments to the blockchain
   - Import receipts for verification
   - Generate reconciliation documents

2. **Accounts Receivable**:
   - Create/import invoices
   - Receive payments
   - Link payments to invoices
   - Generate reconciliation documents

## Development Notes

- The application uses a local PostgreSQL database instead of Neon.tech (which was used in Replit)
- File storage uses the local filesystem in the `files` directory
- The BLS signature system is simulated for demonstration purposes 
# ProofPay: ERP Integration Middleware For Private Stablecoin Payments On Proofbase

## Overview

ProofPay is an ERP transaction management platform built to streamline accounts payable and receivable operations with blockchain integration for private stablecoin payments. The application supports standard ERP payment, invoice, and reconcilliation file formats to provide simple, real-time payment processing on blockchain, without the complexity.

## Key Features

### Accounts Payable
- **Payment Management**: Import, approve, and process outgoing payments
- **Approval Workflow**: Structured approval flow with revocation capabilities
- **BLS Cryptographic Signatures**: Secure payment authorization with cryptographic proof
- **Blockchain Integration**: Simulated blockchain payment sending
- **Manual Receipt Import**: Import payment receipts for reconciliation
- **Reconciliation**: Generate standardized reconciliation documents in various formats (MT940, BAI2, ISO20022)

### Accounts Receivable
- **Invoice Management**: Create and track invoices with automatic status updates
- **Payment Receiving**: Monitor and process incoming payments
- **Manual Payment Import**: Import received payments via JSON data
- **Payment-Invoice Linking**: Connect received payments to open invoices
- **Reconciliation Generation**: Create standardized reconciliation documents

### Shared Capabilities
- **File Import/Export**: Support for multiple financial document formats
- **Historical Tracking**: Permanent record of all transactions
- **Status Filtering**: Filter by payment/invoice status
- **Search Functionality**: Find payments and invoices across the system

## Technical Architecture

### Frontend
- **Framework**: React with TypeScript
- **State Management**: TanStack Query (React Query) for server state
- **Styling**: Tailwind CSS with Shadcn/UI components
- **Routing**: Wouter for lightweight routing
- **Form Handling**: React Hook Form with Zod validation

### Backend
- **Server**: Express.js with TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Express-session with Passport.js
- **API Layer**: RESTful API for all financial operations

## Getting Started

### Prerequisites
- Node.js 18+
- PostgreSQL database

### Installation

1. Clone the repository
```
git clone https://github.com/yourusername/proofpay.git
cd proofpay
```

2. Install dependencies
```
npm install
```

3. Configure environment variables
```
# Create a .env file with the following variables
DATABASE_URL=postgresql://username:password@localhost:5432/proofpay

# BLS signature configuration
BLS_PRIVATE_KEY=your_private_key_here  # For testing only, use proper key management in production
BLS_PUBLIC_KEY=your_public_key_here    # For signature verification

# Network simulation configuration
SIMULATOR_ENABLED=false                # Set to true to enable simulated network delays
```

4. Run database migrations
```
npm run db:push
```

5. Start the development server
```
npm run dev
```

## Usage Guides

### Accounts Payable Process Flow

1. **Import Payment Files**: Use the "Import" button to upload payment files (PEXR2002, MT103, ISO20022 formats)
2. **Approve Payments**: Review and approve payments in the "Approve" column (generates BLS signatures)
3. **Send Payments**: Send approved payments to the blockchain network (downloads transaction file)
4. **Import Receipt**: Import receipt for a sent payment via JSON format
5. **Generate Reconciliation**: Create reconciliation documents for sent payments

### Accounts Receivable Process Flow

1. **Create/Import Invoices**: Use the "Import" button to upload invoice files (EDI X12, ISO20022 formats)
2. **Monitor Incoming Payments**: View received payments in the "Received Payments" column
3. **Import Manual Payment**: Import received payment via JSON data
4. **Link Payments**: Connect received payments to open invoices
5. **Generate Reconciliation**: Create reconciliation documents for paid invoices

### Payment Import/Export JSON Format

For manual import and receipt functions, use the following JSON format:

**Import Payment (AR):**
```json
{
  "from": "Sender Company Name",
  "amount": 1000.00,
  "reference": "REF-12345"
}
```

**Import Receipt (AP):**
```json
{
  "account": "Recipient Account ID",
  "amount": 1000.00,
  "reference": "REF-12345"
}
```

## File Format Support

### Payment Formats
- PEXR2002 (Proprietary Exchange Format)
- MT103 (SWIFT Message Type)
- ISO20022 (International Standard)

### Invoice Formats
- EDI X12 (Electronic Data Interchange)
- ISO20022 (International Standard)

### Reconciliation Formats
- MT940 (SWIFT Customer Statement Message)
- BAI2 (Bank Administration Institute)
- ISO20022 (International Standard)

## Project Structure

```
proofpay/
├── client/                  # Frontend React application
│   ├── src/
│   │   ├── assets/          # Static assets and images
│   │   ├── components/      # Reusable UI components
│   │   ├── hooks/           # Custom React hooks
│   │   ├── lib/             # Utility functions and helpers
│   │   ├── pages/           # Page components and routes
│   │   └── App.tsx          # Main application component
├── server/                  # Backend Express application
│   ├── utils/               # Server utility functions
│   ├── index.ts             # Server entry point
│   ├── routes.ts            # API routes
│   ├── storage.ts           # Database access layer
│   └── vite.ts              # Vite development server
├── shared/                  # Shared code between client and server
│   └── schema.ts            # Database schema and types
└── files/                   # Uploaded and generated files
```

## Code Documentation

The codebase is extensively documented with JSDoc comments explaining:

1. Component purposes and behaviors
2. Data flow and state management
3. Complex business logic and financial operations
4. API interactions and optimistic updates

Key areas of documentation include:

- Data hooks (`usePayments`, `useInvoices`, `useReceivedPayments`)
- UI components (`KanbanBoard`, `DetailPanel`, card components)
- Main workflow pages (`AccountsPayable`, `AccountsReceivable`)
- Schema definitions and database models

## Development Guidelines

- **Status Terminology**: "Reconciled" is used throughout the app (previously "Remitted")
- **Column Consistency**: Items remain in columns even after status changes to maintain context
- **Column Sorting**: Items are sorted by creation/action date, newest first
- **Animations**: Smooth transitions between states without refreshing entire columns
- **Error Handling**: Clear error messages with specific validation requirements

## Testing

The application includes a comprehensive test suite built with:

- **Vitest**: Fast Vite-based test runner
- **React Testing Library**: DOM testing utilities
- **MSW (Mock Service Worker)**: API mocking for tests
- **User Event**: User interaction simulation

### Running Tests

Tests can be run using the provided script:

```bash
# Run all tests
./run-tests.sh

# Run tests in watch mode
./run-tests.sh -w

# Run with UI
./run-tests.sh -u

# Run with coverage
./run-tests.sh -c

# Filter tests
./run-tests.sh -f PaymentCard
```

Alternatively, run tests directly with Vitest:

```bash
# Run all tests
npx vitest run

# Run tests in watch mode
npx vitest
```

For more comprehensive testing documentation, see [TESTING.md](TESTING.md).

### Test Coverage

The test suite covers key application areas:

- **Components**: UI rendering and interactions
- **Hooks**: Data fetching and state management
- **Utility Functions**: Helper functionality
- **API Integration**: Server request/response handling

# ProofPay Technical Documentation for AI Agents

This document provides comprehensive technical details about the ProofPay application, designed specifically for AI agents to understand the codebase structure, architectural patterns, data flow, and implementation details to facilitate maintenance and feature implementation.

## Application Overview

ProofPay is a financial transaction management platform for handling accounts payable and receivable with blockchain integration. The application provides a Kanban-style interface for tracking payment lifecycles through various statuses and generating standardized reconciliation documents.

## Project Architecture

### Technology Stack

- **Frontend**: React 18 with TypeScript, using functional components and hooks
- **State Management**: TanStack Query (React Query) v5 for server state caching
- **Styling**: Tailwind CSS with custom components via shadcn/ui
- **Forms**: React Hook Form with Zod validation
- **Routing**: Wouter for lightweight client-side routing
- **Backend**: Express.js with TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Validation**: Zod for schema validation across frontend and backend
- **File Storage**: Local file system for storing imported/exported financial documents

### Directory Structure

```
proofpay/
├── client/                  # Frontend React application
│   ├── src/
│   │   ├── assets/          # Static assets and images
│   │   ├── components/      # Reusable UI components
│   │   │   ├── ui/          # shadcn/ui components
│   │   │   ├── DetailPanel.tsx  # Detail slide-out panel 
│   │   │   ├── PaymentCard.tsx  # Payment card component
│   │   │   ├── InvoiceCard.tsx  # Invoice card component
│   │   │   └── ...
│   │   ├── hooks/           # Custom React hooks
│   │   │   ├── usePayments.ts    # Payment data and mutations
│   │   │   ├── useInvoices.ts    # Invoice data and mutations
│   │   │   └── ...
│   │   ├── lib/             # Utility functions and helpers
│   │   │   ├── queryClient.ts    # TanStack Query configuration
│   │   │   ├── dateUtils.ts      # Date formatting utilities
│   │   │   ├── fileHelpers.ts    # File processing utilities
│   │   │   └── utils.ts          # General utility functions
│   │   ├── pages/           # Page components and routes
│   │   │   ├── AccountsPayable.tsx   # AP workflow page
│   │   │   ├── AccountsReceivable.tsx  # AR workflow page
│   │   │   └── Home.tsx          # Dashboard/landing page
│   │   ├── App.tsx          # Main application component with routes
│   │   ├── index.css        # Global styles and animations
│   │   └── main.tsx         # Application entry point
├── server/                  # Backend Express application
│   ├── index.ts             # Server entry point
│   ├── routes.ts            # API routes definitions
│   ├── storage.ts           # Database access layer interface
│   ├── db.ts                # Database connection and Drizzle setup
│   └── vite.ts              # Vite development server integration
├── shared/                  # Shared code between client and server
│   └── schema.ts            # Drizzle database schema and types
└── files/                   # Uploaded and generated files storage
```

## Core Data Model

The application uses Drizzle ORM with a PostgreSQL database. Schema is defined in `shared/schema.ts` and includes:

### Database Entities

1. **Users**: Basic authentication model
   - `id`: Primary key
   - `username`: Unique username
   - `password`: Password (should be hashed in production)

2. **Payments**: Outgoing payments in Accounts Payable
   - `id`: Primary key
   - `reference`: Unique reference number
   - `amount`: Payment amount
   - `sender`: Company sending payment
   - `recipient`: Payment recipient
   - `status`: Payment status ("Not Approved", "Approved", "Paid", "Reconciled")
   - `approved`: Boolean indicating approval
   - `signature`: BLS cryptographic signature for payment verification
   - `message`: Message hash used for signature verification
   - `receipt_imported`: Boolean indicating if a receipt has been imported
   - `file_content`: Original imported file content
   - Additional fields for timestamps and linked reconciliations

3. **Remittances**: Reconciliation documents
   - `id`: Primary key
   - `payment_id`: Foreign key to payment/invoice
   - `format`: File format (MT940, BAI2, ISO20022)
   - `file_path`: Path to generated file
   - Additional fields for timestamps and transaction details

4. **Invoices**: Incoming invoices in Accounts Receivable
   - `id`: Primary key
   - `reference`: Unique reference number
   - `amount`: Invoice amount
   - `customer`: Customer name
   - `due_date`: Payment due date
   - `status`: Invoice status ("Open", "Overdue", "Paid", "Reconciled")
   - Additional fields for file content and linked payments

5. **ReceivedPayments**: Incoming payments in Accounts Receivable
   - `id`: Primary key
   - `reference`: Reference number
   - `amount`: Payment amount
   - `sender`: Payment sender
   - `status`: Payment status ("Unlinked", "Linked", "Reconciled")
   - Additional fields for invoice linking and reconciliation

## Data Flow and State Management

The application follows a clear data flow pattern:

1. **Data Fetching**: 
   - Uses TanStack Query for data fetching, caching, and invalidation
   - Custom hooks (`usePayments`, `useInvoices`, `useReceivedPayments`) wrap queries and mutations
   - `queryClient.ts` configures global query behaviors

2. **Data Mutation**:
   - Mutations are defined in the custom hooks
   - Use optimistic updates for immediate UI feedback
   - Invalidate queries after mutations to ensure fresh data
   - Server-side validation with Zod schemas

3. **State Management Pattern**:
   - Server state: Managed by TanStack Query
   - UI state: Managed by React state (useState, useReducer)
   - Form state: Managed by React Hook Form

## Frontend Architecture Details

### Component Structure

1. **Page Components**: 
   - High-level pages that serve as containers for the workflow
   - Import and compose smaller components
   - Handle route-specific logic
   - Example: `AccountsPayable.tsx`, `AccountsReceivable.tsx`

2. **KanbanBoard Component**:
   - Main container for displaying columns of cards
   - Takes arrays of items and renders them in columns
   - Manages horizontal scrolling and layout
   - Used in both AP and AR pages with different configurations

3. **Card Components**:
   - Represent individual items (payments, invoices, received payments)
   - Display summary information with consistent format
   - Handle click events to show detail panels
   - Examples: `PaymentCard.tsx`, `InvoiceCard.tsx`, `ReceivedPaymentCard.tsx`

4. **DetailPanel Component**:
   - Slide-out panel showing detailed information and action buttons
   - Dynamically renders different content based on item type and status
   - Handles animations for opening/closing
   - Used across the application for consistent UX

5. **UploadModal Component**:
   - Modal dialog for importing files
   - Handles file selection, validation, and upload
   - Used for both payment and invoice imports

### Hooks Implementation

1. **usePayments**:
   - Fetches all payments with `useQuery`
   - Provides filtered arrays for different payment statuses
   - Includes mutations for payment approval, revocation, sending
   - Handles optimistic updates and cache invalidation

2. **useInvoices**:
   - Fetches all invoices with `useQuery`
   - Provides filtered arrays for different invoice statuses
   - Includes mutations for status updates and payment linking
   - Automatically detects overdue invoices based on due date

3. **useReceivedPayments**:
   - Fetches all received payments with `useQuery`
   - Provides filtered arrays for different payment statuses
   - Includes mutations for linking payments to invoices
   - Handles simulated payment creation for testing

### Styling and Animations

1. **Styling System**:
   - Tailwind CSS for utility classes
   - shadcn/ui components for consistent UI elements
   - Custom component styles in `index.css`
   - Uses `cn()` utility for conditional class application

2. **Animation System**:
   - CSS animations for card movements between columns
   - Smooth transitions for status changes
   - Slide effects for detail panels
   - Classes like `slide-up-out`, `slide-down-in` control animations

3. **Status Styles**:
   - Consistent color coding for different statuses
   - Custom classes like `status-paid`, `status-reconciled`
   - Applied to badges and status indicators

## Backend Architecture Details

### API Routes

1. **Payments API**:
   - `GET /api/payments`: Get all payments
   - `GET /api/payments/:id`: Get specific payment
   - `POST /api/payments/:id/approve`: Approve payment
   - `POST /api/payments/:id/revoke`: Revoke approval
   - `POST /api/payments/:id/send`: Send payment (simulated blockchain)
   - `POST /api/payments/:id/generate-remittance`: Generate reconciliation
   - `POST /api/upload_payment`: Upload payment file

2. **Invoices API**:
   - `GET /api/invoices`: Get all invoices
   - `GET /api/invoices/:id`: Get specific invoice
   - `POST /api/invoices`: Create invoice
   - `POST /api/invoices/:id/update-status`: Update invoice status
   - `POST /api/invoices/:id/generate-remittance`: Generate reconciliation
   - `POST /api/upload_invoice`: Upload invoice file

3. **Received Payments API**:
   - `GET /api/received-payments`: Get all received payments
   - `GET /api/received-payments/:id`: Get specific payment
   - `POST /api/received-payments/:id/link-invoice`: Link to invoice
   - `POST /api/simulate_received_payment`: Create test payment

4. **Remittances API**:
   - `GET /api/remittances/:id`: Get reconciliation details
   - `GET /api/remittances/:id/download`: Download reconciliation file

### Storage Layer

The `storage.ts` file implements the `IStorage` interface, which provides a consistent API for data persistence. The implementation uses Drizzle ORM to interact with PostgreSQL:

```typescript
export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Payment methods
  createPayment(payment: InsertPayment): Promise<Payment>;
  getPayment(id: number): Promise<Payment | undefined>;
  getAllPayments(): Promise<Payment[]>;
  approvePayment(id: number): Promise<Payment | undefined>;
  // ...other methods
}
```

### File Handling

Files are stored on the server filesystem:
- Imported files (payments, invoices) have their content stored in the database
- Generated files (reconciliations) are saved to the filesystem with paths stored in the database
- Files are accessed via API endpoints that read from the filesystem

## Key Workflows

### Accounts Payable Workflow

1. **Import Payment**:
   - Upload payment file (PEXR2002, MT103, ISO20022)
   - System parses file and creates payment record
   - Payment appears in "Not Approved" column with status "Not Approved"

2. **Approve Payment**:
   - User reviews payment details and clicks "Approve"
   - BLS cryptographic signature is generated for the payment
   - Payment status changes to "Approved"
   - Payment remains in "Approval" column but with updated status and actions
   - Signature and message hash are stored with the payment

3. **Send Payment**:
   - User clicks "Send Payment" on approved payment
   - Backend simulates blockchain transaction
   - Payment status changes to "Paid"
   - Transaction JSON file is downloaded (contains payment details and signature)
   - Payment moves to "Sent" column

4. **Import Receipt**:
   - User can import a receipt JSON file for a payment
   - System marks the payment with receipt_imported=true
   - Payment appears in "Reconciliation" column but maintains "Paid" status
   - This allows manual verification of payment receipt before reconciliation

5. **Generate Reconciliation**:
   - User selects reconciliation format and generates document
   - System creates file in specified format
   - Payment status changes to "Reconciled"
   - Reconciliation document is available for download

### Accounts Receivable Workflow

1. **Create/Import Invoice**:
   - User creates invoice manually or imports file
   - Invoice appears in "Invoices" column with status "Open"
   - System automatically changes status to "Overdue" when due date passes

2. **Receive Payment**:
   - Simulated or actual payment comes in
   - Payment appears in "Received Payments" column with status "Unlinked"

3. **Link Payment to Invoice**:
   - User links received payment to matching invoice
   - Payment status changes to "Linked"
   - Invoice status changes to "Paid"
   - Reference numbers are used for automatic matching when possible

4. **Generate Reconciliation**:
   - User generates reconciliation document for the payment/invoice
   - Status changes to "Reconciled"
   - Reconciliation document is available for download

## Application States and Transitions

### Payment Status Flow

```
Not Approved → Approved → Paid → Reconciled
    ^              |         |
    |              v         v
    └───── Revoked ←   Receipt Imported (flag)
                      (appears in Reconciliation column)
```

The updated Accounts Payable workflow includes an important receipt import step:
1. After a payment is sent and has status "Paid", it doesn't automatically appear in the Reconciliation column
2. User must manually import a receipt using the "Import Receipt" button
3. This sets the receipt_imported flag to true WITHOUT changing the status from "Paid"
4. The payment then appears in the Reconciliation column for manual verification
5. After verification, the user can generate reconciliation documents, changing status to "Reconciled"

### Invoice Status Flow

```
Open → Overdue → Paid → Reconciled
```

### Received Payment Status Flow

```
Unlinked → Linked → Reconciled
```

## Critical Implementation Details

### BLS Cryptography Implementation

- **Purpose**: The application uses BLS (Boneh-Lynn-Shacham) signatures to cryptographically verify payment authorization
- **Core Implementation**: Located in `client/src/crypto/bls.ts`
- **Key Components**:
  - Uses @chainsafe/bls library for cryptographic operations
  - Private key is stored in client-side environment variables for security
  - The signature workflow:
    1. When approving a payment, client hashes payment details (reference, recipient, amount)
    2. Client signs the hash with the private key using BLS algorithm
    3. Both signature and message hash are stored with the payment record
    4. When verifying, the application checks signature against the message hash
  - Implementation uses a promise-based initialization (`blsReady`) to ensure WASM is loaded
  - Network simulation can be toggled with the SIMULATOR_ENABLED environment variable

### Manual Payment/Receipt Import

- **Import Receipt** (Accounts Payable):
  - Uses `receipt_imported` field in payments table to track imported receipts
  - Imported receipts maintain "Paid" status (not automatically changed to "Reconciled")
  - Payments with imported receipts appear in Reconciliation column for manual processing
  - ImportReceiptModal component provides JSON input interface for receipt data
  - **Implementation Details**:
    - `markReceiptImported` method in storage.ts sets receipt_imported=true without changing status
    - `/api/import/receipt` endpoint accepts JSON with account, amount, and reference fields
    - `sentPayments` filtered array in usePayments.ts checks for receipt_imported=true flag
    - Fixed workflow prevents sent payments from auto-populating reconciliation column
    - Expected JSON format: `{"account": "Recipient Account", "amount": 1000, "reference": "REF123"}`

- **Import Payment** (Accounts Receivable):
  - Allows manual import of received payments via JSON data
  - ImportPaymentModal component handles validation and submission
  - Imported payments start with "Unlinked" status for manual invoice linking
  - **Implementation Details**:
    - `markPaymentReceived` method in storage.ts creates a new ReceivedPayment record
    - `/api/import/received-payment` endpoint supports both "sender" and "from" field names
    - Flexibility in field naming accommodates different JSON formats
    - Manual import bypasses network simulator for direct testing
    - Expected JSON format: `{"sender": "Company Name", "amount": 1000, "reference": "REF123"}`

### Status Management

- Status changes are managed server-side to ensure consistency
- UI optimistically updates for immediate feedback
- Event-based status transitions (e.g., due date triggers Overdue status)
- Status used for filtering in Kanban columns

### Data Integrity

- Reference numbers must be unique for payments and invoices
- Amount validation ensures correct decimal handling
- Automatic status change for overdue invoices based on due date
- Database constraints prevent invalid operations

### File Format Handling

- Template-based generation for reconciliation documents
- Parsers for different import formats (PEXR2002, MT103, ISO20022, EDI X12)
- Validation to ensure file contents match expected format
- Error handling for malformed files

## Implementation Quirks & Important Notes

1. **Terminology Change**: Throughout development, "Remittance" was renamed to "Reconciliation" in UI displays, but variable/function names may still use "remittance" internally.

2. **Animation Requirements**: Column items must:
   - Not refresh entire columns on individual item status changes
   - Smoothly transition between states
   - Appear at the top when added
   - Only move between columns at specific state transitions

3. **Duplicate Prevention**: The system checks for duplicate reference numbers and prevents creation of duplicate records, showing appropriate warnings.

4. **Column Behavior**:
   - Items may appear in multiple columns simultaneously depending on status
   - AP page: Approved payments show in both "Approve" and "Payment" columns
   - Only after payment is "Paid" does it disappear from Approval column

5. **Detail Panel Logic**:
   - Different actions are available based on item status and column
   - In AP, Approved payment in Approve column only shows "Revoke Approval"
   - In AP, Approved payment in Payment column only shows "Send Payment"

6. **File Processing**:
   - Upload modal stays open after detecting duplicates
   - Duplicate files are automatically removed from upload list
   - Reference numbers from files are used for automatic linking

## Adding New Features

When implementing new features, follow these guidelines:

### 1. Database Schema Changes

- Add new table or column definitions in `shared/schema.ts`
- Create corresponding insert schemas and type definitions
- Run database migration using `npm run db:push`

### 2. Backend Implementation

- Add new methods to the `IStorage` interface in `storage.ts`
- Implement those methods in the `DatabaseStorage` class
- Add new API routes in `routes.ts` that use the storage methods
- Include proper validation using Zod schemas

### 3. Frontend Implementation

- Create custom hook for the new feature in `client/src/hooks/`
- Add UI components in `client/src/components/`
- Update or create page components in `client/src/pages/`
- Add any required utility functions in `client/src/lib/`

### 4. File Format Changes

- To add new import/export formats:
  - Add format to `fileFormats` in `shared/schema.ts`
  - Create parser/generator in server-side utils
  - Update UI with new format option

### 5. Example: Adding Document Attachments

1. Schema changes:
   ```typescript
   // In schema.ts
   export const attachments = pgTable("attachments", {
     id: serial("id").primaryKey(),
     payment_id: integer("payment_id"),
     invoice_id: integer("invoice_id"),
     file_path: text("file_path").notNull(),
     file_name: text("file_name").notNull(),
     created_at: timestamp("created_at").defaultNow().notNull(),
   });
   
   export const insertAttachmentSchema = createInsertSchema(attachments).omit({
     id: true,
     created_at: true,
   });
   
   export type InsertAttachment = z.infer<typeof insertAttachmentSchema>;
   export type Attachment = typeof attachments.$inferSelect;
   ```

2. Storage interface update:
   ```typescript
   // In storage.ts
   export interface IStorage {
     // ...existing methods
     
     // Attachment methods
     createAttachment(attachment: InsertAttachment): Promise<Attachment>;
     getAttachmentsByPaymentId(paymentId: number): Promise<Attachment[]>;
     getAttachmentsByInvoiceId(invoiceId: number): Promise<Attachment[]>;
     deleteAttachment(id: number): Promise<void>;
   }
   ```

3. API route implementation:
   ```typescript
   // In routes.ts
   app.post("/api/attachments", upload.single("file"), async (req: Request, res: Response) => {
     try {
       const { payment_id, invoice_id } = req.body;
       
       if (!req.file) {
         return res.status(400).json({ error: "No file uploaded" });
       }
       
       const attachment = await storage.createAttachment({
         payment_id: payment_id ? parseInt(payment_id) : null,
         invoice_id: invoice_id ? parseInt(invoice_id) : null,
         file_path: req.file.path,
         file_name: req.file.originalname,
       });
       
       res.status(201).json(attachment);
     } catch (error: any) {
       res.status(500).json({ error: error.message });
     }
   });
   ```

4. Frontend hook:
   ```typescript
   // In hooks/useAttachments.ts
   export function useAttachments(entityType: 'payment' | 'invoice', entityId: number) {
     const queryKey = [`/api/attachments/${entityType}/${entityId}`];
     
     const { data: attachments = [], isLoading, error } = useQuery({
       queryKey,
       enabled: !!entityId,
     });
     
     const uploadAttachmentMutation = useMutation({
       mutationFn: async (file: File) => {
         const formData = new FormData();
         formData.append('file', file);
         formData.append(`${entityType}_id`, entityId.toString());
         
         const res = await fetch('/api/attachments', {
           method: 'POST',
           body: formData,
         });
         
         if (!res.ok) {
           throw new Error('Failed to upload attachment');
         }
         
         return res.json();
       },
       onSuccess: () => {
         queryClient.invalidateQueries({ queryKey });
       },
     });
     
     return {
       attachments,
       isLoading,
       error,
       uploadAttachment: uploadAttachmentMutation.mutate,
       isUploading: uploadAttachmentMutation.isPending,
     };
   }
   ```

5. UI component:
   ```tsx
   // In components/AttachmentList.tsx
   export function AttachmentList({ 
     entityType, 
     entityId 
   }: { 
     entityType: 'payment' | 'invoice'; 
     entityId: number 
   }) {
     const { attachments, isLoading, uploadAttachment } = useAttachments(entityType, entityId);
     
     const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
       if (e.target.files?.[0]) {
         uploadAttachment(e.target.files[0]);
       }
     };
     
     return (
       <div className="mt-4">
         <h3 className="text-sm font-medium mb-2">Attachments</h3>
         
         {isLoading ? (
           <div>Loading attachments...</div>
         ) : attachments.length === 0 ? (
           <div className="text-sm text-gray-500">No attachments</div>
         ) : (
           <ul className="space-y-2">
             {attachments.map((attachment) => (
               <li key={attachment.id} className="flex items-center">
                 <a 
                   href={`/api/attachments/${attachment.id}/download`} 
                   className="text-blue-500 hover:underline"
                 >
                   {attachment.file_name}
                 </a>
               </li>
             ))}
           </ul>
         )}
         
         <div className="mt-2">
           <input
             type="file"
             id="file-upload"
             className="hidden"
             onChange={handleFileChange}
           />
           <label
             htmlFor="file-upload"
             className="btn btn-outline text-xs cursor-pointer"
           >
             Add Attachment
           </label>
         </div>
       </div>
     );
   }
   ```

## Troubleshooting Common Issues

1. **Cache Invalidation Problems**:
   - Check that queryClient.invalidateQueries is called after mutations
   - Ensure queryKey arrays are consistent (e.g., ['/api/payments'] vs '/api/payments')
   - Use Promise.all for refreshing related data that depends on each other

2. **Animation Issues**:
   - Check that item keys are consistent for React's reconciliation
   - Ensure animation classes are applied correctly
   - Verify CSS transitions and animations are defined

3. **State Management Bugs**:
   - Use React DevTools to inspect component state
   - Check TanStack Query Dev Tools for query/mutation status
   - Verify optimistic updates and error handling

4. **API Connection Issues**:
   - Ensure server is running on correct port
   - Check that API endpoints match between client and server
   - Verify correct HTTP methods (GET, POST) are used

5. **File Processing Errors**:
   - Check file format matches expected template
   - Verify file content parsing logic
   - Ensure proper error handling for malformed files

## Performance Considerations

1. **Query Optimization**:
   - Use staleTime and cacheTime appropriately in TanStack Query
   - Consider paginating large data sets
   - Use select() to transform query data when needed

2. **Rendering Optimization**:
   - Use memoization (useMemo, useCallback) for expensive calculations
   - Implement virtualization for long lists (e.g., react-window)
   - Consider lazy loading components for infrequently used features

3. **Animation Performance**:
   - Prefer CSS animations over JavaScript for smoother performance
   - Use transform and opacity for animations (GPU accelerated)
   - Be cautious with animations that trigger layout recalculations

## Future Development Ideas

1. **Real Blockchain Integration**:
   - Replace simulated blockchain with actual Web3 integration
   - Add cryptocurrency payment options
   - Implement smart contract templates for payment agreements

2. **Advanced Reporting**:
   - Add analytics dashboard for payment trends
   - Implement export to Excel/CSV functionality
   - Create visual payment flow diagrams

3. **Approval Workflow Enhancement**:
   - Multi-level approval process with roles
   - Approval delegation functionality
   - Scheduled/recurring payment support

4. **UI/UX Improvements**:
   - Drag-and-drop for payment status changes
   - Dark mode toggle
   - Enhanced mobile responsiveness

5. **Integration Capabilities**:
   - API authentication for third-party access
   - Webhook support for external notifications
   - Integration with accounting software (QuickBooks, Xero)
# Proofpay Testing Documentation

This document provides comprehensive guidance on running and extending the test suite for the Proofpay application.

## Overview

The Proofpay application includes a comprehensive testing suite built with the following technologies:

- **Vitest**: Fast Vite-based test runner
- **React Testing Library**: DOM testing utilities
- **MSW (Mock Service Worker)**: API mocking
- **User Event**: User interaction simulation

## Running Tests

Tests can be run using the following commands:

```bash
# Run all tests
npx vitest run

# Run tests in watch mode
npx vitest

# Run tests with UI
npx vitest --ui

# Run tests with coverage report
npx vitest run --coverage
```

## Test Suite Structure

The test suite is organized as follows:

```
tests/
├── components/         # Component tests
│   ├── PaymentCard.test.tsx
│   ├── InvoiceCard.test.tsx
│   ├── ReceivedPaymentCard.test.tsx
│   ├── DetailPanel.test.tsx
│   ├── KanbanBoard.test.tsx
│   └── UploadModal.test.tsx
├── hooks/              # Custom hook tests
│   ├── usePayments.test.tsx
│   ├── useInvoices.test.tsx
│   └── useReceivedPayments.test.tsx
├── mocks/              # Mock data and handlers
│   └── mockData.ts     # Mock data for testing
├── utils/              # Test utilities
│   └── test-utils.tsx  # Custom render method
├── setup.ts            # Global test setup and MSW configuration
└── README.md           # Testing documentation
```

## Mock Data

The test suite uses mock data defined in `tests/mocks/mockData.ts` to simulate the application's data structures:

- **Payments**: Outgoing payment records with various statuses
- **Invoices**: Invoice records with various statuses
- **Received Payments**: Incoming payment records with various statuses
- **Remittances**: Reconciliation documents for payments and invoices

## API Mocking

API requests are mocked using Mock Service Worker (MSW) in `tests/setup.ts`. This intercepts all API requests during tests and returns predefined responses based on the mock data.

## Component Testing Approach

Component tests verify that UI components render correctly and respond appropriately to user interactions. Key testing approaches include:

1. **Rendering Tests**: Verify components display the correct content
2. **Interaction Tests**: Verify components respond correctly to user interactions
3. **State Tests**: Verify components display correctly based on different props and states
4. **Styling Tests**: Verify components apply the correct styling based on their state

## Hook Testing Approach

Hook tests verify that custom hooks provide the correct data and functions. Testing approaches include:

1. **Data Tests**: Verify hooks return the expected data
2. **Mutation Tests**: Verify hooks correctly handle state mutations
3. **Error Handling**: Verify hooks handle errors correctly
4. **Loading States**: Verify hooks correctly report loading states

## Adding New Tests

### Component Tests

To add a new component test:

1. Create a file in `tests/components/` named after the component (e.g., `ComponentName.test.tsx`)
2. Import the component and necessary testing utilities
3. Write tests using the testing library pattern

Example:

```tsx
import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '../utils/test-utils';
import YourComponent from '@/components/YourComponent';

describe('YourComponent', () => {
  it('renders correctly', () => {
    render(<YourComponent prop="value" />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });
});
```

### Hook Tests

To add a new hook test:

1. Create a file in `tests/hooks/` named after the hook (e.g., `useHookName.test.tsx`)
2. Import the hook and testing utilities
3. Use `renderHook` to test the hook's behavior

Example:

```tsx
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import useYourHook from '@/hooks/useYourHook';

// Create wrapper with QueryClientProvider
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  
  return ({ children }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useYourHook', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  
  it('returns expected data', async () => {
    const { result } = renderHook(() => useYourHook(), {
      wrapper: createWrapper(),
    });
    
    // Test initial state
    expect(result.current.isLoading).toBe(true);
    
    // Wait for data to load
    await waitFor(() => expect(result.current.isLoading).toBe(false));
    
    // Test loaded state
    expect(result.current.data).toBeDefined();
  });
});
```

## Test Coverage

The test suite is configured to generate coverage reports when run with the `--coverage` flag. Coverage reports show which parts of the codebase are covered by tests.

To run tests with coverage:

```bash
npx vitest run --coverage
```

## Continuous Integration

The test suite can be integrated into a continuous integration (CI) pipeline by running `npx vitest run` as part of the build process. This ensures all tests pass before the code is deployed.

## Testing Best Practices

1. **Test behavior, not implementation**: Focus on what the user sees and interacts with.
2. **Use accessible queries**: Prefer queries that align with how users interact with the application (`getByRole`, `getByText`, etc.).
3. **Mock external dependencies**: Use `vi.mock()` to mock external dependencies like API calls.
4. **Test edge cases**: Include tests for loading states, error states, and boundary conditions.
5. **Isolate tests**: Each test should be independent of others and clean up after itself.
6. **Use descriptive test names**: Tests should clearly describe what they're testing and the expected outcome.
7. **Keep tests simple**: Tests should be as simple as possible while still being effective.
# ERP/SAP Payment Layer Demo Application Rebuild
Create a sophisticated ERP/SAP Stablecoin Payment Layer application that streamlines complex financial workflow management through advanced transaction processing and intelligent document handling. The application should handle the complete payment lifecycle from file upload, approval, payment sending, to remittance generation, with equivalent functionality to the reference application but implemented with a JavaScript frontend and TypeScript backend.
## Core Requirements:
1. Rebuild the application with a JavaScript frontend and TypeScript backend
2. Exact replication of functionality, UI components, workflow, and user experience
3. Match all API endpoints, data structures, and business logic
4. Implement the full bidirectional payment flow (Accounts Payable to Accounts Receivable)
## System Architecture
### Frontend (JavaScript):
- Use vanilla JavaScript with optional modern frameworks (React/Vue)
- Create a minimalist, Apple/OpenAI-inspired UI with clean white background and black/gray elements
- Implement the pipeline/kanban board layout for payment workflows
- Maintain exact workflow transitions (approve → send → remit)
- Support all interactive features (detail panels, modal dialogs, status transitions)
### Backend (TypeScript):
- Express.js or NestJS with TypeScript for type safety
- RESTful API endpoints matching the reference application
- PostgreSQL database with a TypeORM or Prisma ORM layer
- Structured file storage for payment files and remittances
- Network simulation module for payment transmission delays (5 seconds)
## Data Models & Functionality
### Core Models (Implement as TypeScript interfaces/classes):
1. **Payment**:
   - Properties: id, reference, amount, sender, recipient, status, file_type, approved, sent_at, file_content, remittance_generated, remittance_id
   - Status logic: Not Approved → Approved → Sent → Remitted
2. **Remittance**:
   - Properties: id, payment_id, status, format, created_at, file_path, amount, sender, recipient, reference
   - Format options: MT940, BAI2, ISO20022
3. **Invoice**:
   - Properties: id, customer, amount, reference, due_date, status, description, created_at, payment_id, remittance_id
   - Status logic: Open → Overdue → Paid → Remitted
4. **ReceivedPayment**:
   - Properties: id, sender, recipient, amount, reference, created_at, invoice_id, status, remittance_generated, remittance_id
   - Auto-linking to invoices via reference numbers
### File Processing:
- Parse and generate multiple payment and invoice file formats:
  - Payment formats: PEXR2002, MT103, ISO20022
  - Invoice formats: EDI X12 (810), ISO20022
  - Remittance formats: MT940, BAI2, ISO20022
- Generate appropriate sample files for testing
## User Interface Requirements
### Global UI:
- Navigation tabs: Home, Accounts Payable, Accounts Receivable
- Clean, minimalist design with subtle shadows and rounded corners (8px)
- Status badges: Blue (Sent), Green (Approved), Yellow (Not Approved), Gray (Remitted)
- Only one detail panel open at a time
### Accounts Payable (AP) Pipeline:
1. "Approve" column: Display payments to approve with status badges
2. "Payments" column: Show approved payments ready to send
3. "Remittance" column: Display sent payments ready for remittance generation
### Accounts Receivable (AR) Pipeline:
1. "Open Invoices" column: Show invoices awaiting payment
2. "Received Payments" column: Display payments received with auto-linking
3. "Reconciliation" column: Show reconciled payments with remittance options
### Detail Panels:
- Display detailed information when an item is clicked
- Action buttons in the panel relevant to the item's state
- Panel slides in from the right with animation
- Section headers to organize information
### Interactive Elements:
- Format selection dropdowns (MT940, BAI2, ISO20022)
- Status change animations (5-second overlay)
- Automatic movement of items between columns after status changes
- Hover and active states for cards and buttons
## API Endpoints (to be implemented in TypeScript)
### Payment API:
- GET /api/payments/:id - Get payment details
- POST /api/payments/:id/approve - Approve payment
- POST /api/payments/:id/revoke - Revoke payment approval
- POST /api/payments/:id/send - Send payment
- POST /api/payments/:id/generate-remittance - Generate remittance file
- POST /upload_payment - Upload and process payment file
### Remittance API:
- GET /api/remittances/:id - Get remittance details
- GET /api/remittances/:id/download - Download remittance file
- GET /api/remittances/:id/debug - Debug endpoint for remittance content
### Invoice API:
- GET /api/invoices - Get all invoices with filters
- GET /api/invoices/:id - Get invoice details
- POST /api/invoices - Create new invoice
- POST /api/invoices/:id/update-status - Update invoice status
- POST /api/invoices/:id/generate-remittance - Generate remittance for invoice
- GET /api/invoices/:id/download-remittance - Download invoice remittance
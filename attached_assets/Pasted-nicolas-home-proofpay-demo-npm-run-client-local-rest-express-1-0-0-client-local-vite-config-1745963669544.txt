nicolas@home proofpay-demo % npm run client:local

> rest-express@1.0.0 client:local
> vite --config vite.config.local.ts

/Users/<USER>/Documents/GitHub/proofpay-demo/node_modules/rollup/dist/native.js:63
		throw new Error(
		      ^

Error: Cannot find module @rollup/rollup-darwin-x64. npm has a bug related to optional dependencies (https://github.com/npm/cli/issues/4828). Please try `npm i` again after removing both package-lock.json and node_modules directory.
    at requireWithFriendlyError (/Users/<USER>/Documents/GitHub/proofpay-demo/node_modules/rollup/dist/native.js:63:9)
    at Object.<anonymous> (/Users/<USER>/Documents/GitHub/proofpay-demo/node_modules/rollup/dist/native.js:72:76)
    at Module._compile (node:internal/modules/cjs/loader:1723:14)
    at Object..js (node:internal/modules/cjs/loader:1888:10)
    at Module.load (node:internal/modules/cjs/loader:1458:32)
    at Function._load (node:internal/modules/cjs/loader:1275:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:234:24)
    at cjsLoader (node:internal/modules/esm/translators:311:5)
    at ModuleWrap.<anonymous> (node:internal/modules/esm/translators:204:7) {
  [cause]: Error: Cannot find module '@rollup/rollup-darwin-x64'
  Require stack:
  - /Users/<USER>/Documents/GitHub/proofpay-demo/node_modules/rollup/dist/native.js
      at Function._resolveFilename (node:internal/modules/cjs/loader:1394:15)
      at defaultResolveImpl (node:internal/modules/cjs/loader:1050:19)
      at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1055:22)
      at Function._load (node:internal/modules/cjs/loader:1204:37)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:234:24)
      at Module.require (node:internal/modules/cjs/loader:1480:12)
      at require (node:internal/modules/helpers:135:16)
      at requireWithFriendlyError (/Users/<USER>/Documents/GitHub/proofpay-demo/node_modules/rollup/dist/native.js:45:10)
      at Object.<anonymous> (/Users/<USER>/Documents/GitHub/proofpay-demo/node_modules/rollup/dist/native.js:72:76) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      '/Users/<USER>/Documents/GitHub/proofpay-demo/node_modules/rollup/dist/native.js'
    ]
  }
}

Node.js v23.7.0
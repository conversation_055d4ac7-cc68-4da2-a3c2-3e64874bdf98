nicolas@home proofpay-demo % npm run dev:local

> rest-express@1.0.0 dev:local
> concurrently "NODE_ENV=development tsx --watch server/index.ts" "vite --config vite.config.local.ts"

[0] (node:12165) ExperimentalWarning: Watch mode is an experimental feature and might change at any time
[0] (Use `node --trace-warnings ...` to show where the warning was created)
[1] 
[1]   VITE v5.4.18  ready in 151 ms
[1] 
[1]   ➜  Local:   http://localhost:5173/
[1]   ➜  Network: use --host to expose
[0] node:internal/errors:496
[0]     ErrorCaptureStackTrace(err);
[0]     ^
[0] 
[0] TypeError [ERR_INVALID_ARG_TYPE]: The "paths[0]" argument must be of type string. Received undefined
[0]     at __node_internal_captureLargerStackTrace (node:internal/errors:496:5)
[0]     at new NodeError (node:internal/errors:405:5)
[0]     at validateString (node:internal/validators:162:11)
[0]     at Object.resolve (node:path:1115:7)
[0]     at <anonymous> (/Users/<USER>/Documents/GitHub/proofpay-demo/vite.config.ts:23:17)
[0]     at ModuleJob.run (node:internal/modules/esm/module_job:195:25)
[0]     at async ModuleLoader.import (node:internal/modules/esm/loader:337:24)
[0]     at async loadESM (node:internal/process/esm_loader:34:7)
[0]     at async handleMainPromise (node:internal/modules/run_main:106:12) {
[0]   code: 'ERR_INVALID_ARG_TYPE'
[0] }
[0] 
[0] Node.js v18.20.8
[0] Failed running 'server/index.ts'
[0] Restarting 'server/index.ts'
[0] node:internal/errors:496
[0]     ErrorCaptureStackTrace(err);
[0]     ^
[0] 
[0] TypeError [ERR_INVALID_ARG_TYPE]: The "paths[0]" argument must be of type string. Received undefined
[0]     at __node_internal_captureLargerStackTrace (node:internal/errors:496:5)
[0]     at new NodeError (node:internal/errors:405:5)
[0]     at validateString (node:internal/validators:162:11)
[0]     at Object.resolve (node:path:1115:7)
[0]     at <anonymous> (/Users/<USER>/Documents/GitHub/proofpay-demo/vite.config.ts:23:17)
[0]     at ModuleJob.run (node:internal/modules/esm/module_job:195:25)
[0]     at async ModuleLoader.import (node:internal/modules/esm/loader:337:24)
[0]     at async loadESM (node:internal/process/esm_loader:34:7)
[0]     at async handleMainPromise (node:internal/modules/run_main:106:12) {
[0]   code: 'ERR_INVALID_ARG_TYPE'
[0] }
[0] 
[0] Node.js v18.20.8
[0] Failed running 'server/index.ts'
[0] Restarting 'server/index.ts'
[0] node:internal/errors:496
[0]     ErrorCaptureStackTrace(err);
[0]     ^
[0] 
[0] TypeError [ERR_INVALID_ARG_TYPE]: The "paths[0]" argument must be of type string. Received undefined
[0]     at __node_internal_captureLargerStackTrace (node:internal/errors:496:5)
[0]     at new NodeError (node:internal/errors:405:5)
[0]     at validateString (node:internal/validators:162:11)
[0]     at Object.resolve (node:path:1115:7)
[0]     at <anonymous> (/Users/<USER>/Documents/GitHub/proofpay-demo/vite.config.ts:23:17)
[0]     at ModuleJob.run (node:internal/modules/esm/module_job:195:25)
[0]     at async ModuleLoader.import (node:internal/modules/esm/loader:337:24)
[0]     at async loadESM (node:internal/process/esm_loader:34:7)
[0]     at async handleMainPromise (node:internal/modules/run_main:106:12) {
[0]   code: 'ERR_INVALID_ARG_TYPE'
[0] }
[0] 
[0] Node.js v18.20.8
[0] Failed running 'server/index.ts'
[0] Restarting 'server/index.ts'
[0] Restarting 'server/index.ts'
[0] Restarting 'server/index.ts'
[0] node:internal/errors:496
[0]     ErrorCaptureStackTrace(err);
[0]     ^
[0] 
[0] TypeError [ERR_INVALID_ARG_TYPE]: The "paths[0]" argument must be of type string. Received undefined
[0]     at __node_internal_captureLargerStackTrace (node:internal/errors:496:5)
[0]     at new NodeError (node:internal/errors:405:5)
[0]     at validateString (node:internal/validators:162:11)
[0]     at Object.resolve (node:path:1115:7)
[0]     at <anonymous> (/Users/<USER>/Documents/GitHub/proofpay-demo/vite.config.ts:23:17)
[0]     at ModuleJob.run (node:internal/modules/esm/module_job:195:25)
[0]     at async ModuleLoader.import (node:internal/modules/esm/loader:337:24)
[0]     at async loadESM (node:internal/process/esm_loader:34:7)
[0]     at async handleMainPromise (node:internal/modules/run_main:106:12) {
[0]   code: 'ERR_INVALID_ARG_TYPE'
[0] }
[0] 
[0] Node.js v18.20.8
[0] Failed running 'server/index.ts'
^C[0] NODE_ENV=development tsx --watch server/index.ts exited with code 0
[1] vite --config vite.config.local.ts exited with code SIGINT
nicolas@home proofpay-demo % npm run dev

> rest-express@1.0.0 dev
> NODE_ENV=development tsx server/index.ts

node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen ENOTSUP: operation not supported on socket 0.0.0.0:5000
    at Server.setupListenHandle [as _listen2] (node:net:1915:21)
    at listenInCluster (node:net:1994:12)
    at Server.listen (node:net:2099:7)
    at <anonymous> (/Users/<USER>/Documents/GitHub/proofpay-demo/server/index.ts:63:10)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1973:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'ENOTSUP',
  errno: -45,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 5000
}

Node.js v22.14.0
nicolas@home proofpay-demo % nvm install 18
Downloading and installing node v18.20.8...
Downloading https://nodejs.org/dist/v18.20.8/node-v18.20.8-darwin-arm64.tar.xz...
######################################################################################################################################################### 100.0%
Computing checksum with sha256sum
Checksums matched!
Now using node v18.20.8 (npm v10.8.2)
nicolas@home proofpay-demo % nvm use 18
Now using node v18.20.8 (npm v10.8.2)
nicolas@home proofpay-demo % npm run dev   

> rest-express@1.0.0 dev
> NODE_ENV=development tsx server/index.ts

node:internal/errors:496
    ErrorCaptureStackTrace(err);
    ^

TypeError [ERR_INVALID_ARG_TYPE]: The "paths[0]" argument must be of type string. Received undefined
    at __node_internal_captureLargerStackTrace (node:internal/errors:496:5)
    at new NodeError (node:internal/errors:405:5)
    at validateString (node:internal/validators:162:11)
    at Object.resolve (node:path:1115:7)
    at <anonymous> (/Users/<USER>/Documents/GitHub/proofpay-demo/vite.config.ts:23:17)
    at ModuleJob.run (node:internal/modules/esm/module_job:195:25)
    at async ModuleLoader.import (node:internal/modules/esm/loader:337:24)
    at async loadESM (node:internal/process/esm_loader:34:7)
    at async handleMainPromise (node:internal/modules/run_main:106:12) {
  code: 'ERR_INVALID_ARG_TYPE'
}

Node.js v18.20.8
/**
 * Import Payment Modal
 * 
 * This component provides a modal dialog for importing received payment data 
 * via JSON input for the Accounts Receivable workflow. It allows users to paste 
 * incoming payment data to manually record payments received.
 * 
 * Key features:
 * - Supports flexible field naming: both "sender" and "from" field names are accepted
 * - Creates a new received payment record without auto-linking to invoices
 * - Enables manual testing and simulation of incoming payments
 * - Part of the Accounts Receivable workflow
 * 
 * Expected JSON format:
 * {
 *   "sender": "Company Name",  // can also use "from" 
 *   "amount": 1000.00,
 *   "reference": "REF-12345"
 * }
 */

import { useState } from "react";
import { X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { apiRequest } from "@/lib/queryClient";
import { useQueryClient } from "@tanstack/react-query";

interface ImportPaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ImportPaymentModal = ({ isOpen, onClose }: ImportPaymentModalProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [text, setText] = useState<string>("");
  const [isImporting, setIsImporting] = useState(false);

  if (!isOpen) return null;

  const handleImport = async () => {
    if (!text.trim()) {
      toast({
        title: "Error",
        description: "Please enter valid JSON data",
        variant: "destructive",
      });
      return;
    }

    setIsImporting(true);
    try {
      const parsed = JSON.parse(text);
      await apiRequest("POST", "/api/import/received-payment", parsed);
      queryClient.invalidateQueries({ queryKey: ["/api/received-payments"] });
      
      toast({
        title: "Success",
        description: "Payment imported successfully",
      });
      
      setText("");
      onClose();
    } catch (e) {
      toast({
        title: "Error",
        description: "Invalid JSON or server error",
        variant: "destructive",
      });
      console.error(e);
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-20">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">
            Import Payment JSON
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
            disabled={isImporting}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-2">
            Paste a JSON payment object below and click Import.
          </p>
          <textarea
            className="w-full h-60 p-3 border border-gray-300 rounded-md text-sm font-mono"
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder='{"sender": "Company Name", "amount": 1000, "reference": "REF123", "recipient": "Your Company"}'
            disabled={isImporting}
          />
        </div>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={onClose}
            className="flex-1"
            disabled={isImporting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleImport}
            className="flex-1 bg-primary hover:bg-blue-600"
            disabled={isImporting}
          >
            {isImporting ? "Importing..." : "Import"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ImportPaymentModal;
import { formatSafeDate } from "@/lib/dateUtils";

/**
 * InvoiceCard Component
 * 
 * Displays a single invoice card in the Accounts Receivable interface.
 * Shows invoice reference, status, customer, amount, and due date in a standardized format.
 * Each card is clickable to view details and perform invoice-related actions.
 */

interface InvoiceCardProps {
  id: number;               // Unique invoice identifier
  reference: string;        // Invoice reference number
  status: string;           // Current invoice status (Open, Overdue, Paid, Reconciled)
  customer: string;         // Customer name associated with the invoice
  amount: number;           // Invoice amount
  dueDate: string | Date;   // Invoice due date
  onClick: (id: number) => void; // Handler for card click events
}

const InvoiceCard = ({
  id,
  reference,
  status,
  customer,
  amount,
  dueDate,
  onClick,
}: InvoiceCardProps) => {
  // Format currency amount with proper currency symbol and decimal places
  const formattedAmount = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);

  // Format due date in a user-friendly format (e.g., "Jan 01, 2025")
  const formattedDate = formatSafeDate(dueDate, "MMM dd, yyyy");

  // Base badge class styling
  let badgeClass = "badge inline-flex items-center text-xs font-medium px-2.5 py-0.5 rounded-full";
  
  // Apply status-specific styling to the badge
  switch (status) {
    case "Open":
      badgeClass += " bg-amber-50 text-amber-700"; // Amber for open invoices
      break;
    case "Overdue":
      badgeClass += " bg-red-50 text-red-700"; // Red for overdue invoices
      break;
    case "Paid":
      badgeClass += " bg-emerald-50 text-emerald-700"; // Green for paid invoices
      break;
    case "Remitted": // Legacy status name, maintained for backwards compatibility
      badgeClass += " bg-gray-100 text-gray-700"; // Gray for remitted/reconciled
      break;
    default:
      badgeClass += " bg-gray-100 text-gray-700"; // Default gray styling
  }

  // Change display of "Remitted" status to "Reconciled" in AR for invoices
  const displayStatus = status === "Remitted" ? "Reconciled" : status;
  
  return (
    <div 
      className="invoice-card bg-white rounded-lg shadow-card p-5 mb-3 cursor-pointer hover:shadow-card-hover border border-gray-100 transition-all duration-200 group" 
      onClick={() => onClick(id)}
    >
      {/* Invoice reference and status badge */}
      <div className="flex justify-between items-center mb-3">
        <span className="text-sm font-medium text-gray-900 group-hover:text-primary transition-colors">{reference}</span>
        <span className={badgeClass}>{displayStatus}</span>
      </div>
      
      {/* Customer name */}
      <div className="text-sm text-gray-600 mb-3">{customer}</div>
      
      {/* Invoice amount and due date */}
      <div className="flex justify-between items-center">
        <span className="font-semibold text-gray-900">{formattedAmount}</span>
        <div className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-md flex items-center">
          {/* Calendar icon */}
          <svg className="w-3 h-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
          </svg>
          {formattedDate}
        </div>
      </div>
    </div>
  );
};

export default InvoiceCard;

import { ReactNode, useEffect, useState, useRef, Children, isValidElement, cloneElement, ReactElement } from "react";

/**
 * KanbanBoard Component
 * 
 * A comprehensive Kanban board implementation for financial management interfaces.
 * Provides a column-based layout with filtering, searching, and detail panels.
 * Used in both Accounts Payable and Accounts Receivable views.
 */

/**
 * Define a type for card props to help with type checking
 * This captures all possible properties that might be used on cards throughout the application
 */
interface CardProps {
  id?: number;               // Unique identifier for the card
  status?: string;           // Current status (e.g., 'Approved', 'Paid', 'Reconciled')
  reference?: string;        // Reference number
  recipient?: string;        // Payment recipient (for payment cards)
  customer?: string;         // Customer name (for invoice cards)
  sender?: string;           // Payment sender (for received payment cards)
  amount?: number;           // Payment/invoice amount
  fileType?: string;         // Format of the source file
  dueDate?: string | Date;   // Due date for invoices
  createdAt?: string | Date; // Creation/receipt date
  invoiceId?: number | null; // Linked invoice ID for received payments
  onClick?: (id: number) => void; // Click handler
}

/**
 * Props for the KanbanColumn component
 */
interface KanbanColumnProps {
  title: string;         // Column title (e.g., "Approve", "Payments", "Invoices")
  count: number;         // Number of items in the column
  children: ReactNode;   // Column content (cards and other elements)
  columnId?: string;     // Unique identifier for the column (used for filtering and detail panels)
}

/**
 * KanbanColumn Component
 * 
 * Renders a single column in the Kanban board interface with filtering and search capabilities.
 * Each column can contain various cards (payments, invoices) and provides status-based filtering.
 */
export const KanbanColumn = ({ title, count, children, columnId }: KanbanColumnProps) => {
  // State for search functionality
  const [searchTerm, setSearchTerm] = useState('');
  
  // State for status filter checkboxes - all statuses are initially checked/visible
  const [statusFilters, setStatusFilters] = useState<Record<string, boolean>>({
    'Not Approved': true,
    'Approved': true, 
    'Remitted': true,      // Legacy status - retained for backwards compatibility
    'Paid': true,
    'Open': true,
    'Overdue': true,
    'Unlinked': true,
    'Linked': true,
    'Reconciled': true
  });

  /**
   * Returns the relevant statuses for the current column type
   * Different column types (approval, payment, invoice, etc.) can show different status types
   */
  const getAllPossibleStatuses = () => {
    // Define the valid statuses for each column type by column ID
    const columnStatuses: Record<string, string[]> = {
      // Accounts Payable columns
      'not-approved': ['Not Approved', 'Approved'],           // Approval column
      'approved': ['Approved', 'Paid', 'Reconciled'],         // Payment column
      'sent': ['Paid', 'Reconciled'],                         // Sent column
      'remittances': ['Reconciled'],                          // Reconciliation column
      
      // Accounts Receivable columns
      'invoices': ['Open', 'Overdue', 'Paid', 'Reconciled'],  // Invoices column
      'received-payments': ['Unlinked', 'Linked', 'Reconciled'], // Received payments column
    };
    
    // Return column-specific statuses if this column has a defined set
    if (columnId && columnStatuses[columnId]) {
      return columnStatuses[columnId];
    }
    
    // Default set of all statuses as fallback if column type is unknown
    return [
      'Not Approved', 'Approved', 'Paid', 'Reconciled', 
      'Open', 'Overdue', 'Unlinked', 'Linked'
    ];
  };

  // Get the list of status values that can appear in this column
  const availableStatuses = getAllPossibleStatuses();

  /**
   * Returns styling information (background and text colors) for a given status
   * Used to consistently style status badges throughout the application
   * @param status The status name to get styling for
   * @returns Object containing background and text color CSS classes
   */
  const getStatusStyles = (status: string): { bgClass: string, textClass: string } => {
    switch (status) {
      case "Approved":
        return { bgClass: "bg-emerald-50", textClass: "text-emerald-700" }; // Green for approved
      case "Not Approved":
        return { bgClass: "bg-amber-50", textClass: "text-amber-700" }; // Amber for not approved
      case "Remitted": // Legacy status - keeping for backward compatibility
        return { bgClass: "bg-gray-100", textClass: "text-gray-700" }; // Gray for remitted/reconciled
      case "Open":
        return { bgClass: "bg-amber-50", textClass: "text-amber-700" }; // Amber for open invoices
      case "Overdue":
        return { bgClass: "bg-red-50", textClass: "text-red-700" }; // Red for overdue invoices
      case "Paid":
        return { bgClass: "bg-blue-50", textClass: "text-blue-700" }; // Blue for paid items
      case "Unlinked":
        return { bgClass: "bg-amber-50", textClass: "text-amber-700" }; // Amber for unlinked payments
      case "Linked":
        return { bgClass: "bg-blue-50", textClass: "text-blue-700" }; // Blue for linked payments
      case "Reconciled":
        return { bgClass: "bg-gray-100", textClass: "text-gray-700" }; // Gray for reconciled items
      default:
        return { bgClass: "bg-gray-100", textClass: "text-gray-500" }; // Default gray fallback
    }
  };

  /**
   * Toggles the visibility of items with a specific status
   * @param status The status to toggle in the filter
   */
  const handleStatusFilterChange = (status: string) => {
    setStatusFilters(prev => ({
      ...prev,
      [status]: !prev[status] // Toggle the current value
    }));
  };

  /**
   * Filters the children based on search term and status filters
   * Handles both normal cards and cards that are animating between columns
   */
  const filteredChildren = Children.toArray(children).filter(child => {
    if (!isValidElement(child)) return false;
    
    // Special case: Always show transitioning items (those animating between columns)
    if (child.props.className && 
        (child.props.className.includes('slide-up-out') || 
         child.props.className.includes('slide-down-in'))) {
      return true;
    }
    
    // Special case: Always show empty column messages
    if (typeof child.type === 'string' && child.type === 'div' && 
        child.props.className?.includes('text-center')) {
      return true;
    }
    
    try {
      // Extract the card element from its wrapper if needed
      const cardElement = child.props.children;
      if (!isValidElement(cardElement)) return true; // If not a valid element, show it
      
      // Get the props from the card safely
      const cardProps = cardElement.props as any;
      if (!cardProps) return true; // If no props available, show it by default
      
      // Extract searchable fields from the card props
      const status = cardProps.status;
      const reference = cardProps.reference || '';
      const recipient = cardProps.recipient || '';
      const customer = cardProps.customer || '';
      const sender = cardProps.sender || '';
      
      // Apply status filtering - hide items with unchecked statuses
      if (status && statusFilters.hasOwnProperty(status) && !statusFilters[status]) {
        return false;
      }
      
      // Apply search term filtering across all text fields
      if (searchTerm && searchTerm.trim() !== '') {
        const term = searchTerm.toLowerCase();
        // Check each field for a match
        if (reference && reference.toLowerCase().includes(term)) return true;
        if (recipient && recipient.toLowerCase().includes(term)) return true;
        if (customer && customer.toLowerCase().includes(term)) return true;
        if (sender && sender.toLowerCase().includes(term)) return true;
        // No match found in any field
        return false;
      }
      
      // By default, show the item
      return true;
    } catch (error) {
      // Error safety net - if anything goes wrong in filtering, show the item
      return true;
    }
  });

  return (
    <div 
      className="bg-gray-50 rounded-xl p-5 flex flex-col h-[calc(100vh-180px)] border border-gray-100 shadow-sm"
      data-column-id={columnId}
    >
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-medium text-gray-800 text-sm tracking-tight">{title}</h3>
        <span className="bg-white text-gray-600 text-xs font-medium px-2.5 py-1 rounded-full border border-gray-100 shadow-sm">
          {count}
        </span>
      </div>
      
      {/* Search Input */}
      <div className="mb-3">
        <input
          type="text"
          placeholder="Search..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-3 py-1 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
        />
      </div>
      
      {/* Status Filters */}
      {availableStatuses.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-3">
          {availableStatuses.map(status => {
            const styles = getStatusStyles(status);
            const isActive = statusFilters[status];
            
            // Create badge class based on status and active state
            let badgeClass = "flex items-center cursor-pointer px-2 py-0.5 rounded-full text-xs border ";
            
            if (isActive) {
              badgeClass += `${styles.bgClass} ${styles.textClass} border-${styles.textClass.slice(5)}/30`;
            } else {
              badgeClass += "bg-gray-100 text-gray-500 border-gray-200";
            }
            
            return (
              <div 
                key={status} 
                className={badgeClass}
                onClick={() => handleStatusFilterChange(status)}
              >
                <input
                  type="checkbox"
                  checked={isActive}
                  onChange={() => handleStatusFilterChange(status)}
                  className="mr-1 h-3 w-3"
                />
                {status}
              </div>
            );
          })}
        </div>
      )}
      
      <div className="overflow-y-auto flex-grow pr-1 custom-scrollbar">
        {filteredChildren}
      </div>
    </div>
  );
};

/**
 * Props for the KanbanBoard component
 */
interface KanbanBoardProps {
  children: ReactNode;     // Child components (should be KanbanColumn elements)
  className?: string;      // Optional additional CSS classes
  activeDetailPanel?: {    // Information about the currently open detail panel
    columnId: string;      // ID of the column the panel relates to
    content: ReactNode;    // Panel content
  } | null;
}

/**
 * KanbanBoard Component
 * 
 * Main component that orchestrates the Kanban board layout and detail panel animations.
 * Arranges KanbanColumn components in a horizontal row with detail panels that slide out.
 */
const KanbanBoard = ({ children, className = "", activeDetailPanel }: KanbanBoardProps) => {
  // State for tracking panel animations and transitions
  const [prevColumnId, setPrevColumnId] = useState<string | null>(null); // Previously active column
  const [transitionKey, setTransitionKey] = useState<string>('initial'); // Key for animation tracking
  const isInitialMount = useRef(true); // Track first render
  
  /**
   * Effect to handle smooth detail panel animations when opening/closing
   * Ensures that the transition between different detail panels appears seamless
   */
  useEffect(() => {
    if (activeDetailPanel?.columnId) {
      // Panel is being opened or switched to a different column
      // Using timestamp in key ensures React creates a new component instance
      setTransitionKey(activeDetailPanel.columnId + "-" + Date.now());
      setPrevColumnId(activeDetailPanel.columnId);
    } else if (!activeDetailPanel && prevColumnId) {
      // Panel is being closed
      setPrevColumnId(null);
    }
  }, [activeDetailPanel?.columnId]);
  
  /**
   * Creates the board layout with columns and detail panels
   * Places each detail panel immediately after its associated column
   * @returns Array of React elements representing the columns and panels
   */
  const createGridLayout = () => {
    // Normalize children to array for consistent handling
    const childrenArray = Array.isArray(children) ? children : [children];
    const flexItems: ReactNode[] = [];
    
    // Set fixed dimensions
    const columnWidth = "340px"; // Fixed width for all columns and panels
    
    // Process each column and add detail panels where needed
    childrenArray.forEach((column, index) => {
      // Extract column ID from props
      const columnId = (column as any)?.props?.columnId;
      
      // Add the regular column with fixed width
      flexItems.push(
        <div 
          key={`col-${columnId || index}`} 
          className="flex-shrink-0"
          style={{ width: columnWidth }}
        >
          {column}
        </div>
      );
      
      // If this column has an active detail panel, insert it next in the layout
      if (activeDetailPanel && columnId === activeDetailPanel.columnId) {
        // Create a unique key for the panel to control animations
        const panelKey = `detail-panel-${columnId}-${transitionKey}`;
        
        // Add the detail panel with matching height and width
        flexItems.push(
          <div 
            key={panelKey}
            className="flex-shrink-0 bg-white rounded-xl flex flex-col h-[calc(100vh-180px)] shadow-md border border-gray-100 overflow-hidden"
            style={{ width: columnWidth }}
          >
            {activeDetailPanel.content}
          </div>
        );
      }
    });
    
    return flexItems;
  };
    
  // Render the horizontal container with columns and detail panels
  return (
    <div className={`flex flex-col md:flex-row kanban-container gap-6 ${className}`}>
      {createGridLayout()}
    </div>
  );
};

export default KanbanBoard;

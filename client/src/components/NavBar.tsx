import { Link, useLocation } from "wouter";
import proofpayLogo from "@assets/pclogo6.png";

const NavBar = () => {
  const [location] = useLocation();

  return (
    <header className="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-100 shadow-sm">
      <div className="container mx-auto px-6 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <img
            src={proofpayLogo}
            alt="Proofpay Logo"
            className="w-8 h-8 object-contain"
          />
          <h1 className="text-lg font-medium tracking-tight">Proofpay</h1>
        </div>
        <nav>
          <ul className="flex space-x-8">
            <li>
              <Link
                href="/"
                className={`px-1 py-2 font-medium text-sm ${
                  location === "/"
                    ? "text-primary border-b-2 border-primary"
                    : "text-gray-600 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-200 transition-colors duration-150"
                }`}
              >
                Home
              </Link>
            </li>
            <li>
              <Link
                href="/accounts-payable"
                className={`px-1 py-2 font-medium text-sm ${
                  location === "/accounts-payable"
                    ? "text-primary border-b-2 border-primary"
                    : "text-gray-600 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-200 transition-colors duration-150"
                }`}
              >
                Accounts Payable
              </Link>
            </li>
            <li>
              <Link
                href="/accounts-receivable"
                className={`px-1 py-2 font-medium text-sm ${
                  location === "/accounts-receivable"
                    ? "text-primary border-b-2 border-primary"
                    : "text-gray-600 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-200 transition-colors duration-150"
                }`}
              >
                Accounts Receivable
              </Link>
            </li>
            <li>
              <Link
                href="/admin"
                className={`px-1 py-2 font-medium text-sm ${
                  location === "/admin"
                    ? "text-primary border-b-2 border-primary"
                    : "text-gray-600 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-200 transition-colors duration-150"
                }`}
              >
                Admin
              </Link>
            </li>
          </ul>
        </nav>
      </div>
    </header>
  );
};

export default NavBar;

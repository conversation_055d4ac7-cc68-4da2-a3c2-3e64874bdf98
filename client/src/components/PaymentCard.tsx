/**
 * PaymentCard Component
 * 
 * Displays a single payment card with all relevant information in the Accounts Payable interface.
 * The card shows payment reference, status, recipient, amount and file type in a standardized format.
 * Each card is clickable to view details and perform payment-related actions.
 */

interface PaymentCardProps {
  id: number;         // Unique payment identifier
  reference: string;  // Payment reference number
  status: string;     // Current payment status (Not Approved, Approved, Paid, Reconciled, etc.)
  recipient: string;  // Payment recipient name
  amount: number;     // Payment amount
  fileType: string;   // Format of the imported payment file
  onClick: (id: number) => void; // Handler for card click events
}

const PaymentCard = ({
  id,
  reference,
  status,
  recipient,
  amount,
  fileType,
  onClick,
}: PaymentCardProps) => {
  // Format currency amount with proper currency symbol and decimal places
  const formattedAmount = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);

  // Base badge class styling
  let badgeClass = "badge inline-flex items-center text-xs font-medium px-2.5 py-0.5 rounded-full";
  
  // Apply status-specific styling to the badge
  switch (status) {
    case "Approved":
      badgeClass += " bg-emerald-50 text-emerald-700"; // Green for approved
      break;
    case "Not Approved":
      badgeClass += " bg-amber-50 text-amber-700"; // Amber for not approved
      break;
    case "Paid":
      badgeClass += " bg-blue-50 text-blue-700"; // Blue for paid
      break;
    case "Sent":
      badgeClass += " bg-blue-50 text-blue-700"; // Blue for sent
      break;
    case "Remitted": // Legacy status name, maintained for backwards compatibility
      badgeClass += " bg-gray-100 text-gray-700"; // Gray for remitted/reconciled
      break;
    default:
      badgeClass += " bg-gray-100 text-gray-700"; // Default gray styling
  }

  // Change display of "Remitted" status to "Reconciled" in AP for consistent terminology
  const displayStatus = status === "Remitted" ? "Reconciled" : status;
  
  return (
    <div 
      className="payment-card bg-white rounded-lg shadow-card p-5 mb-3 cursor-pointer hover:shadow-card-hover border border-gray-100 transition-all duration-200 group" 
      onClick={() => onClick(id)}
    >
      {/* Payment reference and status badge */}
      <div className="flex justify-between items-center mb-3">
        <span className="text-sm font-medium text-gray-900 group-hover:text-primary transition-colors">{reference}</span>
        <span className={badgeClass}>{displayStatus}</span>
      </div>
      
      {/* Payment recipient */}
      <div className="text-sm text-gray-600 mb-3">{recipient}</div>
      
      {/* Payment amount and file type */}
      <div className="flex justify-between items-center">
        <span className="font-semibold text-gray-900">{formattedAmount}</span>
        <span className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-md">{fileType}</span>
      </div>
    </div>
  );
};

export default PaymentCard;

import { useState, useEffect } from 'react';

interface ProcessingOverlayProps {
  isOpen: boolean;
  title: string;
  message: string;
  duration?: number; // In milliseconds
}

const ProcessingOverlay = ({ isOpen, title, message, duration = 5000 }: ProcessingOverlayProps) => {
  const [progress, setProgress] = useState(0);
  const [timeLeft, setTimeLeft] = useState(duration / 1000);
  
  useEffect(() => {
    if (!isOpen) {
      setProgress(0);
      setTimeLeft(duration / 1000);
      return;
    }
    
    const interval = 100; // Update every 100ms for smooth animation
    const steps = duration / interval;
    let currentStep = 0;
    
    const timer = setInterval(() => {
      currentStep++;
      const newProgress = (currentStep / steps) * 100;
      setProgress(newProgress);
      
      // Update time left (in seconds)
      const newTimeLeft = Math.max(0, Math.ceil((duration - currentStep * interval) / 1000));
      setTimeLeft(newTimeLeft);
      
      if (currentStep >= steps) {
        clearInterval(timer);
      }
    }, interval);
    
    return () => clearInterval(timer);
  }, [isOpen, duration]);
  
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-900 bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-6 w-full max-w-md shadow-xl border border-gray-100">
        <h3 className="text-xl font-semibold mb-4 text-center tracking-tight">{title}</h3>
        
        <div className="flex items-center justify-center mb-6">
          <div className="w-16 h-16 relative mr-4">
            <svg className="animate-spin w-16 h-16 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <div className="absolute inset-0 flex items-center justify-center font-bold text-lg">
              {timeLeft}s
            </div>
          </div>
          <div className="flex-1">
            <p className="text-gray-800 font-medium mb-2">{message}</p>
            <div className="relative pt-1">
              <div className="overflow-hidden h-2 text-xs flex rounded-full bg-gray-200">
                <div
                  className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-primary transition-all duration-100 ease-in-out rounded-full"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
        
        <p className="text-center text-gray-500 text-sm">Please wait for the blockchain transaction to complete...</p>
      </div>
    </div>
  );
};

export default ProcessingOverlay;

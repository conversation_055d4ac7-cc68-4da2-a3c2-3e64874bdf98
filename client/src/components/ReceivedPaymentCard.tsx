import { formatSafeDate } from "@/lib/dateUtils";

/**
 * ReceivedPaymentCard Component
 * 
 * Displays a single received payment card in the Accounts Receivable interface.
 * Shows payment reference, status, sender, amount, receipt date, and invoice linkage.
 * Each card is clickable to view details and perform payment-related actions.
 */

interface ReceivedPaymentCardProps {
  id: number;                 // Unique payment identifier
  reference: string;          // Payment reference number
  status: string;             // Current payment status (Unlinked, Linked, Reconciled)
  sender: string;             // Payment sender name
  amount: number;             // Payment amount
  createdAt: string | Date;   // Date payment was received
  invoiceId?: number | null;  // ID of linked invoice, if any
  onClick: (id: number) => void; // Handler for card click events
}

const ReceivedPaymentCard = ({
  id,
  reference,
  status,
  sender,
  amount,
  createdAt,
  invoiceId,
  onClick,
}: ReceivedPaymentCardProps) => {
  // Format currency amount with proper currency symbol and decimal places
  const formattedAmount = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);

  // Format received date in a user-friendly format (e.g., "Jan 01, 2025")
  const formattedDate = formatSafeDate(createdAt, "MMM dd, yyyy");

  // Base badge class styling
  let badgeClass = "badge inline-flex items-center text-xs font-medium px-2.5 py-0.5 rounded-full";
  
  // Apply status-specific styling to the badge
  switch (status) {
    case "Unlinked":
      badgeClass += " bg-amber-50 text-amber-700"; // Amber for unlinked payments
      break;
    case "Linked":
      badgeClass += " bg-blue-50 text-blue-700"; // Blue for linked payments
      break;
    case "Reconciled":
      badgeClass += " bg-emerald-50 text-emerald-700"; // Green for reconciled payments
      break;
    case "Remitted": // Legacy status name, maintained for backwards compatibility
      badgeClass += " bg-gray-100 text-gray-700"; // Gray for remitted/reconciled
      break;
    default:
      badgeClass += " bg-gray-100 text-gray-700"; // Default gray styling
  }

  // Determine if payment is linked to an invoice
  const isLinked = invoiceId !== null && invoiceId !== undefined;

  // Change display of "Remitted" status to "Reconciled" in AR for consistent terminology
  const displayStatus = status === "Remitted" ? "Reconciled" : status;
  
  return (
    <div 
      className="received-payment-card bg-white rounded-lg shadow-card p-5 mb-3 cursor-pointer hover:shadow-card-hover border border-gray-100 transition-all duration-200 group" 
      onClick={() => onClick(id)}
    >
      {/* Payment reference and status badge */}
      <div className="flex justify-between items-center mb-3">
        <span className="text-sm font-medium text-gray-900 group-hover:text-primary transition-colors">{reference}</span>
        <span className={badgeClass}>{displayStatus}</span>
      </div>
      
      {/* Payment sender */}
      <div className="text-sm text-gray-600 mb-3">{sender}</div>
      
      {/* Payment amount and receipt date */}
      <div className="flex justify-between items-center mb-2">
        <span className="font-semibold text-gray-900">{formattedAmount}</span>
        <div className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-md flex items-center">
          {/* Calendar icon */}
          <svg className="w-3 h-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
          </svg>
          {formattedDate}
        </div>
      </div>
      
      {/* Invoice linkage indicator - only shown when payment is linked to an invoice */}
      {isLinked && (
        <div className="mt-1 text-xs text-emerald-600 flex items-center bg-emerald-50 py-1 px-2 rounded-md w-fit">
          {/* Link icon */}
          <svg 
            className="w-3 h-3 mr-1" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth="2" 
              d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
            ></path>
          </svg>
          Linked to invoice
        </div>
      )}
    </div>
  );
};

export default ReceivedPaymentCard;

import { useState, useRef, FormEvent, useEffect } from "react";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { AlertCircle, Upload, FileText, CheckCircle2, X, File, AlertTriangle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface UploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  type: "payment" | "invoice";
}

interface FileWithError extends File {
  error?: string;
}

const UploadModal = ({ isOpen, onClose, onSuccess, type }: UploadModalProps) => {
  const { toast } = useToast();
  const [files, setFiles] = useState<FileWithError[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [uploadStatus, setUploadStatus] = useState<string>("Ready to upload");
  const [dragActive, setDragActive] = useState(false);
  const [fileErrors, setFileErrors] = useState<{[filename: string]: string}>({});
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Reset state when modal is opened/closed
  useEffect(() => {
    if (isOpen) {
      // Clear files and reset state when modal is opened
      setFiles([]);
      setUploadProgress(0);
      setUploadStatus("Ready to upload");
      setFileErrors({});
      setIsUploading(false);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const newFiles = Array.from(e.dataTransfer.files);
      setFiles(prev => [...prev, ...newFiles]);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);
      setFiles(prev => [...prev, ...newFiles]);
    }
  };

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const uploadSingleFile = async (file: FileWithError, index: number, totalFiles: number): Promise<boolean> => {
    setUploadStatus(`Uploading file ${index + 1} of ${totalFiles}: ${file.name}`);
    
    try {
      const formData = new FormData();
      formData.append("file", file);
      
      const endpoint = type === "payment" ? "/api/upload_payment" : "/api/upload_invoice";
      
      const response = await fetch(endpoint, {
        method: "POST",
        body: formData,
        credentials: "include",
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        const errorMessage = errorData.error || "Upload failed";
        
        // Update fileErrors state to track which files had errors
        setFileErrors(prev => ({
          ...prev,
          [file.name]: errorMessage
        }));
        
        // For duplicate reference errors, automatically remove the file after 2 seconds
        const isDuplicateError = errorMessage.includes("already exists");
        
        if (isDuplicateError) {
          // Show a toast notification for duplicate file
          toast({
            title: "Duplicate Detected",
            description: `${file.name} will be automatically removed (duplicate reference number)`,
            duration: 2000,
          });
          
          // Display the error briefly before removing the file
          setTimeout(() => {
            // Remove the duplicate file
            setFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
            
            // Also remove from fileErrors
            setFileErrors(prev => {
              const newErrors = {...prev};
              delete newErrors[file.name];
              return newErrors;
            });
          }, 2000);
        } else {
          // For other errors, just mark the file as having an error
          setFiles(prevFiles => {
            const newFiles = [...prevFiles];
            // Instead of creating a new File, just add the error property to the existing file
            const updatedFile = file;
            updatedFile.error = errorMessage;
            newFiles[index] = updatedFile;
            return newFiles;
          });
        }
        
        return false;
      }
      
      const result = await response.json();
      
      // Clear any error for this file
      setFileErrors(prev => {
        const newErrors = {...prev};
        delete newErrors[file.name];
        return newErrors;
      });
      
      setUploadProgress(((index + 1) / totalFiles) * 100);
      return true;
    } catch (error) {
      console.error(`Error uploading file ${file.name}:`, error);
      
      // Add the error to fileErrors state
      const errorMessage = error instanceof Error 
        ? error.message 
        : "An unknown error occurred during upload";
      
      // Check if this is a duplicate error (from previous detection)
      const isDuplicateError = 
        (error instanceof Error && error.message.includes("already exists"));
        
      setFileErrors(prev => ({
        ...prev,
        [file.name]: errorMessage
      }));
      
      if (isDuplicateError) {
        // Show a toast notification for duplicate file
        toast({
          title: "Duplicate Detected",
          description: `${file.name} will be automatically removed (duplicate reference number)`,
          duration: 2000,
        });
        
        // Display error briefly, then auto-remove
        setTimeout(() => {
          // Remove the duplicate file
          setFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
          
          // Also remove from fileErrors
          setFileErrors(prev => {
            const newErrors = {...prev};
            delete newErrors[file.name];
            return newErrors;
          });
        }, 2000);
      } else {
        // For other errors, just mark the file as having an error
        setFiles(prevFiles => {
          const newFiles = [...prevFiles];
          const updatedFile = file;
          updatedFile.error = errorMessage;
          newFiles[index] = updatedFile;
          return newFiles;
        });
      }
      
      return false;
    }
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    if (files.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one file to upload",
        variant: "destructive",
      });
      return;
    }
    
    setIsUploading(true);
    setUploadProgress(0);
    setUploadStatus("Starting upload...");
    setFileErrors({});
    
    try {
      let successCount = 0;
      let hasErrors = false;
      
      // Upload files one by one
      for (let i = 0; i < files.length; i++) {
        const success = await uploadSingleFile(files[i], i, files.length);
        if (success) {
          successCount++;
        } else {
          hasErrors = true;
        }
      }
      
      // Invalidate the appropriate query to refresh data
      if (type === "payment") {
        queryClient.invalidateQueries({ queryKey: ["/api/payments"] });
      } else {
        queryClient.invalidateQueries({ queryKey: ["/api/invoices"] });
      }
      
      // If we have any errors, don't close the modal
      if (hasErrors) {
        setIsUploading(false);
        
        toast({
          title: "Upload Partially Complete",
          description: `Successfully uploaded ${successCount} of ${files.length} ${type} files. Some files had errors.`,
          variant: "destructive",
        });
        
        // After 5 seconds, clear file errors but keep the files with errors
        setTimeout(() => {
          setFileErrors({});
        }, 5000);
      } else {
        // Only close if all files were uploaded successfully
        toast({
          title: "Upload Complete",
          description: `Successfully uploaded ${successCount} of ${files.length} ${type} files.`,
        });
        
        onSuccess();
        onClose();
      }
    } catch (error) {
      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
      setIsUploading(false);
      setUploadProgress(0);
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleDownloadSample = async () => {
    try {
      // Default format for download sample
      const defaultFormat = type === "payment" ? "PEXR2002" : "EDI X12";
      const endpoint = type === "payment" 
        ? `/api/sample_payment/${defaultFormat}` 
        : `/api/sample_invoice/${defaultFormat}`;
      
      window.open(endpoint, '_blank');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to download sample file",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-20">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">
            Import {type === "payment" ? "Payment" : "Invoice"} Files
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
            disabled={isUploading}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {Object.keys(fileErrors).length > 0 && (
            <Alert variant="destructive" className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Upload Issues Detected</AlertTitle>
              <AlertDescription>
                <div className="mt-2 text-sm space-y-1 max-h-32 overflow-y-auto">
                  {Object.entries(fileErrors).map(([filename, error]) => (
                    <div key={filename} className="flex items-start">
                      <span className="font-semibold">{filename}:</span>
                      <span className="ml-1">{error}</span>
                    </div>
                  ))}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {isUploading && (
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-blue-500 border-blue-200 bg-blue-50">
                <AlertCircle className="w-3.5 h-3.5 mr-1" />
                {uploadStatus}
              </Badge>
            </div>
          )}

          {isUploading && (
            <div className="space-y-2">
              <div className="flex justify-between text-xs text-gray-500">
                <span>Importing...</span>
                <span>{Math.round(uploadProgress)}%</span>
              </div>
              <Progress value={uploadProgress} className="h-2" />
            </div>
          )}

          <div
            className={`border-2 border-dashed ${
              dragActive ? "border-primary" : "border-gray-300"
            } rounded-lg p-6 text-center`}
            onDragEnter={handleDrag}
            onDragOver={handleDrag}
            onDragLeave={handleDrag}
            onDrop={handleDrop}
            onClick={() => !isUploading && fileInputRef.current?.click()}
          >
            {files.length > 0 ? (
              <div className="space-y-3">
                <CheckCircle2 className="mx-auto h-8 w-8 text-green-500 mb-2" />
                <div className="text-sm text-gray-600 mb-2">
                  {files.length} file{files.length !== 1 ? 's' : ''} selected
                </div>
                <div className="max-h-40 overflow-y-auto space-y-2">
                  {files.map((file, index) => (
                    <div 
                      key={index} 
                      className={`flex items-center p-2 rounded text-sm ${
                        file.error ? 'bg-red-50 border border-red-100' : 'bg-gray-50'
                      }`}
                    >
                      {file.error ? (
                        <AlertTriangle className="h-4 w-4 mr-2 text-red-500" />
                      ) : (
                        <File className="h-4 w-4 mr-2 text-blue-500" />
                      )}
                      <span className={`truncate flex-1 ${file.error ? 'text-red-800' : ''}`}>
                        {file.name}
                      </span>
                      <span className="text-gray-400 text-xs ml-2">
                        ({Math.round(file.size / 1024)} KB)
                      </span>
                      <button 
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeFile(index);
                        }}
                        className="ml-2 text-gray-400 hover:text-gray-600"
                        disabled={isUploading}
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <>
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-1 text-sm text-gray-600">
                  Drag and drop your files here, or{" "}
                  <span className="text-primary">browse</span>
                </p>
                <p className="mt-1 text-xs text-gray-500">
                  You can select multiple files for upload
                </p>
              </>
            )}
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              className="hidden"
              disabled={isUploading}
              multiple
              accept=".txt,.xml,.json,.csv"
            />
          </div>

          <div className="flex space-x-2">
            <Button
              type="submit"
              className="flex-1 bg-primary hover:bg-blue-600"
              disabled={files.length === 0 || isUploading}
            >
              {isUploading ? "Importing..." : "Import Files"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UploadModal;

import React from "react";
import { Member, ApprovalPredicate as ApprovalPredicateType } from "@/types/admin";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { CheckCircle, Crown } from "lucide-react";

interface ApprovalMatrixProps {
  members?: Member[];
  predicate?: ApprovalPredicateType;
  isLoading: boolean;
}

export function ApprovalMatrix({ members = [], predicate, isLoading }: ApprovalMatrixProps) {
  // Helper function to check if a member is in the approval predicate
  const isInPredicate = (member: Member): string | null => {
    if (!predicate) return null;

    if (predicate.tier1 === member.email) return "Tier 1 Approver";
    if (predicate.tier2 === member.email) return "Tier 2 Approver";
    if (predicate.accountOwner === member.email) return "Account Owner";

    return null;
  };

  // Helper function to get role access
  const getRoleAccess = (role: string) => {
    switch (role) {
      case "Approver":
        return { ap: true, ar: true };
      case "Accounts Payable":
        return { ap: true, ar: false };
      case "Accounts Receivable":
        return { ap: false, ar: true };
      default:
        return { ap: false, ar: false };
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div>
          <Skeleton className="h-6 w-48 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="border rounded-lg">
          <div className="p-4">
            <div className="space-y-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-48" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Approval Matrix</h3>
        <p className="text-sm text-muted-foreground">
          Overview of all workspace members and their granted roles and permissions.
        </p>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Member</TableHead>
              <TableHead>Approver Tier</TableHead>
              <TableHead>AP Access</TableHead>
              <TableHead>AR Access</TableHead>
              <TableHead>Work-Statement Filters</TableHead>
              <TableHead>Rule Source</TableHead>
              <TableHead>Step Seq</TableHead>
              <TableHead>Approval Mode</TableHead>
              <TableHead>Predicate Role</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {members.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                  No members found. Add team members to see the approval matrix.
                </TableCell>
              </TableRow>
            ) : (
              members
                .filter(member => member.status !== "Disabled")
                .map((member) => {
                  const predicateRole = isInPredicate(member);
                  const access = getRoleAccess(member.role);

                  return (
                    <TableRow
                      key={member.id}
                      className={predicateRole ? "bg-muted/50" : ""}
                    >
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          {predicateRole && (
                            <Crown className="h-4 w-4 text-amber-500" />
                          )}
                          <div>
                            <div>{member.firstName} {member.lastName}</div>
                            <div className="text-sm text-muted-foreground">{member.email}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {member.role === "Approver" && member.tier ? (
                          <Badge variant="outline">{member.tier}</Badge>
                        ) : (
                          <span className="text-muted-foreground">—</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {access.ap ? (
                          <div className="flex items-center gap-1 text-green-600">
                            <CheckCircle className="h-4 w-4" />
                            <span className="text-sm">Yes</span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">—</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {access.ar ? (
                          <div className="flex items-center gap-1 text-green-600">
                            <CheckCircle className="h-4 w-4" />
                            <span className="text-sm">Yes</span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">—</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {member.filters.length > 0 ? (
                            member.filters.map((filter, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {filter}
                              </Badge>
                            ))
                          ) : (
                            <span className="text-muted-foreground">None</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-xs">
                          Legacy Predicate
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="text-muted-foreground">—</span>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-xs">
                          SERIAL
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {predicateRole ? (
                          <Badge variant="default" className="bg-amber-100 text-amber-800 border-amber-200">
                            {predicateRole}
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground">—</span>
                        )}
                      </TableCell>
                    </TableRow>
                  );
                })
            )}
          </TableBody>
        </Table>
      </div>

      {predicate && (
        <div className="bg-muted/30 rounded-lg p-4">
          <h4 className="font-medium mb-2">Current Approval Predicate</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Tier 1 Approver:</span>
              <div className="font-medium">{predicate.tier1 || "Not assigned"}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Tier 2 Approver:</span>
              <div className="font-medium">{predicate.tier2 || "Not assigned"}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Account Owner:</span>
              <div className="font-medium">{predicate.accountOwner || "Not assigned"}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

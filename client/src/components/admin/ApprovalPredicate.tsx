import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Member, ApprovalPredicate as ApprovalPredicateType, validateApprovalPredicate } from "@/types/admin";
import { useAdminData } from "@/hooks/useAdminData";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, CheckCircle } from "lucide-react";

const approvalPredicateSchema = z.object({
  tier1: z.string().min(1, "Tier 1 approver is required"),
  tier2: z.string().min(1, "Tier 2 approver is required"),
  accountOwner: z.string().min(1, "Account owner is required"),
}).refine((data) => {
  // Ensure all three roles are assigned to different people
  const values = [data.tier1, data.tier2, data.accountOwner];
  return new Set(values).size === values.length;
}, {
  message: "All roles must be assigned to different people",
  path: ["tier1"], // This will show the error on the first field
});

interface ApprovalPredicateProps {
  predicate?: ApprovalPredicateType;
  members?: Member[];
  isLoading: boolean;
}

export function ApprovalPredicate({ predicate, members = [], isLoading }: ApprovalPredicateProps) {
  const { updateApprovalPredicate } = useAdminData();

  const form = useForm<ApprovalPredicateType>({
    resolver: zodResolver(approvalPredicateSchema),
    defaultValues: predicate || {
      tier1: "",
      tier2: "",
      accountOwner: "",
    },
  });

  // Update form when predicate data changes
  React.useEffect(() => {
    if (predicate) {
      form.reset(predicate);
    }
  }, [predicate, form]);

  const onSubmit = async (data: ApprovalPredicateType) => {
    try {
      await updateApprovalPredicate(data);
    } catch (error) {
      console.error("Failed to update approval predicate:", error);
    }
  };

  // Filter members by role
  const tier1Approvers = members.filter(m => m.role === "Approver" && m.tier === "Tier 1" && m.status === "Active");
  const tier2Approvers = members.filter(m => m.role === "Approver" && m.tier === "Tier 2" && m.status === "Active");
  const accountOwners = members.filter(m => (m.role === "Accounts Payable" || m.role === "Accounts Receivable") && m.status === "Active");

  const currentValues = form.watch();
  const isValid = validateApprovalPredicate(currentValues);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div>
          <Skeleton className="h-6 w-48 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-10 w-full" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Organization-Level Approval Predicate</h3>
        <p className="text-sm text-muted-foreground">
          Define the approval hierarchy for your organization. Each role must be assigned to a different person.
        </p>
      </div>

      {!isValid && currentValues.tier1 && currentValues.tier2 && currentValues.accountOwner && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            All three roles must be assigned to different people. Please select unique members for each role.
          </AlertDescription>
        </Alert>
      )}

      {isValid && currentValues.tier1 && currentValues.tier2 && currentValues.accountOwner && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            Approval predicate is valid. All roles are assigned to different members.
          </AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <FormField
              control={form.control}
              name="tier1"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tier 1 Approver</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select Tier 1 approver" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {tier1Approvers.length === 0 ? (
                        <div className="px-2 py-1.5 text-sm text-muted-foreground">
                          No Tier 1 approvers available
                        </div>
                      ) : (
                        tier1Approvers.map((member) => (
                          <SelectItem key={member.id} value={member.email}>
                            {member.firstName} {member.lastName} ({member.email})
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tier2"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tier 2 Approver</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select Tier 2 approver" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {tier2Approvers.length === 0 ? (
                        <div className="px-2 py-1.5 text-sm text-muted-foreground">
                          No Tier 2 approvers available
                        </div>
                      ) : (
                        tier2Approvers.map((member) => (
                          <SelectItem key={member.id} value={member.email}>
                            {member.firstName} {member.lastName} ({member.email})
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="accountOwner"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Account Owner</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select account owner" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {accountOwners.length === 0 ? (
                        <div className="px-2 py-1.5 text-sm text-muted-foreground">
                          No account owners available
                        </div>
                      ) : (
                        accountOwners.map((member) => (
                          <SelectItem key={member.id} value={member.email}>
                            {member.firstName} {member.lastName} ({member.email})
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex justify-end">
            <Button type="submit" disabled={!isValid}>
              Save Approval Predicate
            </Button>
          </div>
        </form>
      </Form>

      {members.length === 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            No members found. Please add team members first before setting up the approval predicate.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

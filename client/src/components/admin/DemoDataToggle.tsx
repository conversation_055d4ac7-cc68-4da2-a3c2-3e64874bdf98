import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Database, Trash2, Info } from "lucide-react";

interface DemoDataToggleProps {
  onSeed: () => Promise<void>;
  onReset: () => Promise<void>;
}

export function DemoDataToggle({ onSeed, onReset }: DemoDataToggleProps) {
  const [isSeeding, setIsSeeding] = useState(false);
  const [isResetting, setIsResetting] = useState(false);

  const handleSeed = async () => {
    setIsSeeding(true);
    try {
      await onSeed();
    } catch (error) {
      console.error("Failed to seed demo data:", error);
    } finally {
      setIsSeeding(false);
    }
  };

  const handleReset = async () => {
    setIsResetting(true);
    try {
      await onReset();
    } catch (error) {
      console.error("Failed to reset data:", error);
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Demo Data Management</h3>
        <p className="text-sm text-muted-foreground">
          Populate or reset the admin page with sample data for testing and demonstration purposes.
        </p>
      </div>

      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Development Feature:</strong> This section is only available in development mode. 
          Use it to quickly populate the admin interface with sample organization data, members, and approval settings.
        </AlertDescription>
      </Alert>

      <div className="flex flex-col sm:flex-row gap-4">
        <Button
          onClick={handleSeed}
          disabled={isSeeding}
          className="flex items-center gap-2"
        >
          <Database className="h-4 w-4" />
          {isSeeding ? "Loading Demo Data..." : "Populate Demo Data"}
        </Button>

        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              variant="destructive"
              disabled={isResetting}
              className="flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              Reset All Data
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action will permanently delete all organization data, members, and approval settings. 
                This cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleReset}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {isResetting ? "Resetting..." : "Reset All Data"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      <div className="bg-muted/30 rounded-lg p-4">
        <h4 className="font-medium mb-2">Demo Data Includes:</h4>
        <ul className="text-sm text-muted-foreground space-y-1">
          <li>• Organization: Global Logistics Ltd. with complete profile</li>
          <li>• 4 Team members with different roles (Approvers, AP, AR)</li>
          <li>• Configured approval predicate with Tier 1/2 approvers</li>
          <li>• Work-statement filters for customer account segregation</li>
          <li>• Sample ERP integration settings</li>
        </ul>
      </div>
    </div>
  );
}

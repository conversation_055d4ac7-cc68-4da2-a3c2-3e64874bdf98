import React from "react";
import { Organization, Member, ApprovalPredicate as ApprovalPredicateType } from "@/types/admin";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, SheetTitle } from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Copy, ExternalLink, Code, Webhook, Database, Settings } from "lucide-react";
import { toast } from "@/components/ui/use-toast";

interface ERPIntegrationSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  organization?: Organization;
  predicate?: ApprovalPredicateType;
  members?: Member[];
}

export function ERPIntegrationSidebar({
  isOpen,
  onClose,
  organization,
  predicate,
  members = [],
}: ERPIntegrationSidebarProps) {
  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied to clipboard",
      description: `${label} copied successfully.`,
    });
  };

  const apiEndpoints = [
    {
      method: "POST",
      path: "/api/v1/payments",
      description: "Create a new payment request",
      icon: <Webhook className="h-4 w-4" />,
    },
    {
      method: "GET",
      path: "/api/v1/invoices/:id",
      description: "Retrieve invoice status",
      icon: <Database className="h-4 w-4" />,
    },
    {
      method: "POST",
      path: "/api/v1/invoices/status",
      description: "Update invoice status webhook",
      icon: <Webhook className="h-4 w-4" />,
    },
    {
      method: "GET",
      path: "/api/v1/balances",
      description: "Polling endpoint for account balances",
      icon: <Database className="h-4 w-4" />,
    },
  ];

  const samplePayload = {
    payment: {
      reference: "PAY-2024-001",
      amount: 1500.00,
      currency: organization?.fiscalCurrency || "USD",
      vendor: "Acme Corp",
      description: "Professional services",
      approvalTier: "Tier 1",
      workStatement: "Boeing",
      requestedBy: predicate?.accountOwner || "<EMAIL>",
    },
    approvalFlow: {
      tier1Approver: predicate?.tier1 || "<EMAIL>",
      tier2Approver: predicate?.tier2 || "<EMAIL>",
      accountOwner: predicate?.accountOwner || "<EMAIL>",
    },
    memberRoles: members.map(member => ({
      email: member.email,
      role: member.role,
      tier: member.tier,
      filters: member.filters,
    })),
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <Code className="h-5 w-5" />
            ERP Integration Reference
          </SheetTitle>
        </SheetHeader>

        <ScrollArea className="h-[calc(100vh-100px)] mt-6">
          <div className="space-y-6">
            {/* API Endpoints Section */}
            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <Settings className="h-4 w-4" />
                API Endpoints
              </h3>
              <div className="space-y-3">
                {apiEndpoints.map((endpoint, index) => (
                  <div key={index} className="border rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {endpoint.icon}
                        <Badge variant={endpoint.method === "GET" ? "secondary" : "default"}>
                          {endpoint.method}
                        </Badge>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(endpoint.path, "Endpoint")}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                    <code className="text-sm bg-muted px-2 py-1 rounded block mb-2">
                      {endpoint.path}
                    </code>
                    <p className="text-xs text-muted-foreground">
                      {endpoint.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Base URL Section */}
            {organization?.erpBaseUrl && (
              <div>
                <h3 className="font-semibold mb-3">Base URL</h3>
                <div className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">ERP Instance</span>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(organization.erpBaseUrl, "Base URL")}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.open(organization.erpBaseUrl, "_blank")}
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <code className="text-sm bg-muted px-2 py-1 rounded block">
                    {organization.erpBaseUrl}
                  </code>
                </div>
              </div>
            )}

            <Separator />

            {/* Sample Payload Section */}
            <div>
              <h3 className="font-semibold mb-3">Sample Request Payload</h3>
              <div className="border rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Payment Creation</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(JSON.stringify(samplePayload, null, 2), "Sample payload")}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
                <pre className="text-xs bg-muted p-3 rounded overflow-x-auto">
                  {JSON.stringify(samplePayload, null, 2)}
                </pre>
              </div>
            </div>

            <Separator />

            {/* Current Configuration */}
            <div>
              <h3 className="font-semibold mb-3">Current Configuration</h3>
              <div className="space-y-3">
                <div className="border rounded-lg p-3">
                  <h4 className="text-sm font-medium mb-2">Organization</h4>
                  <div className="text-xs space-y-1">
                    <div><span className="text-muted-foreground">Name:</span> {organization?.displayName || "Not set"}</div>
                    <div><span className="text-muted-foreground">Domain:</span> {organization?.emailDomain || "Not set"}</div>
                    <div><span className="text-muted-foreground">Currency:</span> {organization?.fiscalCurrency || "Not set"}</div>
                  </div>
                </div>

                <div className="border rounded-lg p-3">
                  <h4 className="text-sm font-medium mb-2">Approval Predicate</h4>
                  <div className="text-xs space-y-1">
                    <div><span className="text-muted-foreground">Tier 1:</span> {predicate?.tier1 || "Not assigned"}</div>
                    <div><span className="text-muted-foreground">Tier 2:</span> {predicate?.tier2 || "Not assigned"}</div>
                    <div><span className="text-muted-foreground">Owner:</span> {predicate?.accountOwner || "Not assigned"}</div>
                  </div>
                </div>

                <div className="border rounded-lg p-3">
                  <h4 className="text-sm font-medium mb-2">Active Members</h4>
                  <div className="text-xs">
                    {members.filter(m => m.status === "Active").length} active members
                  </div>
                </div>
              </div>
            </div>

            {/* Authentication Note */}
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
              <h4 className="text-sm font-medium text-amber-800 mb-1">Authentication</h4>
              <p className="text-xs text-amber-700">
                All API requests require authentication using the ERP API key configured in organization settings.
                Include the key in the Authorization header: <code>Bearer {organization?.erpApiKey ? "***" : "not-set"}</code>
              </p>
            </div>
          </div>
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
}

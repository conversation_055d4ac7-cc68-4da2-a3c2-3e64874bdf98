import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { ERPImportResult, CANONICAL_FIELDS, CanonicalField } from "@/types/workflow";
import { ArrowRight, CheckCircle, AlertTriangle, Save } from "lucide-react";

interface MappingWizardModalProps {
  isOpen: boolean;
  onClose: () => void;
  importResult: ERPImportResult | null;
  onComplete: () => void;
}

export function MappingWizardModal({ isOpen, onClose, importResult, onComplete }: MappingWizardModalProps) {
  const [mappings, setMappings] = useState<Record<string, string>>({});
  const [mappingName, setMappingName] = useState("");
  const [step, setStep] = useState(1);

  React.useEffect(() => {
    if (importResult) {
      setMappings(importResult.fieldMappings);
      setMappingName(`${importResult.vendor} Mapping - ${new Date().toLocaleDateString()}`);
    }
  }, [importResult]);

  if (!importResult) return null;

  const vendorFields = Object.keys(importResult.fieldMappings);
  const unmappedFields = vendorFields.filter(field => !mappings[field]);
  const mappedFields = vendorFields.filter(field => mappings[field]);

  const handleMappingChange = (vendorField: string, canonicalField: string) => {
    setMappings(prev => ({
      ...prev,
      [vendorField]: canonicalField
    }));
  };

  const handleRemoveMapping = (vendorField: string) => {
    setMappings(prev => {
      const newMappings = { ...prev };
      delete newMappings[vendorField];
      return newMappings;
    });
  };

  const handleSave = () => {
    // In a real app, this would save the mapping to the backend
    console.log("Saving mapping:", { name: mappingName, mappings });
    onComplete();
  };

  const getFieldDescription = (canonicalField: CanonicalField) => {
    return CANONICAL_FIELDS[canonicalField];
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Field Mapping Wizard</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress Steps */}
          <div className="flex items-center space-x-4">
            <div className={`flex items-center space-x-2 ${step >= 1 ? 'text-primary' : 'text-muted-foreground'}`}>
              <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                step >= 1 ? 'bg-primary text-primary-foreground' : 'bg-muted'
              }`}>
                1
              </div>
              <span className="text-sm">Review Import</span>
            </div>
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
            <div className={`flex items-center space-x-2 ${step >= 2 ? 'text-primary' : 'text-muted-foreground'}`}>
              <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                step >= 2 ? 'bg-primary text-primary-foreground' : 'bg-muted'
              }`}>
                2
              </div>
              <span className="text-sm">Map Fields</span>
            </div>
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
            <div className={`flex items-center space-x-2 ${step >= 3 ? 'text-primary' : 'text-muted-foreground'}`}>
              <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                step >= 3 ? 'bg-primary text-primary-foreground' : 'bg-muted'
              }`}>
                3
              </div>
              <span className="text-sm">Save & Complete</span>
            </div>
          </div>

          {/* Step 1: Review Import */}
          {step === 1 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Import Summary</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>ERP Vendor</Label>
                  <div className="font-medium">{importResult.vendor}</div>
                </div>
                <div>
                  <Label>File Format</Label>
                  <div className="font-medium">{importResult.format}</div>
                </div>
                <div>
                  <Label>Workflows Found</Label>
                  <div className="font-medium">{importResult.workflows.length}</div>
                </div>
                <div>
                  <Label>Fields to Map</Label>
                  <div className="font-medium">{vendorFields.length}</div>
                </div>
              </div>

              {importResult.workflows.length > 0 && (
                <div>
                  <Label>Imported Workflows</Label>
                  <div className="space-y-2 mt-2">
                    {importResult.workflows.map((workflow, index) => (
                      <div key={index} className="p-3 border rounded-lg">
                        <div className="font-medium">{workflow.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {workflow.steps?.length || 0} steps • {workflow.documentType}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {importResult.warnings.length > 0 && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="font-medium mb-1">Warnings:</div>
                    <ul className="list-disc list-inside space-y-1">
                      {importResult.warnings.map((warning, index) => (
                        <li key={index} className="text-sm">{warning}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}

              <div className="flex justify-end">
                <Button onClick={() => setStep(2)}>
                  Continue to Field Mapping
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          )}

          {/* Step 2: Map Fields */}
          {step === 2 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Field Mapping</h3>
              <p className="text-sm text-muted-foreground">
                Map {importResult.vendor} fields to canonical fields used by the workflow engine.
              </p>

              {/* Mapping Progress */}
              <div className="flex items-center space-x-4 p-3 bg-muted/30 rounded-lg">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Mapped: {mappedFields.length}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4 text-amber-500" />
                  <span className="text-sm">Unmapped: {unmappedFields.length}</span>
                </div>
              </div>

              {/* Field Mappings */}
              <div className="space-y-3 max-h-[400px] overflow-y-auto">
                {vendorFields.map((vendorField) => (
                  <div key={vendorField} className="flex items-center space-x-4 p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium">{vendorField}</div>
                      <div className="text-sm text-muted-foreground">
                        {importResult.vendor} field
                      </div>
                    </div>
                    
                    <ArrowRight className="h-4 w-4 text-muted-foreground" />
                    
                    <div className="flex-1">
                      <Select
                        value={mappings[vendorField] || ""}
                        onValueChange={(value) => handleMappingChange(vendorField, value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select canonical field" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">No mapping</SelectItem>
                          {Object.entries(CANONICAL_FIELDS).map(([field, description]) => (
                            <SelectItem key={field} value={field}>
                              <div>
                                <div className="font-medium">{field}</div>
                                <div className="text-xs text-muted-foreground">{description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {mappings[vendorField] && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleRemoveMapping(vendorField)}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                ))}
              </div>

              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep(1)}>
                  Back
                </Button>
                <Button onClick={() => setStep(3)}>
                  Continue to Save
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          )}

          {/* Step 3: Save & Complete */}
          {step === 3 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Save Mapping</h3>
              
              <div>
                <Label htmlFor="mappingName">Mapping Name</Label>
                <Input
                  id="mappingName"
                  value={mappingName}
                  onChange={(e) => setMappingName(e.target.value)}
                  placeholder="Enter a name for this field mapping"
                />
              </div>

              {/* Mapping Summary */}
              <div className="space-y-3">
                <h4 className="font-medium">Mapping Summary</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Vendor:</span> {importResult.vendor}
                  </div>
                  <div>
                    <span className="text-muted-foreground">Total Fields:</span> {vendorFields.length}
                  </div>
                  <div>
                    <span className="text-muted-foreground">Mapped Fields:</span> {mappedFields.length}
                  </div>
                  <div>
                    <span className="text-muted-foreground">Unmapped Fields:</span> {unmappedFields.length}
                  </div>
                </div>

                {mappedFields.length > 0 && (
                  <div>
                    <h5 className="font-medium mb-2">Mapped Fields</h5>
                    <div className="space-y-1">
                      {mappedFields.map((field) => (
                        <div key={field} className="flex items-center justify-between text-sm p-2 bg-green-50 rounded">
                          <span className="font-medium">{field}</span>
                          <ArrowRight className="h-3 w-3 text-muted-foreground" />
                          <span>{mappings[field]}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {unmappedFields.length > 0 && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      {unmappedFields.length} field(s) will not be mapped: {unmappedFields.join(", ")}
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep(2)}>
                  Back to Mapping
                </Button>
                <Button onClick={handleSave}>
                  <Save className="h-4 w-4 mr-2" />
                  Save & Complete
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

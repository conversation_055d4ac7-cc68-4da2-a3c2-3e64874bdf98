import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Member, Organization, Role, ApproverTier, validateEmail } from "@/types/admin";
import { useAdminData } from "@/hooks/useAdminData";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ChipInput } from "@/components/ui/chip-input";
import { Plus, Edit, UserX, Mail, MoreHorizontal } from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

const memberSchema = z.object({
  email: z.string().email("Invalid email format"),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  role: z.enum(["Approver", "Accounts Payable", "Accounts Receivable"]),
  tier: z.enum(["Tier 1", "Tier 2"]).optional(),
  filters: z.array(z.string()).default([]),
});

interface MemberManagementProps {
  members?: Member[];
  organization?: Organization;
  isLoading: boolean;
}

export function MemberManagement({ members = [], organization, isLoading }: MemberManagementProps) {
  const { createMember, updateMember, disableMember } = useAdminData();
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [editingMember, setEditingMember] = useState<Member | null>(null);

  const form = useForm<Omit<Member, "id" | "status">>({
    resolver: zodResolver(memberSchema),
    defaultValues: {
      email: "",
      firstName: "",
      lastName: "",
      role: "Accounts Payable",
      filters: [],
    },
  });

  const watchedRole = form.watch("role");

  const onSubmit = async (data: Omit<Member, "id" | "status">) => {
    // Validate email domain
    if (organization?.emailDomain && !validateEmail(data.email, organization.emailDomain)) {
      form.setError("email", {
        message: `Email must be from domain: ${organization.emailDomain}`,
      });
      return;
    }

    try {
      if (editingMember) {
        await updateMember(editingMember.id, data);
        setEditingMember(null);
      } else {
        await createMember({ ...data, status: "Pending" });
      }
      form.reset();
      setIsInviteModalOpen(false);
    } catch (error) {
      console.error("Failed to save member:", error);
    }
  };

  const handleEdit = (member: Member) => {
    setEditingMember(member);
    form.reset({
      email: member.email,
      firstName: member.firstName,
      lastName: member.lastName,
      role: member.role,
      tier: member.tier,
      filters: member.filters,
    });
    setIsInviteModalOpen(true);
  };

  const handleDisable = async (memberId: string) => {
    try {
      await disableMember(memberId);
    } catch (error) {
      console.error("Failed to disable member:", error);
    }
  };

  const handleResendInvite = async (memberId: string) => {
    // This would call the resend invite API
    console.log("Resending invite for member:", memberId);
  };

  const closeModal = () => {
    setIsInviteModalOpen(false);
    setEditingMember(null);
    form.reset();
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-6 w-48 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="space-y-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="flex items-center space-x-4">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-3 w-32" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Member Management</h3>
          <p className="text-sm text-muted-foreground">
            Invite and manage team members with different roles and permissions.
          </p>
        </div>
        <Dialog open={isInviteModalOpen} onOpenChange={setIsInviteModalOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setIsInviteModalOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Invite Member
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingMember ? "Edit Member" : "Invite New Member"}
              </DialogTitle>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>First Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Last Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input {...field} type="email" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          value={field.value}
                          className="flex flex-col space-y-2"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="Approver" id="approver" />
                            <Label htmlFor="approver">Approver</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="Accounts Payable" id="ap" />
                            <Label htmlFor="ap">Accounts Payable</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="Accounts Receivable" id="ar" />
                            <Label htmlFor="ar">Accounts Receivable</Label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {watchedRole === "Approver" && (
                  <FormField
                    control={form.control}
                    name="tier"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tier</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select tier" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Tier 1">Tier 1</SelectItem>
                            <SelectItem value="Tier 2">Tier 2</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="filters"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Work-Statement Filter</FormLabel>
                      <FormControl>
                        <ChipInput
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Enter customer account names..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={closeModal}>
                    Cancel
                  </Button>
                  <Button type="submit">
                    {editingMember ? "Update" : "Invite"}
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Tier</TableHead>
              <TableHead>Filters</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {members.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                  No members found. Invite your first team member to get started.
                </TableCell>
              </TableRow>
            ) : (
              members.map((member) => (
                <TableRow key={member.id}>
                  <TableCell className="font-medium">
                    {member.firstName} {member.lastName}
                  </TableCell>
                  <TableCell>{member.email}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{member.role}</Badge>
                  </TableCell>
                  <TableCell>
                    {member.tier && (
                      <Badge variant="secondary">{member.tier}</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {member.filters.map((filter, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {filter}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        member.status === "Active"
                          ? "default"
                          : member.status === "Pending"
                          ? "secondary"
                          : "destructive"
                      }
                    >
                      {member.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEdit(member)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        {member.status === "Pending" && (
                          <DropdownMenuItem onClick={() => handleResendInvite(member.id)}>
                            <Mail className="h-4 w-4 mr-2" />
                            Resend Invite
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem
                          onClick={() => handleDisable(member.id)}
                          className="text-destructive"
                        >
                          <UserX className="h-4 w-4 mr-2" />
                          Disable
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

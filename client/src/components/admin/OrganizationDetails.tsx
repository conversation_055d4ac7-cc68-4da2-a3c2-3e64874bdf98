import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Organization } from "@/types/admin";
import { useAdminData } from "@/hooks/useAdminData";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Skeleton } from "@/components/ui/skeleton";
import { currencies } from "@/lib/currencies";
import { industries } from "@/lib/industries";

const organizationSchema = z.object({
  legalName: z.string().min(1, "Legal name is required"),
  displayName: z.string().min(1, "Display name is required"),
  accountNumber: z.string().min(1, "Account number is required"),
  emailDomain: z.string().min(1, "Email domain is required").regex(/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, "Invalid domain format"),
  industry: z.string().min(1, "Industry is required"),
  fiscalCurrency: z.string().min(1, "Fiscal currency is required"),
  erpBaseUrl: z.string().url("Invalid URL format").optional().or(z.literal("")),
  erpApiKey: z.string().optional(),
});

interface OrganizationDetailsProps {
  organization?: Organization;
  isLoading: boolean;
}

export function OrganizationDetails({ organization, isLoading }: OrganizationDetailsProps) {
  const { updateOrganization } = useAdminData();
  const [isEditing, setIsEditing] = useState(false);

  const form = useForm<Organization>({
    resolver: zodResolver(organizationSchema),
    defaultValues: organization || {
      legalName: "",
      displayName: "",
      accountNumber: "",
      emailDomain: "",
      industry: "",
      fiscalCurrency: "USD",
      erpBaseUrl: "",
      erpApiKey: "",
    },
  });

  // Update form when organization data changes
  React.useEffect(() => {
    if (organization) {
      form.reset(organization);
    }
  }, [organization, form]);

  const onSubmit = async (data: Organization) => {
    try {
      await updateOrganization(data);
      setIsEditing(false);
    } catch (error) {
      console.error("Failed to update organization:", error);
    }
  };

  const handleReset = () => {
    form.reset(organization);
    setIsEditing(false);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-6 w-48 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
          <Skeleton className="h-10 w-20" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-10 w-full" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Organization Details</h3>
          <p className="text-sm text-muted-foreground">
            Manage your organization's basic information and ERP integration settings.
          </p>
        </div>
        {!isEditing ? (
          <Button onClick={() => setIsEditing(true)} variant="outline">
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button onClick={handleReset} variant="outline" size="sm">
              Cancel
            </Button>
            <Button onClick={form.handleSubmit(onSubmit)} size="sm">
              Save
            </Button>
          </div>
        )}
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="legalName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Legal Name</FormLabel>
                  <FormControl>
                    <Input {...field} disabled={!isEditing} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="displayName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Display Name</FormLabel>
                  <FormControl>
                    <Input {...field} disabled={!isEditing} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="accountNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Account Number</FormLabel>
                  <FormControl>
                    <Input {...field} disabled={!isEditing} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="emailDomain"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Primary Email Domain</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="company.com" disabled={!isEditing} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="industry"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Industry</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value} disabled={!isEditing}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select industry" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {industries.map((industry) => (
                        <SelectItem key={industry} value={industry}>
                          {industry}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="fiscalCurrency"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Fiscal Currency</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value} disabled={!isEditing}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select currency" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {currencies.map((currency) => (
                        <SelectItem key={currency.code} value={currency.code}>
                          {currency.code} - {currency.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="erpBaseUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>ERP Instance Base URL</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="https://erp.company.com" disabled={!isEditing} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="erpApiKey"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>ERP API Key</FormLabel>
                  <FormControl>
                    <Input {...field} type="password" placeholder="Enter API key" disabled={!isEditing} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </form>
      </Form>
    </div>
  );
}
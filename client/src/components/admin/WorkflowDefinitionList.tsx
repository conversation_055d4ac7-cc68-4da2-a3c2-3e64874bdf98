import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { WorkflowDefinition, DocumentType, WorkflowStatus } from "@/types/workflow";
import { Search, Plus, Edit, Trash2, Play, Pause, Copy } from "lucide-react";

interface WorkflowDefinitionListProps {
  onSelectWorkflow: (workflow: WorkflowDefinition | null) => void;
  selectedWorkflow: WorkflowDefinition | null;
}

// Mock data for demonstration
const mockWorkflows: WorkflowDefinition[] = [
  {
    id: "wf-001",
    name: "Invoice Approval - Standard",
    description: "Standard invoice approval workflow for amounts under $10,000",
    version: "1.2.0",
    documentType: "INVOICE",
    status: "ACTIVE",
    steps: [
      {
        id: "step-1",
        sequence: 1,
        name: "Manager Approval",
        predicate: {
          type: "comparison",
          field: "amount",
          operator: "lt",
          value: 10000
        },
        approverRoleIds: ["manager"],
        approvalMode: "SERIAL",
        timeoutHours: 24
      }
    ],
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-20T14:30:00Z",
    createdBy: "admin",
    source: {
      vendor: "SAP",
      importedAt: "2024-01-15T10:00:00Z",
      originalFormat: "json"
    }
  },
  {
    id: "wf-002",
    name: "High-Value Invoice Approval",
    description: "Multi-tier approval for invoices over $10,000",
    version: "1.0.0",
    documentType: "INVOICE",
    status: "ACTIVE",
    steps: [
      {
        id: "step-1",
        sequence: 1,
        name: "Manager Approval",
        predicate: {
          type: "comparison",
          field: "amount",
          operator: "gte",
          value: 10000
        },
        approverRoleIds: ["manager"],
        approvalMode: "SERIAL",
        timeoutHours: 24
      },
      {
        id: "step-2",
        sequence: 2,
        name: "Director Approval",
        predicate: {
          type: "comparison",
          field: "amount",
          operator: "gte",
          value: 50000
        },
        approverRoleIds: ["director"],
        approvalMode: "SERIAL",
        timeoutHours: 48
      }
    ],
    createdAt: "2024-01-10T09:00:00Z",
    updatedAt: "2024-01-10T09:00:00Z",
    createdBy: "admin"
  },
  {
    id: "wf-003",
    name: "Payment Authorization",
    description: "Payment approval workflow with dual authorization",
    version: "2.1.0",
    documentType: "PAYMENT",
    status: "DRAFT",
    steps: [
      {
        id: "step-1",
        sequence: 1,
        name: "Dual Authorization",
        predicate: {
          type: "comparison",
          field: "amount",
          operator: "gt",
          value: 0
        },
        approverRoleIds: ["approver-1", "approver-2"],
        approvalMode: "PARALLEL",
        timeoutHours: 12
      }
    ],
    createdAt: "2024-01-25T16:00:00Z",
    updatedAt: "2024-01-26T11:00:00Z",
    createdBy: "admin",
    source: {
      vendor: "Oracle",
      importedAt: "2024-01-25T16:00:00Z",
      originalFormat: "xlsx"
    }
  }
];

export function WorkflowDefinitionList({ onSelectWorkflow, selectedWorkflow }: WorkflowDefinitionListProps) {
  const [workflows] = useState<WorkflowDefinition[]>(mockWorkflows);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<WorkflowStatus | "ALL">("ALL");
  const [documentTypeFilter, setDocumentTypeFilter] = useState<DocumentType | "ALL">("ALL");

  const filteredWorkflows = workflows.filter(workflow => {
    const matchesSearch = workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         workflow.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "ALL" || workflow.status === statusFilter;
    const matchesDocType = documentTypeFilter === "ALL" || workflow.documentType === documentTypeFilter;
    
    return matchesSearch && matchesStatus && matchesDocType;
  });

  const getStatusBadge = (status: WorkflowStatus) => {
    const variants = {
      ACTIVE: "default",
      INACTIVE: "secondary",
      DRAFT: "outline"
    } as const;

    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  const getDocumentTypeBadge = (type: DocumentType) => {
    const colors = {
      INVOICE: "bg-blue-100 text-blue-800",
      PAYMENT: "bg-green-100 text-green-800",
      PURCHASE_ORDER: "bg-purple-100 text-purple-800",
      EXPENSE_REPORT: "bg-orange-100 text-orange-800"
    };

    return (
      <Badge variant="outline" className={colors[type]}>
        {type.replace('_', ' ')}
      </Badge>
    );
  };

  const handleToggleStatus = (workflow: WorkflowDefinition) => {
    // In a real app, this would call an API to toggle the workflow status
    console.log(`Toggle status for workflow: ${workflow.id}`);
  };

  const handleDuplicate = (workflow: WorkflowDefinition) => {
    // In a real app, this would create a copy of the workflow
    console.log(`Duplicate workflow: ${workflow.id}`);
  };

  const handleDelete = (workflow: WorkflowDefinition) => {
    // In a real app, this would delete the workflow after confirmation
    console.log(`Delete workflow: ${workflow.id}`);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h4 className="font-semibold">Workflow Definitions</h4>
          <p className="text-sm text-muted-foreground">
            Manage approval workflows imported from ERP systems or created manually.
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Create Workflow
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search workflows..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as WorkflowStatus | "ALL")}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALL">All Status</SelectItem>
            <SelectItem value="ACTIVE">Active</SelectItem>
            <SelectItem value="INACTIVE">Inactive</SelectItem>
            <SelectItem value="DRAFT">Draft</SelectItem>
          </SelectContent>
        </Select>

        <Select value={documentTypeFilter} onValueChange={(value) => setDocumentTypeFilter(value as DocumentType | "ALL")}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Document Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALL">All Types</SelectItem>
            <SelectItem value="INVOICE">Invoice</SelectItem>
            <SelectItem value="PAYMENT">Payment</SelectItem>
            <SelectItem value="PURCHASE_ORDER">Purchase Order</SelectItem>
            <SelectItem value="EXPENSE_REPORT">Expense Report</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Workflow Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Version</TableHead>
              <TableHead>Steps</TableHead>
              <TableHead>Source</TableHead>
              <TableHead>Updated</TableHead>
              <TableHead className="w-[120px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredWorkflows.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                  No workflows found. Import from your ERP system or create a new workflow.
                </TableCell>
              </TableRow>
            ) : (
              filteredWorkflows.map((workflow) => (
                <TableRow 
                  key={workflow.id}
                  className={`cursor-pointer hover:bg-muted/50 ${
                    selectedWorkflow?.id === workflow.id ? "bg-muted" : ""
                  }`}
                  onClick={() => onSelectWorkflow(workflow)}
                >
                  <TableCell>
                    <div>
                      <div className="font-medium">{workflow.name}</div>
                      {workflow.description && (
                        <div className="text-sm text-muted-foreground truncate max-w-[200px]">
                          {workflow.description}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {getDocumentTypeBadge(workflow.documentType)}
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(workflow.status)}
                  </TableCell>
                  <TableCell className="font-mono text-sm">
                    {workflow.version}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {workflow.steps.length} step{workflow.steps.length !== 1 ? 's' : ''}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {workflow.source ? (
                      <div className="text-sm">
                        <div className="font-medium">{workflow.source.vendor}</div>
                        <div className="text-muted-foreground">{workflow.source.originalFormat}</div>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">Manual</span>
                    )}
                  </TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {new Date(workflow.updatedAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleStatus(workflow);
                        }}
                      >
                        {workflow.status === "ACTIVE" ? (
                          <Pause className="h-4 w-4" />
                        ) : (
                          <Play className="h-4 w-4" />
                        )}
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDuplicate(workflow);
                        }}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(workflow);
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Selected Workflow Details */}
      {selectedWorkflow && (
        <div className="border rounded-lg p-4 bg-muted/30">
          <h5 className="font-medium mb-2">Workflow Details</h5>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">ID:</span> {selectedWorkflow.id}
            </div>
            <div>
              <span className="text-muted-foreground">Created:</span> {new Date(selectedWorkflow.createdAt).toLocaleString()}
            </div>
            <div>
              <span className="text-muted-foreground">Created By:</span> {selectedWorkflow.createdBy}
            </div>
            <div>
              <span className="text-muted-foreground">Steps:</span> {selectedWorkflow.steps.length}
            </div>
          </div>
          
          {selectedWorkflow.steps.length > 0 && (
            <div className="mt-4">
              <h6 className="font-medium mb-2">Workflow Steps</h6>
              <div className="space-y-2">
                {selectedWorkflow.steps.map((step) => (
                  <div key={step.id} className="flex items-center justify-between p-2 bg-background rounded border">
                    <div>
                      <span className="font-medium">{step.sequence}. {step.name}</span>
                      <div className="text-xs text-muted-foreground">
                        {step.approverRoleIds.length} approver(s) • {step.approvalMode} • {step.timeoutHours}h timeout
                      </div>
                    </div>
                    <Badge variant="outline">{step.approvalMode}</Badge>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

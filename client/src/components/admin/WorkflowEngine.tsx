import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { WorkflowImportConnector } from "./WorkflowImportConnector";
import { WorkflowUploadZone } from "./WorkflowUploadZone";
import { WorkflowVersionHistory } from "./WorkflowVersionHistory";
import { WorkflowSimulationSandbox } from "./WorkflowSimulationSandbox";
import { WorkflowDefinitionList } from "./WorkflowDefinitionList";
import { MappingWizardModal } from "./MappingWizardModal";
import { WorkflowDefinition, ERPImportResult } from "@/types/workflow";

interface WorkflowEngineProps {
  isLoading: boolean;
}

export function WorkflowEngine({ isLoading }: WorkflowEngineProps) {
  const [mappingWizardOpen, setMappingWizardOpen] = useState(false);
  const [importResult, setImportResult] = useState<ERPImportResult | null>(null);
  const [selectedWorkflow, setSelectedWorkflow] = useState<WorkflowDefinition | null>(null);

  const handleImportComplete = (result: ERPImportResult) => {
    setImportResult(result);
    if (result.workflows.length > 0 && Object.keys(result.fieldMappings).length > 0) {
      setMappingWizardOpen(true);
    }
  };

  const handleMappingComplete = () => {
    setMappingWizardOpen(false);
    setImportResult(null);
    // Refresh workflow list
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3 mb-6"></div>
          <div className="space-y-4">
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Workflow Engine</h3>
        <p className="text-sm text-muted-foreground">
          Import and manage approval workflows from SAP, Oracle, Dynamics 365, and NetSuite.
          Replace static approval predicates with dynamic, rule-based workflows.
        </p>
      </div>

      <Tabs defaultValue="definitions" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="definitions">Workflow Definitions</TabsTrigger>
          <TabsTrigger value="import">Import & Connectors</TabsTrigger>
          <TabsTrigger value="simulation">Simulation Sandbox</TabsTrigger>
          <TabsTrigger value="versions">Version History</TabsTrigger>
        </TabsList>

        <TabsContent value="definitions" className="space-y-6">
          <Card className="p-6">
            <WorkflowDefinitionList 
              onSelectWorkflow={setSelectedWorkflow}
              selectedWorkflow={selectedWorkflow}
            />
          </Card>
        </TabsContent>

        <TabsContent value="import" className="space-y-6">
          <Card className="p-6">
            <WorkflowImportConnector />
          </Card>
          
          <Card className="p-6">
            <WorkflowUploadZone onImportComplete={handleImportComplete} />
          </Card>
        </TabsContent>

        <TabsContent value="simulation" className="space-y-6">
          <Card className="p-6">
            <WorkflowSimulationSandbox 
              workflows={[]} // Will be populated from API
            />
          </Card>
        </TabsContent>

        <TabsContent value="versions" className="space-y-6">
          <Card className="p-6">
            <WorkflowVersionHistory 
              selectedWorkflow={selectedWorkflow}
            />
          </Card>
        </TabsContent>
      </Tabs>

      {/* Mapping Wizard Modal */}
      <MappingWizardModal
        isOpen={mappingWizardOpen}
        onClose={() => setMappingWizardOpen(false)}
        importResult={importResult}
        onComplete={handleMappingComplete}
      />
    </div>
  );
}

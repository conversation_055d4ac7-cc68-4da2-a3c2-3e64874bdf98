import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ERPVendor, ImportConnector } from "@/types/workflow";
import { Plus, Settings, RefreshCw, AlertTriangle, CheckCircle } from "lucide-react";

// Mock data for demonstration
const mockConnectors: ImportConnector[] = [
  {
    id: "conn-001",
    name: "SAP Production",
    vendor: "SAP",
    type: "API",
    config: {
      baseUrl: "https://sap-prod.company.com",
      apiKey: "***",
      pollIntervalMinutes: 60,
      enabled: true
    },
    lastSync: "2024-01-26T10:30:00Z",
    status: "ACTIVE"
  },
  {
    id: "conn-002", 
    name: "Oracle Fusion",
    vendor: "Oracle",
    type: "API",
    config: {
      baseUrl: "https://oracle-fusion.company.com",
      username: "workflow_user",
      password: "***",
      pollIntervalMinutes: 120,
      enabled: false
    },
    status: "DISABLED"
  }
];

export function WorkflowImportConnector() {
  const [connectors] = useState<ImportConnector[]>(mockConnectors);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newConnector, setNewConnector] = useState({
    name: "",
    vendor: "" as ERPVendor | "",
    type: "API" as "API" | "FILE",
    baseUrl: "",
    apiKey: "",
    username: "",
    password: "",
    pollIntervalMinutes: 60,
    enabled: true
  });

  const handleAddConnector = () => {
    // In a real app, this would call an API to create the connector
    console.log("Adding connector:", newConnector);
    setShowAddForm(false);
    setNewConnector({
      name: "",
      vendor: "",
      type: "API",
      baseUrl: "",
      apiKey: "",
      username: "",
      password: "",
      pollIntervalMinutes: 60,
      enabled: true
    });
  };

  const handleTestConnection = (connector: ImportConnector) => {
    // In a real app, this would test the connection
    console.log("Testing connection for:", connector.id);
  };

  const handleSyncNow = (connector: ImportConnector) => {
    // In a real app, this would trigger an immediate sync
    console.log("Syncing now for:", connector.id);
  };

  const getStatusBadge = (status: ImportConnector["status"]) => {
    const variants = {
      ACTIVE: "default",
      ERROR: "destructive", 
      DISABLED: "secondary"
    } as const;

    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  const getStatusIcon = (status: ImportConnector["status"]) => {
    switch (status) {
      case "ACTIVE":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "ERROR":
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case "DISABLED":
        return <div className="h-4 w-4 rounded-full bg-gray-300" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h4 className="font-semibold">Import Connectors</h4>
          <p className="text-sm text-muted-foreground">
            Configure automated imports from ERP systems via API or scheduled file uploads.
          </p>
        </div>
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Connector
        </Button>
      </div>

      {/* Add Connector Form */}
      {showAddForm && (
        <Card className="p-6">
          <h5 className="font-medium mb-4">Add New Connector</h5>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Connector Name</Label>
              <Input
                id="name"
                value={newConnector.name}
                onChange={(e) => setNewConnector(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., SAP Production"
              />
            </div>

            <div>
              <Label htmlFor="vendor">ERP Vendor</Label>
              <Select value={newConnector.vendor} onValueChange={(value) => setNewConnector(prev => ({ ...prev, vendor: value as ERPVendor }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select vendor" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="SAP">SAP</SelectItem>
                  <SelectItem value="Oracle">Oracle Fusion</SelectItem>
                  <SelectItem value="Dynamics365">Microsoft Dynamics 365</SelectItem>
                  <SelectItem value="NetSuite">NetSuite</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="baseUrl">Base URL</Label>
              <Input
                id="baseUrl"
                value={newConnector.baseUrl}
                onChange={(e) => setNewConnector(prev => ({ ...prev, baseUrl: e.target.value }))}
                placeholder="https://your-erp-system.com"
              />
            </div>

            <div>
              <Label htmlFor="apiKey">API Key</Label>
              <Input
                id="apiKey"
                type="password"
                value={newConnector.apiKey}
                onChange={(e) => setNewConnector(prev => ({ ...prev, apiKey: e.target.value }))}
                placeholder="Enter API key"
              />
            </div>

            <div>
              <Label htmlFor="pollInterval">Poll Interval (minutes)</Label>
              <Input
                id="pollInterval"
                type="number"
                value={newConnector.pollIntervalMinutes}
                onChange={(e) => setNewConnector(prev => ({ ...prev, pollIntervalMinutes: parseInt(e.target.value) }))}
                min="15"
                max="1440"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="enabled"
                checked={newConnector.enabled}
                onCheckedChange={(checked) => setNewConnector(prev => ({ ...prev, enabled: checked }))}
              />
              <Label htmlFor="enabled">Enable connector</Label>
            </div>
          </div>

          <div className="flex justify-end space-x-2 mt-4">
            <Button variant="outline" onClick={() => setShowAddForm(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddConnector}>
              Add Connector
            </Button>
          </div>
        </Card>
      )}

      {/* Existing Connectors */}
      <div className="space-y-4">
        {connectors.length === 0 ? (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              No import connectors configured. Add a connector to enable automated workflow imports.
            </AlertDescription>
          </Alert>
        ) : (
          connectors.map((connector) => (
            <Card key={connector.id} className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(connector.status)}
                  <div>
                    <h6 className="font-medium">{connector.name}</h6>
                    <p className="text-sm text-muted-foreground">
                      {connector.vendor} • {connector.type} • 
                      {connector.config.pollIntervalMinutes && ` ${connector.config.pollIntervalMinutes}min intervals`}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {getStatusBadge(connector.status)}
                  
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleTestConnection(connector)}
                  >
                    <Settings className="h-4 w-4 mr-1" />
                    Test
                  </Button>
                  
                  {connector.status === "ACTIVE" && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleSyncNow(connector)}
                    >
                      <RefreshCw className="h-4 w-4 mr-1" />
                      Sync Now
                    </Button>
                  )}
                </div>
              </div>

              {connector.lastSync && (
                <div className="mt-2 text-xs text-muted-foreground">
                  Last sync: {new Date(connector.lastSync).toLocaleString()}
                </div>
              )}

              {connector.errorMessage && (
                <Alert variant="destructive" className="mt-2">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{connector.errorMessage}</AlertDescription>
                </Alert>
              )}

              <div className="mt-3 grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Base URL:</span>
                  <div className="font-mono text-xs truncate">{connector.config.baseUrl}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Status:</span>
                  <div>{connector.config.enabled ? "Enabled" : "Disabled"}</div>
                </div>
              </div>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}

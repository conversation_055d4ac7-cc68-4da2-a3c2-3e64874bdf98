import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { WorkflowDefinition, DocumentContext, SimulationResponse } from "@/types/workflow";
import { workflowEngine } from "@/services/workflowEngine";
import { Play, AlertTriangle, CheckCircle, Clock, Users } from "lucide-react";

interface WorkflowSimulationSandboxProps {
  workflows: WorkflowDefinition[];
}

const sampleDocuments = {
  lowValueInvoice: {
    id: "INV-001",
    type: "INVOICE" as const,
    amount: 2500,
    currency: "USD",
    supplier: "Acme Corp",
    costCenter: "IT",
    department: "Technology",
    country: "US",
    urgency: "MEDIUM" as const,
    riskScore: 0.2,
    metadata: {
      invoiceDate: "2024-01-26",
      dueDate: "2024-02-25",
      category: "Software License"
    }
  },
  highValueInvoice: {
    id: "INV-002", 
    type: "INVOICE" as const,
    amount: 75000,
    currency: "USD",
    supplier: "Enterprise Solutions Inc",
    costCenter: "CAPEX",
    department: "Operations",
    country: "US",
    urgency: "HIGH" as const,
    riskScore: 0.8,
    metadata: {
      invoiceDate: "2024-01-26",
      dueDate: "2024-02-15",
      category: "Equipment Purchase"
    }
  },
  foreignInvoice: {
    id: "INV-003",
    type: "INVOICE" as const,
    amount: 150000,
    currency: "EUR",
    supplier: "European Vendor GmbH",
    costCenter: "SALES",
    department: "Sales",
    country: "DE",
    urgency: "CRITICAL" as const,
    riskScore: 0.6,
    metadata: {
      invoiceDate: "2024-01-26",
      dueDate: "2024-02-10",
      category: "Professional Services"
    }
  }
};

export function WorkflowSimulationSandbox({ workflows }: WorkflowSimulationSandboxProps) {
  const [selectedWorkflow, setSelectedWorkflow] = useState<string>("");
  const [documentJson, setDocumentJson] = useState<string>(
    JSON.stringify(sampleDocuments.lowValueInvoice, null, 2)
  );
  const [simulationResult, setSimulationResult] = useState<SimulationResponse | null>(null);
  const [isSimulating, setIsSimulating] = useState(false);
  const [error, setError] = useState<string>("");

  // Mock workflows for demonstration
  const mockWorkflows: WorkflowDefinition[] = [
    {
      id: "wf-001",
      name: "Standard Invoice Approval",
      description: "Standard approval workflow for invoices",
      version: "1.0.0",
      documentType: "INVOICE",
      status: "ACTIVE",
      steps: [
        {
          id: "step-1",
          sequence: 1,
          name: "Manager Approval",
          predicate: {
            type: "comparison",
            field: "amount",
            operator: "lt",
            value: 10000
          },
          approverRoleIds: ["manager"],
          approvalMode: "SERIAL",
          timeoutHours: 24
        },
        {
          id: "step-2", 
          sequence: 2,
          name: "Director Approval",
          predicate: {
            type: "comparison",
            field: "amount",
            operator: "gte",
            value: 10000
          },
          approverRoleIds: ["director"],
          approvalMode: "SERIAL",
          timeoutHours: 48
        }
      ],
      createdAt: "2024-01-15T10:00:00Z",
      updatedAt: "2024-01-20T14:30:00Z",
      createdBy: "admin"
    }
  ];

  const allWorkflows = [...workflows, ...mockWorkflows];

  const handleSimulate = async () => {
    if (!selectedWorkflow) {
      setError("Please select a workflow");
      return;
    }

    try {
      setIsSimulating(true);
      setError("");
      
      const document: DocumentContext = JSON.parse(documentJson);
      const workflow = allWorkflows.find(w => w.id === selectedWorkflow);
      
      if (!workflow) {
        throw new Error("Workflow not found");
      }

      // Simulate the workflow evaluation
      const result = await workflowEngine.evaluate(workflow, document);
      
      // Create mock simulation response
      const simulationResponse: SimulationResponse = {
        result,
        executionPath: workflow.steps.map(step => ({
          stepId: step.id,
          stepName: step.name,
          matched: true, // Simplified for demo
          reason: `Amount ${document.amount} matches step criteria`
        })),
        warnings: []
      };

      setSimulationResult(simulationResponse);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Simulation failed");
    } finally {
      setIsSimulating(false);
    }
  };

  const loadSampleDocument = (sample: keyof typeof sampleDocuments) => {
    setDocumentJson(JSON.stringify(sampleDocuments[sample], null, 2));
    setSimulationResult(null);
    setError("");
  };

  const getApprovalModeIcon = (mode: "SERIAL" | "PARALLEL") => {
    return mode === "SERIAL" ? "→" : "⇉";
  };

  return (
    <div className="space-y-6">
      <div>
        <h4 className="font-semibold">Simulation Sandbox</h4>
        <p className="text-sm text-muted-foreground">
          Test workflow evaluation with sample documents to verify approval routing and timing.
        </p>
      </div>

      {/* Sample Documents */}
      <div>
        <h5 className="font-medium mb-2">Sample Documents</h5>
        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadSampleDocument("lowValueInvoice")}
          >
            Low Value Invoice ($2,500)
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadSampleDocument("highValueInvoice")}
          >
            High Value Invoice ($75,000)
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadSampleDocument("foreignInvoice")}
          >
            Foreign Invoice (€150,000)
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input Section */}
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium mb-2 block">Select Workflow</label>
            <Select value={selectedWorkflow} onValueChange={setSelectedWorkflow}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a workflow to test" />
              </SelectTrigger>
              <SelectContent>
                {allWorkflows.map((workflow) => (
                  <SelectItem key={workflow.id} value={workflow.id}>
                    {workflow.name} (v{workflow.version})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Document JSON</label>
            <Textarea
              value={documentJson}
              onChange={(e) => setDocumentJson(e.target.value)}
              placeholder="Paste or edit document JSON..."
              className="font-mono text-sm min-h-[300px]"
            />
          </div>

          <Button 
            onClick={handleSimulate} 
            disabled={isSimulating || !selectedWorkflow}
            className="w-full"
          >
            {isSimulating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                Simulating...
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Run Simulation
              </>
            )}
          </Button>

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        {/* Results Section */}
        <div className="space-y-4">
          <h5 className="font-medium">Simulation Results</h5>
          
          {!simulationResult ? (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center text-gray-500">
              <Play className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Run a simulation to see the approval routing results</p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Evaluation Summary */}
              <Card className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <h6 className="font-medium">Evaluation Summary</h6>
                  <Badge variant={simulationResult.result.autoApproved ? "default" : "secondary"}>
                    {simulationResult.result.autoApproved ? "Auto-Approved" : "Requires Approval"}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Evaluation Time:</span>
                    <div>{simulationResult.result.evaluationTimeMs}ms</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Approval Steps:</span>
                    <div>{simulationResult.result.approvers.length}</div>
                  </div>
                  {simulationResult.result.estimatedCompletionTime && (
                    <div className="col-span-2">
                      <span className="text-muted-foreground">Est. Completion:</span>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        {simulationResult.result.estimatedCompletionTime} hours
                      </div>
                    </div>
                  )}
                </div>
              </Card>

              {/* Approval Steps */}
              {simulationResult.result.approvers.length > 0 && (
                <Card className="p-4">
                  <h6 className="font-medium mb-3">Approval Steps</h6>
                  <div className="space-y-3">
                    {simulationResult.result.approvers.map((step, index) => (
                      <div key={step.stepId} className="flex items-center justify-between p-3 bg-muted/30 rounded">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center justify-center w-6 h-6 bg-primary text-primary-foreground rounded-full text-xs font-medium">
                            {step.sequence}
                          </div>
                          <div>
                            <div className="font-medium">Step {step.sequence}</div>
                            <div className="text-sm text-muted-foreground">
                              {step.approverUserIds.length} approver(s)
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">
                            {getApprovalModeIcon(step.approvalMode)} {step.approvalMode}
                          </Badge>
                          <div className="text-xs text-muted-foreground">
                            {step.timeoutAt && `${Math.round((new Date(step.timeoutAt).getTime() - Date.now()) / (1000 * 60 * 60))}h timeout`}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              )}

              {/* Execution Path */}
              <Card className="p-4">
                <h6 className="font-medium mb-3">Execution Path</h6>
                <div className="space-y-2">
                  {simulationResult.executionPath.map((path, index) => (
                    <div key={path.stepId} className="flex items-center space-x-3 text-sm">
                      {path.matched ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <div className="h-4 w-4 rounded-full border-2 border-gray-300" />
                      )}
                      <div className="flex-1">
                        <span className="font-medium">{path.stepName}</span>
                        {path.reason && (
                          <div className="text-muted-foreground">{path.reason}</div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </Card>

              {/* Warnings */}
              {simulationResult.warnings.length > 0 && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="font-medium mb-1">Warnings:</div>
                    <ul className="list-disc list-inside space-y-1">
                      {simulationResult.warnings.map((warning, index) => (
                        <li key={index} className="text-sm">{warning}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

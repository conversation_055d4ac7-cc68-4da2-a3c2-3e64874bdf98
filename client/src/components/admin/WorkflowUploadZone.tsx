import React, { useState, useCallback, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { ERPVendor, ERPImportResult } from "@/types/workflow";
import { ERPParserFactory } from "@/services/erpParsers";
import { Upload, FileText, AlertTriangle, CheckCircle, X } from "lucide-react";

interface WorkflowUploadZoneProps {
  onImportComplete: (result: ERPImportResult) => void;
}

interface UploadedFile {
  file: File;
  id: string;
  status: "pending" | "processing" | "success" | "error";
  result?: ERPImportResult;
  error?: string;
}

export function WorkflowUploadZone({ onImportComplete }: WorkflowUploadZoneProps) {
  const [selectedVendor, setSelectedVendor] = useState<ERPVendor | "">("");
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isDragActive, setIsDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return;

    const newFiles: UploadedFile[] = Array.from(files).map(file => ({
      file,
      id: `${file.name}-${Date.now()}`,
      status: "pending"
    }));

    setUploadedFiles(prev => [...prev, ...newFiles]);
  }, []);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const processFile = async (uploadedFile: UploadedFile) => {
    if (!selectedVendor) {
      setUploadedFiles(prev => prev.map(f =>
        f.id === uploadedFile.id
          ? { ...f, status: "error", error: "Please select an ERP vendor first" }
          : f
      ));
      return;
    }

    setUploadedFiles(prev => prev.map(f =>
      f.id === uploadedFile.id ? { ...f, status: "processing" } : f
    ));

    try {
      const content = await readFileContent(uploadedFile.file);
      const result = await ERPParserFactory.parseFile(
        selectedVendor as ERPVendor,
        content,
        uploadedFile.file.name
      );

      setUploadedFiles(prev => prev.map(f =>
        f.id === uploadedFile.id
          ? { ...f, status: "success", result }
          : f
      ));

      onImportComplete(result);
    } catch (error) {
      setUploadedFiles(prev => prev.map(f =>
        f.id === uploadedFile.id
          ? { ...f, status: "error", error: error.message }
          : f
      ));
    }
  };

  const processAllFiles = async () => {
    setIsProcessing(true);
    const pendingFiles = uploadedFiles.filter(f => f.status === "pending");

    for (const file of pendingFiles) {
      await processFile(file);
    }

    setIsProcessing(false);
  };

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const clearAll = () => {
    setUploadedFiles([]);
  };

  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = (e) => reject(new Error("Failed to read file"));
      reader.readAsText(file);
    });
  };

  const getStatusIcon = (status: UploadedFile["status"]) => {
    switch (status) {
      case "pending":
        return <FileText className="h-4 w-4 text-gray-500" />;
      case "processing":
        return <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent" />;
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusBadge = (status: UploadedFile["status"]) => {
    const variants = {
      pending: "secondary",
      processing: "default",
      success: "default",
      error: "destructive"
    } as const;

    const labels = {
      pending: "Pending",
      processing: "Processing",
      success: "Success",
      error: "Error"
    };

    return (
      <Badge variant={variants[status]} className="text-xs">
        {labels[status]}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <div>
        <h4 className="font-semibold mb-2">Upload Workflow Files</h4>
        <p className="text-sm text-muted-foreground">
          Upload workflow definitions from your ERP system. Supported formats: JSON, XML, XLSX, CSV, ZIP.
        </p>
      </div>

      {/* Vendor Selection */}
      <div className="space-y-2">
        <label className="text-sm font-medium">ERP Vendor</label>
        <Select value={selectedVendor} onValueChange={setSelectedVendor}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select your ERP vendor" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="SAP">SAP</SelectItem>
            <SelectItem value="Oracle">Oracle Fusion</SelectItem>
            <SelectItem value="Dynamics365">Microsoft Dynamics 365</SelectItem>
            <SelectItem value="NetSuite">NetSuite</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Upload Zone */}
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${isDragActive
            ? "border-blue-500 bg-blue-50"
            : "border-gray-300 hover:border-gray-400"
          }
        `}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".json,.xml,.xlsx,.xls,.csv,.zip"
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
        />
        <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
        {isDragActive ? (
          <p className="text-blue-600">Drop the files here...</p>
        ) : (
          <div>
            <p className="text-gray-600 mb-2">
              Drag & drop workflow files here, or click to select
            </p>
            <p className="text-sm text-gray-500">
              Supports JSON, XML, XLSX, CSV, and ZIP files
            </p>
          </div>
        )}
      </div>

      {/* File List */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h5 className="font-medium">Uploaded Files ({uploadedFiles.length})</h5>
            <div className="space-x-2">
              <Button
                onClick={processAllFiles}
                disabled={isProcessing || !selectedVendor || uploadedFiles.every(f => f.status !== "pending")}
                size="sm"
              >
                Process All
              </Button>
              <Button onClick={clearAll} variant="outline" size="sm">
                Clear All
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            {uploadedFiles.map((uploadedFile) => (
              <div key={uploadedFile.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(uploadedFile.status)}
                  <div>
                    <p className="text-sm font-medium">{uploadedFile.file.name}</p>
                    <p className="text-xs text-gray-500">
                      {(uploadedFile.file.size / 1024).toFixed(1)} KB
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {getStatusBadge(uploadedFile.status)}

                  {uploadedFile.status === "pending" && selectedVendor && (
                    <Button
                      onClick={() => processFile(uploadedFile)}
                      size="sm"
                      variant="outline"
                    >
                      Process
                    </Button>
                  )}

                  <Button
                    onClick={() => removeFile(uploadedFile.id)}
                    size="sm"
                    variant="ghost"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {/* Results Summary */}
          {uploadedFiles.some(f => f.result) && (
            <div className="space-y-2">
              <h6 className="font-medium">Import Results</h6>
              {uploadedFiles
                .filter(f => f.result)
                .map(f => (
                  <Alert key={f.id}>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>{f.file.name}:</strong> Imported {f.result!.workflows.length} workflow(s)
                      {f.result!.warnings.length > 0 && (
                        <span className="text-amber-600"> with {f.result!.warnings.length} warning(s)</span>
                      )}
                    </AlertDescription>
                  </Alert>
                ))}
            </div>
          )}

          {/* Errors */}
          {uploadedFiles.some(f => f.error) && (
            <div className="space-y-2">
              {uploadedFiles
                .filter(f => f.error)
                .map(f => (
                  <Alert key={f.id} variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>{f.file.name}:</strong> {f.error}
                    </AlertDescription>
                  </Alert>
                ))}
            </div>
          )}
        </div>
      )}

      {!selectedVendor && uploadedFiles.length > 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Please select an ERP vendor before processing files.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

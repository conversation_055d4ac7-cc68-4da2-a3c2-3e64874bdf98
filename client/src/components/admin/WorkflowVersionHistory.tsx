import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { WorkflowDefinition, WorkflowVersion } from "@/types/workflow";
import { History, RotateCcw, Eye, GitBranch } from "lucide-react";

interface WorkflowVersionHistoryProps {
  selectedWorkflow: WorkflowDefinition | null;
}

// Mock version history data
const mockVersions: WorkflowVersion[] = [
  {
    id: "ver-001",
    workflowId: "wf-001",
    version: "1.2.0",
    definition: {} as WorkflowDefinition,
    createdAt: "2024-01-20T14:30:00Z",
    createdBy: "admin",
    changeLog: "Updated approval thresholds and added timeout configuration"
  },
  {
    id: "ver-002", 
    workflowId: "wf-001",
    version: "1.1.0",
    definition: {} as WorkflowDefinition,
    createdAt: "2024-01-18T09:15:00Z",
    createdBy: "admin",
    changeLog: "Added parallel approval support for high-value transactions"
  },
  {
    id: "ver-003",
    workflowId: "wf-001", 
    version: "1.0.0",
    definition: {} as WorkflowDefinition,
    createdAt: "2024-01-15T10:00:00Z",
    createdBy: "admin",
    changeLog: "Initial version imported from SAP"
  }
];

export function WorkflowVersionHistory({ selectedWorkflow }: WorkflowVersionHistoryProps) {
  const versions = selectedWorkflow 
    ? mockVersions.filter(v => v.workflowId === selectedWorkflow.id)
    : [];

  const handleViewDiff = (version: WorkflowVersion) => {
    // In a real app, this would show a diff viewer
    console.log("View diff for version:", version.version);
  };

  const handleRollback = (version: WorkflowVersion) => {
    // In a real app, this would rollback to the selected version
    console.log("Rollback to version:", version.version);
  };

  const handleViewDefinition = (version: WorkflowVersion) => {
    // In a real app, this would show the full workflow definition
    console.log("View definition for version:", version.version);
  };

  if (!selectedWorkflow) {
    return (
      <div className="space-y-6">
        <div>
          <h4 className="font-semibold">Version History</h4>
          <p className="text-sm text-muted-foreground">
            View and manage workflow definition versions with diff comparison and rollback capabilities.
          </p>
        </div>

        <Alert>
          <History className="h-4 w-4" />
          <AlertDescription>
            Select a workflow from the Definitions tab to view its version history.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h4 className="font-semibold">Version History</h4>
        <p className="text-sm text-muted-foreground">
          Version history for <strong>{selectedWorkflow.name}</strong>
        </p>
      </div>

      {versions.length === 0 ? (
        <Alert>
          <History className="h-4 w-4" />
          <AlertDescription>
            No version history available for this workflow.
          </AlertDescription>
        </Alert>
      ) : (
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Version</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Created By</TableHead>
                <TableHead>Change Log</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-[150px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {versions.map((version, index) => (
                <TableRow key={version.id}>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <GitBranch className="h-4 w-4 text-muted-foreground" />
                      <span className="font-mono">{version.version}</span>
                      {index === 0 && (
                        <Badge variant="default" className="text-xs">
                          Current
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-sm">
                    {new Date(version.createdAt).toLocaleString()}
                  </TableCell>
                  <TableCell>{version.createdBy}</TableCell>
                  <TableCell>
                    <div className="max-w-[300px] truncate text-sm">
                      {version.changeLog || "No change log provided"}
                    </div>
                  </TableCell>
                  <TableCell>
                    {index === 0 ? (
                      <Badge variant="default">Active</Badge>
                    ) : (
                      <Badge variant="outline">Historical</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleViewDefinition(version)}
                        title="View definition"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      
                      {index > 0 && (
                        <>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleViewDiff(version)}
                            title="View diff"
                          >
                            <GitBranch className="h-4 w-4" />
                          </Button>
                          
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleRollback(version)}
                            title="Rollback to this version"
                          >
                            <RotateCcw className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Version Comparison Info */}
      {versions.length > 1 && (
        <div className="bg-muted/30 rounded-lg p-4">
          <h5 className="font-medium mb-2">Version Management</h5>
          <div className="text-sm text-muted-foreground space-y-1">
            <p>• Click the diff icon to compare versions and see what changed</p>
            <p>• Use the rollback button to revert to a previous version</p>
            <p>• Rolling back creates a new version with the previous definition</p>
            <p>• All version changes are tracked for audit compliance</p>
          </div>
        </div>
      )}
    </div>
  );
}

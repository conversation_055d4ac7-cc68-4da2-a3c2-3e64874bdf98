import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AdminPage from '@/pages/AdminPage';
import { validateApprovalPredicate, validateEmail } from '@/types/admin';

// Mock the admin service
jest.mock('@/services/adminService', () => ({
  adminService: {
    getOrganization: jest.fn().mockResolvedValue({
      legalName: "",
      displayName: "",
      accountNumber: "",
      emailDomain: "",
      industry: "",
      fiscalCurrency: "USD",
      erpBaseUrl: "",
      erpApiKey: "",
    }),
    getMembers: jest.fn().mockResolvedValue([]),
    getApprovalPredicate: jest.fn().mockResolvedValue({
      tier1: "",
      tier2: "",
      accountOwner: "",
    }),
    updateOrganization: jest.fn().mockResolvedValue({}),
    createMember: jest.fn().mockResolvedValue({}),
    updateApprovalPredicate: jest.fn().mockResolvedValue({}),
  },
}));

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('AdminPage', () => {
  it('renders admin dashboard with tabs', async () => {
    renderWithQueryClient(<AdminPage />);
    
    expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Organization')).toBeInTheDocument();
    expect(screen.getByText('Members')).toBeInTheDocument();
    expect(screen.getByText('Approval Settings')).toBeInTheDocument();
  });

  it('shows API Reference button', async () => {
    renderWithQueryClient(<AdminPage />);
    
    expect(screen.getByText('API Reference')).toBeInTheDocument();
  });
});

describe('Admin Validation Functions', () => {
  describe('validateEmail', () => {
    it('validates email against domain correctly', () => {
      expect(validateEmail('<EMAIL>', 'company.com')).toBe(true);
      expect(validateEmail('<EMAIL>', 'company.com')).toBe(false);
      expect(validateEmail('invalid-email', 'company.com')).toBe(false);
      expect(validateEmail('', 'company.com')).toBe(false);
    });
  });

  describe('validateApprovalPredicate', () => {
    it('validates unique approvers correctly', () => {
      const validPredicate = {
        tier1: '<EMAIL>',
        tier2: '<EMAIL>',
        accountOwner: '<EMAIL>',
      };
      expect(validateApprovalPredicate(validPredicate)).toBe(true);

      const invalidPredicate = {
        tier1: '<EMAIL>',
        tier2: '<EMAIL>', // duplicate
        accountOwner: '<EMAIL>',
      };
      expect(validateApprovalPredicate(invalidPredicate)).toBe(false);

      const incompletePredicate = {
        tier1: '<EMAIL>',
        tier2: '',
        accountOwner: '<EMAIL>',
      };
      expect(validateApprovalPredicate(incompletePredicate)).toBe(false);
    });
  });
});

import * as React from "react";
import { cn } from "@/lib/utils";

interface MainContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

const MainContainer = React.forwardRef<HTMLDivElement, MainContainerProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "container mx-auto px-4 py-6 md:px-6 lg:px-8",
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

MainContainer.displayName = "MainContainer";

export { MainContainer };

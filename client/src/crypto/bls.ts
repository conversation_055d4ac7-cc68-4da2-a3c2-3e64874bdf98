import { sha256 } from '@noble/hashes/sha256';

/**
 * BLS Cryptographic Implementation
 * 
 * This module provides BLS (<PERSON><PERSON><PERSON>) cryptographic functions for the application.
 * It allows for signing payments when users approve them, creating cryptographic
 * proof of authorization while keeping private keys secure in the client environment.
 * 
 * In a production environment, this would use the full @chainsafe/bls implementation
 * with secure key management. For this demo, we're using a simplified implementation
 * based on SHA-256.
 */

// Get the private key from environment variables
const PRIVATE_KEY = import.meta.env.VITE_BLS_PRIVATE_KEY || "0x0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef";

// The corresponding public key (in a real implementation, this would be derived from the private key)
// For demo purposes, we'll use a hash of the private key as the public key
const PUBLIC_KEY = sha256(new TextEncoder().encode(PRIVATE_KEY));

// Simulate asynchronous BLS library initialization
// In real implementation, this would wait for WASM to be ready
export const blsReady = Promise.resolve();

/**
 * Convert Uint8Array to hexadecimal string
 * 
 * @param bytes - The byte array to convert
 * @returns Hexadecimal string representation
 */
function toHexString(bytes: Uint8Array): string {
  return Array.from(bytes)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * Convert hexadecimal string to Uint8Array
 * 
 * @param hexString - The hex string to convert (with or without 0x prefix)
 * @returns Uint8Array representation
 */
function fromHexString(hexString: string): Uint8Array {
  // Remove 0x prefix if present
  const hex = hexString.startsWith('0x') ? hexString.slice(2) : hexString;
  
  const bytes = new Uint8Array(hex.length / 2);
  for (let i = 0; i < hex.length; i += 2) {
    bytes[i / 2] = parseInt(hex.substring(i, i + 2), 16);
  }
  return bytes;
}

/**
 * Create a message hash from payment details
 * 
 * This function creates a deterministic hash using the payment reference,
 * recipient account, and amount - these are the essential components that
 * should be cryptographically verified.
 * 
 * @param ref - Payment reference number
 * @param acct - Recipient account number or address
 * @param amt - Payment amount
 * @returns SHA-256 hash of the payment details
 */
export function hashRefAcctAmt(ref: string, acct: string, amt: string): Uint8Array {
  // Create a deterministic string from the payment details
  // acct parameter should be the account number or address from the payment file
  const preimage = `${ref}|${acct}|${amt}`;
  
  // Hash the preimage using SHA-256
  return sha256(new TextEncoder().encode(preimage));
}

/**
 * Sign a payment using BLS cryptography
 * 
 * This function creates a signature for the payment using the private key and
 * returns both the signature and the message that was signed.
 * 
 * @param ref - Payment reference number
 * @param acct - Recipient account number or address from payment file
 * @param amt - Payment amount
 * @returns Object containing the signature and message hash
 */
export function signPayment(ref: string, acct: string, amt: string): { signature: string; message: string } {
  console.log("Signing payment with:", {
    ref,
    acct,
    amt
  });
  
  // Hash the payment details to create the message
  const msg = hashRefAcctAmt(ref, acct, amt);
  
  // In a real BLS implementation, we would use the private key to sign the message
  // For this demo, we simulate signing by combining the message with the private key
  const privateKeyBytes = new TextEncoder().encode(PRIVATE_KEY);
  const combinedData = new Uint8Array(msg.length + privateKeyBytes.length);
  combinedData.set(msg);
  combinedData.set(privateKeyBytes, msg.length);
  
  // Create a deterministic signature by hashing the combined data
  const signature = sha256(combinedData);
  
  // Return both the signature and message as hex strings
  const signatureHex = `0x${toHexString(signature)}`;
  const messageHex = `0x${toHexString(msg)}`;
  
  console.log("Generated signature:", {
    signatureHex,
    messageHex
  });
  
  return {
    signature: signatureHex,
    message: messageHex,
  };
}

/**
 * Verify a payment signature
 * 
 * This function verifies that a signature is valid for a given message.
 * In a real BLS implementation, this would use the public key to verify the signature.
 * For this demo, we're simulating the verification process.
 * 
 * @param signature - The signature to verify (hex string)
 * @param message - The message that was signed (hex string)
 * @param ref - Payment reference number
 * @param acct - Recipient account number or address from payment file
 * @param amt - Payment amount
 * @returns Boolean indicating whether the signature is valid
 */
export function verifyPaymentSignature(
  signature: string, 
  message: string, 
  ref: string, 
  acct: string, 
  amt: string
): boolean {
  console.log("Verifying signature with:", {
    signature,
    message,
    ref,
    acct,
    amt
  });

  // First verify that the provided message matches a recomputed message hash
  const expectedMsg = hashRefAcctAmt(ref, acct, amt);
  const expectedMsgHex = `0x${toHexString(expectedMsg)}`;
  
  // Log message comparison for debugging
  console.log("Message comparison:", {
    providedMessage: message,
    expectedMessage: expectedMsgHex,
    match: message === expectedMsgHex
  });
  
  // If message doesn't match what we expect based on the payment details, fail verification
  if (message !== expectedMsgHex) {
    console.error("Message verification failed");
    return false;
  }
  
  // For the demo, we recreate the signature using the same method as in signPayment
  // and check if it matches the provided signature
  const messageBytes = fromHexString(message.startsWith('0x') ? message.slice(2) : message);
  const privateKeyBytes = new TextEncoder().encode(PRIVATE_KEY);
  
  const combinedData = new Uint8Array(messageBytes.length + privateKeyBytes.length);
  combinedData.set(messageBytes);
  combinedData.set(privateKeyBytes, messageBytes.length);
  
  const expectedSignature = `0x${toHexString(sha256(combinedData))}`;
  
  // Log signature comparison for debugging
  console.log("Signature comparison:", {
    providedSignature: signature,
    expectedSignature,
    match: signature === expectedSignature
  });
  
  // Return whether the provided signature matches the expected one
  return signature === expectedSignature;
}
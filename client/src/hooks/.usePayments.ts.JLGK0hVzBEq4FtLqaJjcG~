import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Payment } from "@shared/schema";

export function usePayments() {
  const queryClient = useQueryClient();
  
  // Get all payments
  const { data: payments = [], isLoading, error } = useQuery<Payment[]>({
    queryKey: ["/api/payments"],
  });
  
  // Not approved payments - sort by creation time (newest first)
  const notApprovedPayments = [...payments]
    .filter(p => p.status === "Not Approved" && p.approved === false)
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  
  // Approved payments - sort by approval time or creation time if approval time doesn't exist (newest first)
  const approvedPayments = [...payments]
    .filter(p => (p.status === "Approved" || p.approved === true) && !p.sent_at)
    .sort((a, b) => {
      // Use created_at for sorting as a fallback
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    });
  
  // Sent payments - sort by sent time (newest first)
  const sentPayments = [...payments]
    .filter(p => 
      (p.status === "Sent" || p.status === "Paid") && 
      p.sent_at && 
      !p.remittance_generated
    )
    .sort((a, b) => new Date(b.sent_at!).getTime() - new Date(a.sent_at!).getTime());
  
  // Remitted payments - sort by remittance time (newest first)
  const remittedPayments = [...payments]
    .filter(p => p.remittance_generated)
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  
  // Get a single payment
  const getPayment = (id: number) => {
    return useQuery<Payment>({
      queryKey: ["/api/payments", id.toString()],
      queryFn: async () => {
        const response = await fetch(`/api/payments/${id}`, {
          credentials: "include",
        });
        if (!response.ok) {
          throw new Error("Failed to fetch payment");
        }
        return response.json();
      },
      enabled: !!id,
    });
  };
  
  // Direct update method that doesn't wait for the API
  const directlyUpdatePaymentStatus = (id: number, newStatus: string, approved: boolean = false) => {
    // Get current payments data from the cache
    queryClient.setQueryData(
      ["/api/payments"],
      (oldPayments: Payment[] | undefined) => {
        if (!oldPayments) return [];
        
        // Get current timestamp for status_updated_at
        const nowTimestamp = new Date().toISOString();
        
        // Create a deep copy to avoid mutation issues
        return oldPayments.map(payment => {
          if (payment.id === id) {
            // Use type assertion to avoid TypeScript errors for dynamic properties
            return {
              ...payment,
              status: newStatus,
              approved: approved,
            } as Payment;
          }
          return payment;
        });
      }
    );
  };

  // Approve payment mutation
  const approvePaymentMutation = useMutation({
    mutationFn: async (id: number) => {
      // First update the UI immediately
      directlyUpdatePaymentStatus(id, "Approved", true);
      
      // Then make the actual API call
      const res = await apiRequest("POST", `/api/payments/${id}/approve`, {});
      return res.json();
    },
    onSuccess: (updatedPayment) => {
      // After success, refresh the real data
      queryClient.invalidateQueries({ queryKey: ["/api/payments"] });
    },
    onError: (error, id) => {
      // If the API call fails, revert the optimistic update
      directlyUpdatePaymentStatus(id as number, "Not Approved", false);
    },
  });
  
  // Revoke payment approval mutation
  const revokePaymentMutation = useMutation({
    mutationFn: async (id: number) => {
      // First update the UI immediately
      directlyUpdatePaymentStatus(id, "Not Approved", false);
      
      // Then make the actual API call
      const res = await apiRequest("POST", `/api/payments/${id}/revoke`, {});
      return res.json();
    },
    onSuccess: () => {
      // After success, refresh the real data
      queryClient.invalidateQueries({ queryKey: ["/api/payments"] });
    },
    onError: (error, id) => {
      // If the API call fails, revert the optimistic update
      directlyUpdatePaymentStatus(id as number, "Approved", true);
    },
  });
  
  // Send payment mutation
  const sendPaymentMutation = useMutation({
    mutationFn: async (id: number) => {
      // First update the UI immediately
      const timestamp = new Date().toISOString();
      queryClient.setQueryData(
        ["/api/payments"],
        (oldPayments: Payment[] | undefined) => {
          if (!oldPayments) return [];
          
          return oldPayments.map(payment => {
            if (payment.id === id) {
              return {
                ...payment,
                status: "Sent",  // Will be changed to "Paid" by the server when payment completes
                sent_at: timestamp
              } as Payment;
            }
            return payment;
          });
        }
      );
      
      // Then make the actual API call
      const res = await apiRequest("POST", `/api/payments/${id}/send`, {});
      return res.json();
    },
    onSuccess: () => {
      // Wait 5 seconds before refreshing to simulate network delay
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ["/api/payments"] });
      }, 5000);
    },
    onError: (error, id) => {
      // If the API call fails, revert the optimistic update
      queryClient.setQueryData(
        ["/api/payments"],
        (oldPayments: Payment[] | undefined) => {
          if (!oldPayments) return [];
          
          return oldPayments.map(payment => {
            if (payment.id === id as number) {
              return {
                ...payment,
                status: "Approved",
                sent_at: null
              } as Payment;
            }
            return payment;
          });
        }
      );
    }
  });
  
  // Generate remittance mutation
  const generateRemittanceMutation = useMutation({
    mutationFn: async ({ id, format }: { id: number; format: string }) => {
      // First update the UI immediately
      queryClient.setQueryData(
        ["/api/payments"],
        (oldPayments: Payment[] | undefined) => {
          if (!oldPayments) return [];
          
          return oldPayments.map(payment => {
            if (payment.id === id) {
              return {
                ...payment,
                remittance_generated: true,
                // We don't know the remittance ID yet, but we'll set it to 0 temporarily
                // This will be updated when we get the real data from the server
                remittance_id: payment.remittance_id || 0
              };
            }
            return payment;
          });
        }
      );
      
      // Then make the actual API call
      const res = await apiRequest("POST", `/api/payments/${id}/generate-remittance`, { format });
      return res.json();
    },
    onSuccess: () => {
      // Get the latest data with the real remittance ID
      queryClient.invalidateQueries({ queryKey: ["/api/payments"] });
    },
    onError: (error, variables) => {
      // If the API call fails, revert the optimistic update
      const { id } = variables as { id: number };
      queryClient.setQueryData(
        ["/api/payments"],
        (oldPayments: Payment[] | undefined) => {
          if (!oldPayments) return [];
          
          return oldPayments.map(payment => {
            if (payment.id === id) {
              return {
                ...payment,
                remittance_generated: false,
                remittance_id: null
              };
            }
            return payment;
          });
        }
      );
    }
  });
  
  return {
    payments,
    notApprovedPayments,
    approvedPayments,
    sentPayments,
    remittedPayments,
    isLoading,
    error,
    getPayment,
    approvePaymentMutation,
    revokePaymentMutation,
    sendPaymentMutation,
    generateRemittanceMutation,
    directlyUpdatePaymentStatus
  };
}

import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@/components/ui/use-toast";
import { 
  Organization, 
  Member, 
  ApprovalPredicate,
  demoData
} from "@/types/admin";
import { adminService } from "@/services/adminService";

export function useAdminData() {
  const queryClient = useQueryClient();
  
  // Fetch organization data
  const { 
    data: organization,
    isLoading: isOrgLoading
  } = useQuery({
    queryKey: ['/api/admin/organization'],
    queryFn: adminService.getOrganization,
  });
  
  // Fetch members data
  const { 
    data: members,
    isLoading: isMembersLoading
  } = useQuery({
    queryKey: ['/api/admin/members'],
    queryFn: adminService.getMembers,
  });
  
  // Fetch approval predicate
  const { 
    data: approvalPredicate,
    isLoading: isPredicateLoading
  } = useQuery({
    queryKey: ['/api/admin/approval-predicate'],
    queryFn: adminService.getApprovalPredicate,
  });
  
  // Update organization mutation
  const updateOrganizationMutation = useMutation({
    mutationFn: adminService.updateOrganization,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/organization'] });
      toast({
        title: "Organization updated",
        description: "Your organization details have been saved successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to update organization",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Update member mutation
  const updateMemberMutation = useMutation({
    mutationFn: adminService.updateMember,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/members'] });
      toast({
        title: "Member updated",
        description: "Member details have been saved successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to update member",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Create member mutation
  const createMemberMutation = useMutation({
    mutationFn: adminService.createMember,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/members'] });
      toast({
        title: "Member invited",
        description: "An invitation has been sent to the new member.",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to invite member",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Disable member mutation
  const disableMemberMutation = useMutation({
    mutationFn: adminService.disableMember,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/members'] });
      toast({
        title: "Member disabled",
        description: "The member has been disabled successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to disable member",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Update approval predicate mutation
  const updatePredicateMutation = useMutation({
    mutationFn: adminService.updateApprovalPredicate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/approval-predicate'] });
      toast({
        title: "Approval settings updated",
        description: "Your approval settings have been saved successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to update approval settings",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Seed demo data
  const seedDemoData = async () => {
    try {
      // Update organization
      await adminService.updateOrganization(demoData.organization);
      
      // Clear existing members and add demo members
      const existingMembers = await adminService.getMembers();
      for (const member of existingMembers) {
        await adminService.disableMember(member.id);
      }
      
      for (const member of demoData.members) {
        await adminService.createMember(member);
      }
      
      // Update approval predicate
      await adminService.updateApprovalPredicate(demoData.approvalPredicate);
      
      // Refresh all data
      queryClient.invalidateQueries({ queryKey: ['/api/admin'] });
      
      toast({
        title: "Demo data loaded",
        description: "The demo data has been loaded successfully.",
      });
    } catch (error) {
      toast({
        title: "Failed to load demo data",
        description: error.message,
        variant: "destructive",
      });
    }
  };
  
  // Reset data
  const resetData = async () => {
    try {
      // Reset organization
      await adminService.updateOrganization({
        legalName: "",
        displayName: "",
        accountNumber: "",
        emailDomain: "",
        industry: "",
        fiscalCurrency: "USD",
        erpBaseUrl: "",
        erpApiKey: "",
      });
      
      // Clear existing members
      const existingMembers = await adminService.getMembers();
      for (const member of existingMembers) {
        await adminService.disableMember(member.id);
      }
      
      // Reset approval predicate
      await adminService.updateApprovalPredicate({
        tier1: "",
        tier2: "",
        accountOwner: "",
      });
      
      // Refresh all data
      queryClient.invalidateQueries({ queryKey: ['/api/admin'] });
      
      toast({
        title: "Data reset",
        description: "All data has been reset successfully.",
      });
    } catch (error) {
      toast({
        title: "Failed to reset data",
        description: error.message,
        variant: "destructive",
      });
    }
  };
  
  return {
    organization,
    members,
    approvalPredicate,
    isLoading: isOrgLoading || isMembersLoading || isPredicateLoading,
    updateOrganization: updateOrganizationMutation.mutate,
    updateMember: updateMemberMutation.mutate,
    createMember: createMemberMutation.mutate,
    disableMember: disableMemberMutation.mutate,
    updateApprovalPredicate: updatePredicateMutation.mutate,
    seedDemoData,
    resetData,
  };
}
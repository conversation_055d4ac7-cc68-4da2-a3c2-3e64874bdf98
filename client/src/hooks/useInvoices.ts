/**
 * Invoices Data Management Hook
 * 
 * This custom hook provides a comprehensive interface for managing invoice data throughout
 * the Accounts Receivable workflow. It includes queries for fetching invoices and mutations
 * for invoice lifecycle actions (status updates, reconciliation generation).
 * 
 * Key features:
 * - Auto-refreshing data to ensure invoice statuses stay current
 * - Optimistic UI updates for all mutations
 * - Filtered invoice lists for different status categories
 * - Atomic updates to maintain consistent UI state
 */

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Invoice } from "@shared/schema";

export function useInvoices() {
  const queryClient = useQueryClient();
  
  /**
   * Primary query for fetching all invoices
   * Auto-refreshes every 3 seconds to reflect status changes (like overdue)
   */
  const { data: invoices = [], isLoading, error } = useQuery<Invoice[]>({
    queryKey: ["/api/invoices"],
    refetchInterval: 3000, // Auto-refresh every 3 seconds
  });
  
  // Sort invoices by creation time (newest first) 
  // This ensures newest invoices are always at the top of the list
  const sortedInvoices = [...invoices].sort((a, b) => {
    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
  });
  
  // We now include all invoices in the main list
  // Only keep these separate lists for filtering if needed
  const openInvoices = sortedInvoices.filter(i => i.status === "Open" || i.status === "Overdue");
  const paidInvoices = sortedInvoices.filter(i => i.status === "Paid");
  const remittedInvoices = sortedInvoices.filter(i => i.status === "Remitted");
  
  // For AR page, we want all invoices
  const allInvoices = sortedInvoices;
  
  // Get invoices by status
  const getInvoicesByStatus = (status: string) => {
    return useQuery<Invoice[]>({
      queryKey: ["/api/invoices", { status }],
      queryFn: async () => {
        const response = await fetch(`/api/invoices?status=${status}`, {
          credentials: "include",
        });
        if (!response.ok) {
          throw new Error(`Failed to fetch ${status} invoices`);
        }
        return response.json();
      },
    });
  };
  
  // Get a single invoice
  const getInvoice = (id: number) => {
    return useQuery<Invoice>({
      queryKey: ["/api/invoices", id.toString()],
      queryFn: async () => {
        const response = await fetch(`/api/invoices/${id}`, {
          credentials: "include",
        });
        if (!response.ok) {
          throw new Error("Failed to fetch invoice");
        }
        return response.json();
      },
      enabled: !!id,
    });
  };
  
  // Create invoice mutation
  const createInvoiceMutation = useMutation({
    mutationFn: async (invoiceData: any) => {
      const res = await apiRequest("POST", "/api/invoices", invoiceData);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/invoices"] });
    },
  });
  
  // Update invoice status mutation
  const updateInvoiceStatusMutation = useMutation({
    mutationFn: async ({ id, status }: { id: number; status: string }) => {
      // First update the UI immediately
      queryClient.setQueryData(
        ["/api/invoices"],
        (oldInvoices: Invoice[] | undefined) => {
          if (!oldInvoices) return [];
          
          return oldInvoices.map(invoice => {
            if (invoice.id === id) {
              return {
                ...invoice,
                status: status
              } as Invoice;
            }
            return invoice;
          });
        }
      );
      
      // Then make the actual API call
      const res = await apiRequest("POST", `/api/invoices/${id}/update-status`, { status });
      return res.json();
    },
    onSuccess: (updatedInvoice) => {
      // Update the specific invoice without triggering a full refresh
      queryClient.setQueryData(
        ["/api/invoices"],
        (oldInvoices: Invoice[] | undefined) => {
          if (!oldInvoices) return [];
          return oldInvoices.map(invoice => 
            invoice.id === updatedInvoice.id ? updatedInvoice : invoice
          );
        }
      );
    },
  });
  
  // Generate remittance for invoice mutation
  const generateInvoiceRemittanceMutation = useMutation({
    mutationFn: async ({ id, format }: { id: number; format: string }) => {
      // First update the UI immediately
      queryClient.setQueryData(
        ["/api/invoices"],
        (oldInvoices: Invoice[] | undefined) => {
          if (!oldInvoices) return [];
          
          return oldInvoices.map(invoice => {
            if (invoice.id === id) {
              return {
                ...invoice,
                status: "Remitted",
                remittance_generated: true,
                remittance_generated_at: new Date()
              } as unknown as Invoice;
            }
            return invoice;
          });
        }
      );
      
      // Then make the actual API call
      const res = await apiRequest("POST", `/api/invoices/${id}/generate-remittance`, { format });
      return res.json();
    },
    onSuccess: (result) => {
      // Update the specific invoice with the server response without full refresh
      queryClient.setQueryData(
        ["/api/invoices"],
        (oldInvoices: Invoice[] | undefined) => {
          if (!oldInvoices) return [];
          return oldInvoices.map(invoice => 
            invoice.id === result.invoice_id ? {
              ...invoice,
              status: "Remitted",
              remittance_generated: true,
              remittance_id: result.id
            } : invoice
          );
        }
      );
      
      // Only update received payments directly through their API if needed
      apiRequest("GET", "/api/received-payments", {})
        .then(res => res.json())
        .then(payments => {
          queryClient.setQueryData(["/api/received-payments"], payments);
        });
    },
  });
  
  return {
    invoices,
    openInvoices,
    paidInvoices,
    remittedInvoices,
    allInvoices,  // Include the allInvoices list
    isLoading,
    error,
    getInvoicesByStatus,
    getInvoice,
    createInvoiceMutation,
    updateInvoiceStatusMutation,
    generateInvoiceRemittanceMutation,
  };
}

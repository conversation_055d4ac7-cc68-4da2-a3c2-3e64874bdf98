/**
 * Payments Data Management Hook
 * 
 * This custom hook provides a comprehensive interface for managing payment data throughout
 * the Accounts Payable workflow. It includes queries for fetching payments and mutations
 * for payment lifecycle actions (approval, sending, reconciliation).
 * 
 * Key features:
 * - BLS cryptographic signatures for secure payment authorization
 * - Receipt import functionality for manual reconciliation verification
 * - Optimistic UI updates for all mutations ensuring smooth, immediate feedback
 * - Detailed sorting and filtering for various payment views
 * - Error handling with automatic state rollback
 * - Atomic updates to maintain column sorting during state transitions
 * 
 * Payment lifecycle flow:
 * 1. Import → Not Approved (Approval column)
 * 2. Approve → Approved + BLS signature (Approval column + Payment column)
 * 3. Send → Paid (Sent column) + Transaction file download
 * 4. Import Receipt → Paid + receipt_imported=true (Reconciliation column)
 * 5. Generate Reconciliation → Reconciled (Reconciliation column)
 * 
 * This hook implements the complex state transitions required for the payment approval
 * workflow while maintaining UI consistency by updating only specific payments.
 */

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Payment } from "@/types";
import { blsReady, signPayment } from "@/crypto/bls";

export function usePayments() {
  const queryClient = useQueryClient();
  
  /**
   * Primary query for fetching all payments
   * This data is used as the source for all filtered payment views
   */
  const { data: payments = [], isLoading, error } = useQuery<Payment[]>({
    queryKey: ["/api/payments"],
  });
  
  /**
   * All payments sorted chronologically (newest first)
   * Used for the main "Payments" column that shows all payments regardless of status
   */
  const notApprovedPayments = [...payments]
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
    
  /**
   * Payments requiring approval action (unapproved only)
   * Used for the "Approve" column showing payments that need approval
   */
  const approvalRequests = [...payments]
    .filter(p => !p.approved && p.status === "Not Approved")
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  
  /**
   * All approved payments including those that have been paid or reconciled
   * This ensures the column maintains a complete history and payments are never removed
   * even after changing status to Paid or Reconciled
   */
  const approvedPayments = [...payments]
    .filter(p => p.approved === true || p.status === "Paid" || p.status === "Remitted") 
    .sort((a, b) => {
      // Sort by approved_at timestamp to maintain consistent order
      // This ensures reconciliation generation doesn't change the payment order
      return new Date(b.approved_at || b.created_at).getTime() - new Date(a.approved_at || a.created_at).getTime();
    });
  
  /**
   * Payments that have been sent to the blockchain network and have had receipts imported
   * 
   * This is a critical part of the fix for the reconciliation workflow:
   * 1. Previously, all "Paid" payments automatically appeared in the reconciliation column
   * 2. Now, payments only appear in the reconciliation column when:
   *    - They have been sent (status="Paid")
   *    - AND a receipt has been imported (receipt_imported=true)
   *    - BUT they haven't been reconciled yet (status not "Reconciled")
   * 
   * This allows for a manual verification step where users can import receipts
   * and then generate reconciliation documents only after confirming receipt.
   */
  const sentPayments = [...payments]
    .filter(p => 
      // Only show payments in the reconciliation column if they have had a receipt imported
      // This prevents auto-populating the column with just "Paid" status payments
      p.receipt_imported === true && p.sent_at && p.status === "Paid"
    )
    .sort((a, b) => new Date(b.sent_at!).getTime() - new Date(a.sent_at!).getTime());
  
  /**
   * Payments that have been reconciled (previously called "remitted")
   * Used for tracking payments that have completed the full lifecycle
   * Sorted by reconciliation generation timestamp to show newest first
   */
  const remittedPayments = [...payments]
    .filter(p => p.status === "Remitted" && p.remittance_generated)
    .sort((a, b) => {
      // Multi-level sorting strategy - first try reconciliation timestamps
      if (a.remittance_generated_at && b.remittance_generated_at) {
        return new Date(b.remittance_generated_at).getTime() - new Date(a.remittance_generated_at).getTime();
      }
      // If reconciliation timestamps aren't available, try sent timestamps
      if (a.sent_at && b.sent_at) {
        return new Date(b.sent_at).getTime() - new Date(a.sent_at).getTime();
      }
      // Fall back to creation timestamps as last resort
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    });
  
  /**
   * Function to fetch a single payment by ID
   * Used for displaying payment details in modals and panels
   * 
   * @param id - The payment ID to fetch
   * @returns A query result containing the payment data when successful
   */
  const getPayment = (id: number) => {
    return useQuery<Payment>({
      queryKey: ["/api/payments", id.toString()],
      queryFn: async () => {
        const response = await fetch(`/api/payments/${id}`, {
          credentials: "include",
        });
        if (!response.ok) {
          throw new Error("Failed to fetch payment");
        }
        return response.json();
      },
      enabled: !!id, // Only execute query when ID is provided
    });
  };
  
  /**
   * Utility function to directly update payment status in the cache
   * Used for optimistic updates and error recovery without API calls
   * 
   * @param id - The payment ID to update
   * @param newStatus - The new status to apply to the payment
   * @param approved - Whether the payment should be marked as approved
   */
  const directlyUpdatePaymentStatus = (id: number, newStatus: string, approved: boolean = false) => {
    // Update payment data in the cache without waiting for API response
    queryClient.setQueryData(
      ["/api/payments"],
      (oldPayments: Payment[] | undefined) => {
        if (!oldPayments) return [];
        
        // Generate current timestamp for tracking update time
        const nowTimestamp = new Date().toISOString();
        
        // Create a deep copy with the specific payment updated
        return oldPayments.map(payment => {
          if (payment.id === id) {
            // Use type assertion to ensure TypeScript compatibility
            return {
              ...payment,
              status: newStatus,
              approved: approved,
            } as Payment;
          }
          return payment;
        });
      }
    );
  };

  // Approve payment mutation - optimized for smooth transitions
  const approvePaymentMutation = useMutation({
    mutationFn: async (id: number) => {
      // Ensure BLS is ready to use
      await blsReady;
      
      // Create payment with optimistic update values
      const timestamp = new Date();
      const optimisticPayment = payments.find(p => p.id === id);
      
      if (!optimisticPayment) {
        throw new Error("Payment not found");
      }
      
      // Generate BLS signature for this payment - use recipient_account if available
      const { signature, message } = signPayment(
        optimisticPayment.reference,
        optimisticPayment.recipient_account || optimisticPayment.recipient_address || optimisticPayment.recipient,
        optimisticPayment.amount.toString()
      );
      
      // Create a copy with the approved status and signature
      const approvedPayment = {
        ...optimisticPayment,
        status: "Approved",
        approved: true,
        approved_at: timestamp,
        signature,
        message
      };
      
      // Update single payment in the cache atomically
      // This will ensure it appears in both columns without refreshing all payments
      queryClient.setQueryData(
        ["/api/payments"],
        (oldPayments: Payment[] | undefined) => {
          if (!oldPayments) return [];
          
          // Insert new approved payment at the start of the payments list
          return oldPayments.map(payment => 
            payment.id === id ? approvedPayment : payment
          );
        }
      );
      
      // Make the actual API call after the UI has been updated
      // Send both the signature and message to the server to be stored in the database
      const res = await apiRequest("POST", `/api/payments/${id}/approve`, {
        signature,
        message
      });
      
      // Return server response (which should now include the signature and message)
      return await res.json();
    },
    onSuccess: (updatedPayment) => {
      // Just update this single payment in the cache without any invalidation
      // Ensure signature and message are preserved even if server doesn't return them
      queryClient.setQueryData(
        ["/api/payments"],
        (oldPayments: Payment[] | undefined) => {
          if (!oldPayments) return [];
          return oldPayments.map(payment => {
            if (payment.id === updatedPayment.id) {
              // Make sure we maintain the signature and message
              return {
                ...updatedPayment,
                signature: updatedPayment.signature || payment.signature,
                message: updatedPayment.message || payment.message
              };
            }
            return payment;
          });
        }
      );
    },
    onError: (error, id) => {
      // If the API call fails, revert the optimistic update
      directlyUpdatePaymentStatus(id as number, "Not Approved", false);
    },
  });
  
  // Revoke payment approval mutation - optimized for smooth transitions
  const revokePaymentMutation = useMutation({
    mutationFn: async (id: number) => {
      // Find the current payment to build optimistic update
      const optimisticPayment = payments.find(p => p.id === id);
      
      if (!optimisticPayment) {
        throw new Error("Payment not found");
      }
      
      // Create a copy with revoked status
      const revokedPayment = {
        ...optimisticPayment,
        status: "Not Approved",
        approved: false,
        approved_at: null
      };
      
      // Update the payment in the cache immediately - single atomic update
      queryClient.setQueryData(
        ["/api/payments"],
        (oldPayments: Payment[] | undefined) => {
          if (!oldPayments) return [];
          return oldPayments.map(payment => 
            payment.id === id ? revokedPayment : payment
          );
        }
      );
      
      // Then make the actual API call
      const res = await apiRequest("POST", `/api/payments/${id}/revoke`, {});
      return res.json();
    },
    onSuccess: (updatedPayment) => {
      // Server succeeded - do a final update with server data but no full refresh
      queryClient.setQueryData(
        ["/api/payments"],
        (oldPayments: Payment[] | undefined) => {
          if (!oldPayments) return [];
          return oldPayments.map(payment => 
            payment.id === updatedPayment.id ? updatedPayment : payment
          );
        }
      );
    },
    onError: (error, id) => {
      // If the API call fails, revert to approved state
      const revertedPayment = payments.find(p => p.id === id);
      if (revertedPayment) {
        queryClient.setQueryData(
          ["/api/payments"],
          (oldPayments: Payment[] | undefined) => {
            if (!oldPayments) return [];
            return oldPayments.map(payment => 
              payment.id === id ? { ...revertedPayment, status: "Approved", approved: true } : payment
            );
          }
        );
      }
    },
  });
  
  // Send payment mutation - optimized for smooth column transitions
  const sendPaymentMutation = useMutation({
    mutationFn: async (id: number) => {
      // Find the payment to build optimistic update
      const optimisticPayment = payments.find(p => p.id === id);
      
      if (!optimisticPayment) {
        throw new Error("Payment not found");
      }
      
      // Current timestamp for sent_at
      const timestamp = new Date();
      
      // Create a copy with paid status - keep approved status!
      const paidPayment = {
        ...optimisticPayment,
        status: "Paid",
        approved: true, // Make sure approved stays true
        sent_at: timestamp
      };
      
      // Single atomic update to move the payment between columns
      queryClient.setQueryData(
        ["/api/payments"],
        (oldPayments: Payment[] | undefined) => {
          if (!oldPayments) return [];
          
          // Update just this one payment without refreshing the whole list
          return oldPayments.map(payment => 
            payment.id === id ? paidPayment : payment
          );
        }
      );
      
      // Make the API call after the UI updates
      await apiRequest("POST", `/api/payments/${id}/send`, {});
      
      // Start polling for payment updates after the server delay
      // Wait for 6 seconds (5 second server delay + 1s buffer)
      await new Promise(resolve => setTimeout(resolve, 6000));
      
      // Now, create a permanent "Paid" payment in the cache that won't revert
      return {
        ...paidPayment,
        status: "Paid" 
      };
    },
    onSuccess: (updatedPayment, id) => {
      // Wait a few ms to let things settle
      setTimeout(() => {
        // Set the payment status to "Paid" if it's from the server
        // and make sure it has sent_at field (crucial for filtering)
        const finalPayment = {
          ...updatedPayment,
          status: "Paid", // Ensure status is "Paid" for UI
          approved: true, // Keep approved flag
          sent_at: updatedPayment.sent_at || new Date().toISOString() // Ensure sent_at is present
        };
        
        // Force a final update of the "Paid" status in all caches
        // This ensures it never reverts even if app fetches more data
        queryClient.setQueryData(
          ["/api/payments"],
          (oldPayments: Payment[] | undefined) => {
            if (!oldPayments) return [];
            return oldPayments.map(payment => 
              payment.id === id ? finalPayment : payment
            );
          }
        );
        
        // Manually force a single refetch to ensure we have the data from server
        queryClient.fetchQuery({ queryKey: ["/api/payments"] });
      }, 100);
    },
    onError: (error, id) => {
      // If the API call fails, revert to approved state
      const revertedPayment = payments.find(p => p.id === id);
      if (revertedPayment) {
        queryClient.setQueryData(
          ["/api/payments"],
          (oldPayments: Payment[] | undefined) => {
            if (!oldPayments) return [];
            return oldPayments.map(payment => 
              payment.id === id ? { ...revertedPayment, status: "Approved", sent_at: null } : payment
            );
          }
        );
      }
      
      // No forced refresh needed - the revert should handle it
    }
  });
  
  // Generate remittance mutation - optimized for smooth transitions
  const generateRemittanceMutation = useMutation({
    mutationFn: async ({ id, format }: { id: number; format: string }) => {
      // Find the payment to build optimistic update
      const optimisticPayment = payments.find(p => p.id === id);
      
      if (!optimisticPayment) {
        throw new Error("Payment not found");
      }
      
      // Current timestamp for remittance generation
      const timestamp = new Date();
      
      // Create a copy with remitted status
      const remittedPayment = {
        ...optimisticPayment,
        status: "Remitted",
        remittance_generated: true,
        remittance_generated_at: timestamp,
        // Temporary remittance ID until server response
        remittance_id: optimisticPayment.remittance_id || 0
      };
      
      // Single atomic update to change the status without refreshing columns
      queryClient.setQueryData(
        ["/api/payments"],
        (oldPayments: Payment[] | undefined) => {
          if (!oldPayments) return [];
          
          // Update just this one payment without refreshing the whole list
          return oldPayments.map(payment => 
            payment.id === id ? remittedPayment : payment
          );
        }
      );
      
      // Then make the actual API call
      const res = await apiRequest("POST", `/api/payments/${id}/generate-remittance`, { format });
      return res.json();
    },
    onSuccess: (result) => {
      // Server succeeded - update with exact server values
      queryClient.setQueryData(
        ["/api/payments"],
        (oldPayments: Payment[] | undefined) => {
          if (!oldPayments) return [];
          
          // Find the payment that needs updating
          return oldPayments.map(payment => {
            if (payment.id === result.payment_id) {
              return {
                ...payment,
                remittance_id: result.id, // Use the remittance ID from the result
                remittance_generated: true,
                status: "Remitted"
              };
            }
            return payment;
          });
        }
      );
    },
    onError: (error, variables) => {
      // If the API call fails, revert to previous state
      const { id } = variables as { id: number };
      const revertedPayment = payments.find(p => p.id === id);
      
      if (revertedPayment) {
        queryClient.setQueryData(
          ["/api/payments"],
          (oldPayments: Payment[] | undefined) => {
            if (!oldPayments) return [];
            
            return oldPayments.map(payment => 
              payment.id === id ? {
                ...revertedPayment,
                remittance_generated: false,
                remittance_id: null
              } : payment
            );
          }
        );
      }
    }
  });
  
  /**
   * Returns a comprehensive set of data and functions for payment management
   * 
   * @returns {Object} Payment management tools and data:
   * - `payments` - Raw payments data array from the API
   * - Filtered payment arrays (`notApprovedPayments`, `approvedPayments`, etc.)
   * - Loading and error states for UI feedback
   * - Functions to fetch individual payments
   * - Mutations for payment lifecycle management with optimistic updates
   * - Utility functions for direct cache manipulation
   */
  return {
    // Raw data and loading states
    payments,               // All payments from the API
    isLoading,              // Loading state for payment data
    error,                  // Error state for payment loading failures
    
    // Filtered payment arrays for specific views/columns
    notApprovedPayments,    // All payments with newest first (main Payments column)
    approvalRequests,       // Unapproved payments only (Approve column)
    approvedPayments,       // Approved payments including paid/reconciled (Approved column)
    sentPayments,           // Sent payments with Paid/Reconciled status (Sent column)
    remittedPayments,       // Reconciled payments only (previously "remitted")
    
    // Functions to interact with payments
    getPayment,             // Get a single payment by ID
    approvePaymentMutation, // Approve a payment
    revokePaymentMutation,  // Revoke payment approval
    sendPaymentMutation,    // Send payment to blockchain
    generateRemittanceMutation, // Generate reconciliation document
    directlyUpdatePaymentStatus // Utility for cache manipulation
  };
}

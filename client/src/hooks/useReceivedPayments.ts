/**
 * Received Payments Data Management Hook
 * 
 * This hook provides comprehensive data access and mutation capabilities for managing
 * incoming payments in the Accounts Receivable workflow. It handles fetching, filtering,
 * and updating received payments, including linking them to invoices and generating
 * reconciliation documents.
 * 
 * Key features:
 * - Auto-refreshing data to ensure payment status stays current
 * - Optimistic UI updates for all mutations to provide immediate feedback
 * - Filtering payments by status
 * - Functions for linking received payments to invoices
 * - Reconciliation document generation
 */

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { ReceivedPayment } from "@shared/schema";

export function useReceivedPayments() {
  const queryClient = useQueryClient();
  
  /**
   * Primary query for fetching all received payments
   * Auto-refreshes every 3 seconds to ensure UI is current with incoming payments
   */
  const { data: allReceivedPayments = [], isLoading, error } = useQuery<ReceivedPayment[]>({
    queryKey: ["/api/received-payments"],
    refetchInterval: 3000, // Auto-refresh every 3 seconds
  });
  
  /**
   * All received payments sorted chronologically (newest first)
   * This ensures the most recent payments appear at the top of payment lists
   * and maintains consistent UI presentation
   */
  const receivedPayments = [...allReceivedPayments].sort((a, b) => {
    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
  });
  
  /**
   * Function to fetch a specific received payment by ID
   * Used for displaying detailed payment information in panels and modals
   * Updates the cache with the retrieved payment to maintain consistency
   * 
   * @param id - The ID of the payment to retrieve
   * @returns The payment object if found, undefined otherwise
   */
  const getReceivedPaymentById = async (id: number): Promise<ReceivedPayment | undefined> => {
    if (!id) return undefined;
    
    try {
      // Fetch the payment from the server
      const res = await apiRequest("GET", `/api/received-payments/${id}`);
      const payment = await res.json();
      
      // Update the cache with the fresh data to ensure consistent UI state
      queryClient.setQueryData(
        ["/api/received-payments"],
        (oldData: ReceivedPayment[] | undefined) => {
          if (!oldData) return [payment];
          // Replace the old payment with the new one, keeping others unchanged
          return oldData.map(item => item.id === id ? payment : item);
        }
      );
      
      return payment;
    } catch (error) {
      console.error("Error fetching payment by ID:", error);
      return undefined;
    }
  };
  
  /**
   * Mutation for linking a received payment to an invoice
   * This is a key part of the Accounts Receivable workflow, connecting incoming
   * payments with their corresponding invoices
   * 
   * When a payment is linked to an invoice:
   * 1. The payment status changes from "Unlinked" to "Linked"
   * 2. The invoice status changes from "Open/Overdue" to "Paid"
   * 3. Both objects are updated with cross-references
   */
  const linkToInvoiceMutation = useMutation({
    mutationFn: async ({ paymentId, invoiceId }: { paymentId: number; invoiceId: number }) => {
      const res = await apiRequest("POST", `/api/received-payments/${paymentId}/link-invoice`, { invoiceId });
      return res.json();
    },
    onSuccess: (result) => {
      // Update the cache with both payment and invoice from the result
      // This ensures immediate UI feedback for both the payment and invoice lists
      if (result.receivedPayment) {
        queryClient.setQueryData(
          ['/api/received-payments'],
          (oldPayments: any[] = []) => {
            return oldPayments.map(payment => 
              payment.id === result.receivedPayment.id ? result.receivedPayment : payment
            );
          }
        );
      }
      
      if (result.invoice) {
        queryClient.setQueryData(
          ['/api/invoices'],
          (oldInvoices: any[] = []) => {
            return oldInvoices.map(invoice => 
              invoice.id === result.invoice.id ? result.invoice : invoice
            );
          }
        );
      }
      
      // Perform a comprehensive refresh to ensure complete data consistency
      // This guards against race conditions and ensures all related data is updated
      Promise.all([
        fetch('/api/received-payments'),
        fetch('/api/invoices')
      ]).then(responses => 
        Promise.all(responses.map(r => r.json()))
      ).then(([payments, invoices]) => {
        queryClient.setQueryData(['/api/received-payments'], payments);
        queryClient.setQueryData(['/api/invoices'], invoices);
        console.log('Payments and invoices refreshed after linking');
      }).catch(error => {
        console.error('Error refreshing data after linking:', error);
      });
    },
  });
  
  /**
   * Mutation for creating a simulated received payment
   * Used in development/testing to create incoming payments without
   * waiting for actual blockchain transactions
   */
  const createReceivedPaymentMutation = useMutation({
    mutationFn: async (paymentData: {
      sender: string;     // Company/person sending the payment
      recipient: string;  // Recipient company (usually the current company)
      amount: number;     // Payment amount
      reference: string;  // Payment reference/identifier
    }) => {
      const res = await apiRequest("POST", `/api/simulate_received_payment`, paymentData);
      return res.json();
    },
    onSuccess: (result) => {
      // Ensure all related data is refreshed after payment creation
      // This is especially important as payments may be auto-linked to invoices
      // by reference number matching on the server
      Promise.all([
        fetch('/api/received-payments'),
        fetch('/api/invoices')
      ]).then(responses => 
        Promise.all(responses.map(r => r.json()))
      ).then(([payments, invoices]) => {
        queryClient.setQueryData(['/api/received-payments'], payments);
        queryClient.setQueryData(['/api/invoices'], invoices);
        console.log('Payments and invoices refreshed after creating payment');
      }).catch(error => {
        console.error('Error refreshing data after creating payment:', error);
      });
    },
  });
  
  /**
   * Filtered payment arrays for different status categories
   * Each maintains the chronological sort order (newest first)
   */
  const unlinkedPayments = receivedPayments.filter(p => p.status === "Unlinked");
  const linkedPayments = receivedPayments.filter(p => p.status === "Linked");
  const remittedPayments = receivedPayments.filter(p => p.status === "Remitted");
  
  // Main list for AR page display - all payments chronologically sorted
  const allPayments = receivedPayments;
  
  /**
   * Returns the complete set of received payment data and functions
   * 
   * @returns {Object} Payment management tools and data:
   * - Various filtered payment arrays for different views and statuses
   * - Loading and error states
   * - Functions to retrieve individual payments
   * - Mutations for payment linking and creation
   */
  return {
    // Payment arrays for different views
    receivedPayments,       // All received payments (sorted)
    unlinkedPayments,       // Payments that aren't linked to invoices
    linkedPayments,         // Payments that are linked to invoices
    remittedPayments,       // Payments that have been reconciled
    allPayments,            // All payments for the main AR view
    
    // Query status
    isLoading,              // Loading state for payment data
    error,                  // Error state if any
    
    // Functions and mutations
    getReceivedPaymentById, // Get a specific payment by ID
    linkToInvoiceMutation,  // Link a payment to an invoice
    createReceivedPaymentMutation, // Create a simulated payment
  };
}

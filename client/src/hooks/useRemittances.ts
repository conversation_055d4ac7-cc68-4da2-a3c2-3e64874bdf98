import { useQuery } from "@tanstack/react-query";
import { Remittance } from "@shared/schema";

export function useRemittances() {
  // Get a single remittance
  const getRemittance = (id: number) => {
    return useQuery<Remittance>({
      queryKey: ["/api/remittances", id.toString()],
      queryFn: async () => {
        const response = await fetch(`/api/remittances/${id}`, {
          credentials: "include",
        });
        if (!response.ok) {
          throw new Error("Failed to fetch remittance");
        }
        return response.json();
      },
      enabled: !!id,
    });
  };
  
  // Download remittance file
  const downloadRemittance = (id: number) => {
    window.open(`/api/remittances/${id}/download`, '_blank');
  };
  
  // View remittance content (debug)
  const viewRemittanceContent = (id: number) => {
    window.open(`/api/remittances/${id}/debug`, '_blank');
  };
  
  return {
    getRemittance,
    downloadRemittance,
    viewRemittanceContent,
  };
}

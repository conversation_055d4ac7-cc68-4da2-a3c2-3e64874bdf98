/**
 * Main Application Styles
 * 
 * This file contains global styles for the ProofPay application.
 * It includes:
 * - Font imports (Inter as the primary font)
 * - Tailwind CSS configuration
 * - Custom component styles and animations
 * - Status indicator styling
 * - Card and UI component enhancements
 */

/* Import Inter font for clean, modern typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Tailwind CSS directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Status Indicators
 * Custom styling for different payment and invoice statuses
 * These classes are applied to badges throughout the application
 */
.status-paid {
  @apply bg-blue-100 text-blue-700 border-blue-300;
}

.status-reconciled {
  @apply bg-gray-100 text-gray-700 border-gray-300;
}

.status-unlinked {
  @apply bg-orange-100 text-orange-700 border-orange-300;
}

.status-linked {
  @apply bg-green-100 text-green-700 border-green-300;
}

/* Custom Animations
 *
 * These animations provide fluid transitions between states
 * and enhance the user experience throughout the application.
 * Used for:
 * - Item movement between columns
 * - Status changes
 * - Detail panel opening/closing
 * - New item additions
 */

/* Pulsing highlight effect for newly updated items */
@keyframes pulse-highlight {
  0% {
    box-shadow: 0 0 0 0 rgba(34, 139, 230, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(34, 139, 230, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(34, 139, 230, 0);
  }
}

/* Simple opacity pulse for subtle attention */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* Animation for items moving out of a column (upward exit) */
@keyframes slide-up-out {
  0% {
    transform: translateY(0);
    max-height: 200px;
    opacity: 1;
    margin-bottom: 1rem;
  }
  30% {
    transform: translateY(-10px);
    opacity: 0.7;
  }
  100% {
    transform: translateY(-30px);
    max-height: 0;
    opacity: 0;
    margin-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
}

/* Animation for items moving into a column (downward entrance) */
@keyframes slide-down-in {
  0% {
    transform: translateY(-20px);
    max-height: 0;
    opacity: 0;
    margin-top: 0;
  }
  30% {
    transform: translateY(5px);
    opacity: 0.5;
  }
  100% {
    transform: translateY(0);
    max-height: 200px;
    opacity: 1;
    margin-top: 0;
    margin-bottom: 1rem;
  }
}

/* Animation for detail panels sliding in from left */
@keyframes slide-in-left {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Animation for detail panels sliding out to left */
@keyframes slide-out-left {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
}

/* Animation utility classes for applying keyframe animations */

/* Combined pulse effect for newly updated or changed items */
.pulse-animation {
  animation: pulse 0.5s ease-in-out, pulse-highlight 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

/* Exit animation for items leaving a column */
.slide-up-out {
  animation: slide-up-out 0.6s ease-out forwards;
  overflow: hidden;
}

/* Entry animation for items entering a column */
.slide-down-in {
  animation: slide-down-in 0.6s ease-out forwards;
  overflow: hidden;
}

/* Detail panel slide-in animation */
.slide-in-left {
  animation: slide-in-left 0.3s ease-out forwards;
}

/* Detail panel slide-out animation */
.slide-out-left {
  animation: slide-out-left 0.3s ease-out forwards;
}

/* Smooth transition for status changes in detail panels */
.detail-panel-content {
  transition: all 0.3s ease-in-out;
}

/* Card hover effects for payment, invoice, and received payment cards */
.payment-card, .invoice-card, .received-payment-card {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

/* Subtle lift effect on card hover for better interactivity */
.payment-card:hover, .invoice-card:hover, .received-payment-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/**
 * Tailwind Base Layer
 *
 * Global styling fundamentals that apply across the application.
 * Establishes typography, base colors, and core visual elements.
 */
@layer base {
  /* Apply border color to all elements */
  * {
    @apply border-border;
  }

  /* Base body styling with Inter font and anti-aliasing */
  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    @apply antialiased bg-background text-foreground;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Heading styles with consistent font family and tracking */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    @apply font-medium tracking-tight;
  }
}

/**
 * Tailwind Components Layer
 *
 * Custom component styles that use the utility classes
 * Define reusable UI patterns used throughout the application.
 */
@layer components {
  /* Card shadow styles for standard and hover states */
  .shadow-card {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.03);
  }

  .shadow-card-hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.02);
  }
  
  /* Button base style */
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary/50;
  }
  
  /* Primary action button */
  .btn-primary {
    @apply bg-primary text-white hover:bg-primary/90;
  }
  
  /* Secondary/outline button */
  .btn-outline {
    @apply border border-gray-300 text-gray-700 hover:bg-gray-50;
  }
  
  /* Status badge style */
  .badge {
    @apply inline-flex text-xs font-medium px-2.5 py-1 rounded-full;
  }
  
  /**
   * Kanban Board Styles
   * 
   * Specialized styling for the drag-and-drop kanban board interface.
   * Includes horizontal scrolling container and custom scrollbars.
   */
  
  /* Main container with horizontal scrolling */
  .kanban-container {
    overflow-x: auto;
    overflow-y: hidden;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    padding-bottom: 10px; /* Space for scrollbar */
  }
  
  /* Apple-style custom scrollbar styles for webkit browsers */
  .custom-scrollbar::-webkit-scrollbar,
  .kanban-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track,
  .kanban-container::-webkit-scrollbar-track {
    background: transparent; 
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb,
  .kanban-container::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover,
  .kanban-container::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }
  
  /* Firefox scrollbar styles */
  .custom-scrollbar, 
  .kanban-container {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db transparent;
  }
}
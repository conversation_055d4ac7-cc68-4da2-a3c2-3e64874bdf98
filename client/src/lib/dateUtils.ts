/**
 * Utility functions for handling dates in the application
 */
import { format, parseISO } from "date-fns";

/**
 * Converts a Date object to a string in ISO format
 * This is needed because our schema expects strings but some values come as Date objects
 */
export function ensureDateString(dateValue: Date | string | null | undefined): string {
  if (!dateValue) return '';
  
  if (dateValue instanceof Date) {
    return dateValue.toISOString();
  }
  
  return String(dateValue);
}

/**
 * Safely formats a date from various inputs
 * Handles both Date objects and string dates
 */
export function formatSafeDate(dateValue: Date | string | null | undefined, formatPattern: string): string {
  if (!dateValue) return 'N/A';
  
  try {
    if (dateValue instanceof Date) {
      return format(dateValue, formatPattern);
    } else {
      return format(parseISO(String(dateValue)), formatPattern);
    }
  } catch (error) {
    console.error("Error formatting date:", error);
    return 'Invalid date';
  }
}
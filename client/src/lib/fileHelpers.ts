/**
 * File Utility Functions
 * 
 * This module provides helper functions for file operations throughout the application
 * including downloading files, file size formatting, extension validation, and text parsing.
 * These utilities are primarily used for handling imported payment/invoice files and
 * downloaded reconciliation documents.
 */

/**
 * Downloads a file from a URL
 * Creates a temporary anchor element to trigger a file download
 * 
 * @param url - The URL of the file to download
 * @param filename - Optional filename to use for the downloaded file
 */
export const downloadFile = (url: string, filename?: string) => {
  const link = document.createElement('a');
  link.href = url;
  if (filename) {
    link.download = filename;
  }
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * Formats a file size in bytes to a human-readable string
 * Converts to the appropriate unit (KB, MB, GB) with 2 decimal places
 * 
 * @param bytes - The file size in bytes
 * @returns A formatted string with the appropriate size unit
 */
export const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Validates if a file has an allowed extension
 * Used in file upload components to ensure only supported formats are accepted
 * 
 * @param filename - The name of the file to check
 * @param allowedExtensions - Array of allowed file extensions (without the dot)
 * @returns True if the file extension is allowed, false otherwise
 */
export const validateFileExtension = (filename: string, allowedExtensions: string[]) => {
  const extension = filename.split('.').pop()?.toLowerCase() || '';
  return allowedExtensions.includes(extension);
};

/**
 * Parses a File object as text using the FileReader API
 * Returns a Promise that resolves with the file content as a string
 * Used for reading uploaded payment/invoice files
 * 
 * @param file - The File object to read
 * @returns A Promise that resolves with the file content as a string
 */
export const parseTextFile = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      if (event.target?.result) {
        resolve(event.target.result as string);
      } else {
        reject(new Error('Failed to read file'));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Error reading file'));
    };
    
    reader.readAsText(file);
  });
};

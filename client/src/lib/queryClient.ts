/**
 * API Query Client Configuration
 * 
 * This module configures the TanStack Query client for API interactions throughout the application.
 * It provides consistent error handling, authentication, and caching behavior.
 */

import { QueryClient, QueryFunction } from "@tanstack/react-query";

/**
 * Helper function to handle non-successful HTTP responses
 * Extracts error message from response body when available
 * 
 * @param res - The fetch Response object to check
 * @throws Error with status code and response text if response is not ok
 */
async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    throw new Error(`${res.status}: ${text}`);
  }
}

/**
 * Makes an API request with consistent error handling and authentication
 * Used for all mutations (POST, PUT, DELETE, PATCH) in the application
 * 
 * @param method - HTTP method (GET, POST, PUT, DELETE, etc.)
 * @param url - API endpoint URL
 * @param data - Optional request body data (automatically stringified to JSON)
 * @returns Fetch Response object after checking for errors
 * @throws Error if the request fails or returns a non-2xx status code
 */
export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  const res = await fetch(url, {
    method,
    headers: data ? { "Content-Type": "application/json" } : {},
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include", // Send cookies for authentication
  });

  await throwIfResNotOk(res);
  return res;
}

/**
 * Behavior options for handling unauthorized (401) responses
 * - "returnNull": Silently return null (used when checking if user is logged in)
 * - "throw": Throw an error (default behavior, forces login/error handling)
 */
type UnauthorizedBehavior = "returnNull" | "throw";

/**
 * Factory function that creates query functions for TanStack Query
 * Configures how API requests are made and how errors are handled
 * 
 * @param options - Configuration options object
 * @param options.on401 - How to handle 401 Unauthorized responses
 * @returns A query function compatible with TanStack Query
 */
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    // Use the first element of queryKey as the URL
    const res = await fetch(queryKey[0] as string, {
      credentials: "include", // Send cookies for authentication
    });

    // Handle unauthorized responses based on configuration
    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      return null;
    }

    // For all other responses, throw on error
    await throwIfResNotOk(res);
    return await res.json();
  };

/**
 * Centralized query client instance used throughout the application
 * Configures global defaults for all queries and mutations
 */
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }), // Default query function
      refetchInterval: false,     // Disable automatic refetching
      refetchOnWindowFocus: false, // Disable refetch on window focus
      staleTime: Infinity,        // Cache data indefinitely until invalidated
      retry: false,               // Don't retry failed queries
    },
    mutations: {
      retry: false,               // Don't retry failed mutations
    },
  },
});

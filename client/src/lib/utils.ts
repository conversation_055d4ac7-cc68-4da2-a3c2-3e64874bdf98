/**
 * General utility functions used throughout the application
 */
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * Combines and merges class names with Tailwind CSS
 * This utility is used to conditionally apply classes and
 * helps manage complex combinations of utility classes
 * 
 * @param inputs - Any number of class values or class objects
 * @returns A merged string of class names optimized for Tailwind
 * 
 * @example
 * // Basic usage
 * cn("px-4 py-2", "bg-blue-500")
 * 
 * @example
 * // With conditionals
 * cn("text-white", isActive && "font-bold", {"hidden": isHidden})
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

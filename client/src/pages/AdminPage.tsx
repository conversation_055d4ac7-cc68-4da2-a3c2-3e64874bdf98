import { useState } from "react";
import { PageHeader } from "@/components/ui/page-header";
import { MainContainer } from "@/components/ui/main-container";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { OrganizationDetails } from "@/components/admin/OrganizationDetails";
import { MemberManagement } from "@/components/admin/MemberManagement";
import { ApprovalPredicate } from "@/components/admin/ApprovalPredicate";
import { ApprovalMatrix } from "@/components/admin/ApprovalMatrix";
import { DemoDataToggle } from "@/components/admin/DemoDataToggle";
import { ERPIntegrationSidebar } from "@/components/admin/ERPIntegrationSidebar";
import { useAdminData } from "@/hooks/useAdminData";
import { Button } from "@/components/ui/button";
import { PanelRightOpen } from "lucide-react";

export default function AdminPage() {
  const { 
    organization, 
    members, 
    approvalPredicate,
    isLoading,
    resetData,
    seedDemoData
  } = useAdminData();
  
  const [sidebarOpen, setSidebarOpen] = useState(false);
  
  return (
    <>
      <PageHeader title="Admin Dashboard" description="Manage your organization settings, users, and integrations">
        <Button 
          variant="outline" 
          onClick={() => setSidebarOpen(!sidebarOpen)}
          className="ml-auto"
        >
          <PanelRightOpen className="h-4 w-4 mr-2" />
          API Reference
        </Button>
      </PageHeader>
      
      <MainContainer className="flex flex-col lg:flex-row gap-6">
        <div className="w-full lg:w-3/4 space-y-6">
          <Tabs defaultValue="organization" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="organization">Organization</TabsTrigger>
              <TabsTrigger value="members">Members</TabsTrigger>
              <TabsTrigger value="approvals">Approval Settings</TabsTrigger>
            </TabsList>
            
            <TabsContent value="organization" className="space-y-6">
              <Card className="p-6">
                <OrganizationDetails 
                  organization={organization} 
                  isLoading={isLoading} 
                />
              </Card>
              
              <Card className="p-6">
                <DemoDataToggle onSeed={seedDemoData} onReset={resetData} />
              </Card>
            </TabsContent>
            
            <TabsContent value="members" className="space-y-6">
              <Card className="p-6">
                <MemberManagement 
                  members={members} 
                  organization={organization}
                  isLoading={isLoading} 
                />
              </Card>
            </TabsContent>
            
            <TabsContent value="approvals" className="space-y-6">
              <Card className="p-6">
                <ApprovalPredicate 
                  predicate={approvalPredicate} 
                  members={members}
                  isLoading={isLoading} 
                />
              </Card>
              
              <Card className="p-6">
                <ApprovalMatrix 
                  members={members} 
                  predicate={approvalPredicate}
                  isLoading={isLoading} 
                />
              </Card>
            </TabsContent>
          </Tabs>
        </div>
        
        <ERPIntegrationSidebar 
          isOpen={sidebarOpen} 
          onClose={() => setSidebarOpen(false)}
          organization={organization}
          predicate={approvalPredicate}
          members={members}
        />
      </MainContainer>
    </>
  );
}
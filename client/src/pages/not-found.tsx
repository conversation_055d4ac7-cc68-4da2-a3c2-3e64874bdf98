/**
 * Not Found Page
 * 
 * Displayed when a user navigates to a route that doesn't exist in the application.
 * This provides a clean error experience with a visual indicator and helpful message.
 * Used by the router when no matching route is found.
 */
import { Card, CardContent } from "@/components/ui/card";
import { AlertCircle } from "lucide-react";

/**
 * NotFound component renders a clean 404 error page
 * 
 * @returns JSX Element with centered error card
 */
export default function NotFound() {
  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md mx-4">
        <CardContent className="pt-6">
          <div className="flex mb-4 gap-2">
            <AlertCircle className="h-8 w-8 text-red-500" />
            <h1 className="text-2xl font-bold text-gray-900">404 Page Not Found</h1>
          </div>

          <p className="mt-4 text-sm text-gray-600">
            Did you forget to add the page to the router?
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

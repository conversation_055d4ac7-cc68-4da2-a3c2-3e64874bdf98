import { WorkflowEngine, WorkflowUtils } from '../workflowEngine';
import { WorkflowDefinition, DocumentContext, ExpressionNode } from '../../types/workflow';

describe('WorkflowEngine', () => {
  let engine: WorkflowEngine;

  beforeEach(() => {
    engine = new WorkflowEngine();
  });

  describe('evaluate', () => {
    const mockWorkflow: WorkflowDefinition = {
      id: 'test-workflow',
      name: 'Test Workflow',
      description: 'Test workflow for unit tests',
      version: '1.0.0',
      documentType: 'INVOICE',
      status: 'ACTIVE',
      steps: [
        {
          id: 'step-1',
          sequence: 1,
          name: 'Manager Approval',
          predicate: {
            type: 'comparison',
            field: 'amount',
            operator: 'lt',
            value: 5000
          },
          approverRoleIds: ['manager'],
          approvalMode: 'SERIAL',
          timeoutHours: 24
        },
        {
          id: 'step-2',
          sequence: 2,
          name: 'Director Approval',
          predicate: {
            type: 'comparison',
            field: 'amount',
            operator: 'gte',
            value: 5000
          },
          approverRoleIds: ['director'],
          approvalMode: 'SERIAL',
          timeoutHours: 48
        }
      ],
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      createdBy: 'test'
    };

    it('should evaluate low amount invoice to manager approval', async () => {
      const document: DocumentContext = {
        id: 'doc-1',
        type: 'INVOICE',
        amount: 2500,
        currency: 'USD',
        metadata: {}
      };

      const result = await engine.evaluate(mockWorkflow, document);

      expect(result.workflowId).toBe('test-workflow');
      expect(result.documentId).toBe('doc-1');
      expect(result.autoApproved).toBe(false);
      expect(result.approvers).toHaveLength(1);
      expect(result.approvers[0].stepId).toBe('step-1');
      expect(result.approvers[0].approverUserIds).toEqual(['manager']);
      expect(result.evaluationTimeMs).toBeLessThan(100);
    });

    it('should evaluate high amount invoice to director approval', async () => {
      const document: DocumentContext = {
        id: 'doc-2',
        type: 'INVOICE',
        amount: 7500,
        currency: 'USD',
        metadata: {}
      };

      const result = await engine.evaluate(mockWorkflow, document);

      expect(result.approvers).toHaveLength(1);
      expect(result.approvers[0].stepId).toBe('step-2');
      expect(result.approvers[0].approverUserIds).toEqual(['director']);
    });

    it('should handle auto-approval workflow', async () => {
      const autoApprovalWorkflow: WorkflowDefinition = {
        ...mockWorkflow,
        steps: [
          {
            id: 'auto-step',
            sequence: 1,
            name: 'Auto Approval',
            predicate: {
              type: 'comparison',
              field: 'amount',
              operator: 'lt',
              value: 100
            },
            approverRoleIds: [],
            approvalMode: 'SERIAL',
            autoApprove: true
          }
        ]
      };

      const document: DocumentContext = {
        id: 'doc-3',
        type: 'INVOICE',
        amount: 50,
        currency: 'USD',
        metadata: {}
      };

      const result = await engine.evaluate(autoApprovalWorkflow, document);

      expect(result.autoApproved).toBe(true);
      expect(result.approvers).toHaveLength(0);
    });

    it('should handle parallel approval mode', async () => {
      const parallelWorkflow: WorkflowDefinition = {
        ...mockWorkflow,
        steps: [
          {
            id: 'parallel-step',
            sequence: 1,
            name: 'Dual Authorization',
            predicate: {
              type: 'comparison',
              field: 'amount',
              operator: 'gt',
              value: 0
            },
            approverRoleIds: ['approver1', 'approver2'],
            approvalMode: 'PARALLEL',
            timeoutHours: 12
          }
        ]
      };

      const document: DocumentContext = {
        id: 'doc-4',
        type: 'INVOICE',
        amount: 1000,
        currency: 'USD',
        metadata: {}
      };

      const result = await engine.evaluate(parallelWorkflow, document);

      expect(result.approvers).toHaveLength(1);
      expect(result.approvers[0].approvalMode).toBe('PARALLEL');
      expect(result.approvers[0].approverUserIds).toEqual(['approver1', 'approver2']);
      expect(result.approvers[0].requiredApprovals).toBe(2);
    });
  });

  describe('predicate evaluation', () => {
    it('should evaluate comparison operators correctly', async () => {
      const testCases = [
        { operator: 'eq', value: 1000, docAmount: 1000, expected: true },
        { operator: 'eq', value: 1000, docAmount: 2000, expected: false },
        { operator: 'ne', value: 1000, docAmount: 2000, expected: true },
        { operator: 'gt', value: 1000, docAmount: 2000, expected: true },
        { operator: 'gt', value: 2000, docAmount: 1000, expected: false },
        { operator: 'gte', value: 1000, docAmount: 1000, expected: true },
        { operator: 'lt', value: 2000, docAmount: 1000, expected: true },
        { operator: 'lte', value: 1000, docAmount: 1000, expected: true }
      ];

      for (const testCase of testCases) {
        const workflow: WorkflowDefinition = {
          id: 'test',
          name: 'Test',
          description: '',
          version: '1.0.0',
          documentType: 'INVOICE',
          status: 'ACTIVE',
          steps: [
            {
              id: 'step-1',
              sequence: 1,
              name: 'Test Step',
              predicate: {
                type: 'comparison',
                field: 'amount',
                operator: testCase.operator as any,
                value: testCase.value
              },
              approverRoleIds: ['test'],
              approvalMode: 'SERIAL'
            }
          ],
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
          createdBy: 'test'
        };

        const document: DocumentContext = {
          id: 'test-doc',
          type: 'INVOICE',
          amount: testCase.docAmount,
          currency: 'USD',
          metadata: {}
        };

        const result = await engine.evaluate(workflow, document);
        const hasApprovers = result.approvers.length > 0;

        expect(hasApprovers).toBe(testCase.expected);
      }
    });

    it('should evaluate logical operators correctly', async () => {
      const andPredicate: ExpressionNode = {
        type: 'logical',
        operator: 'AND',
        children: [
          {
            type: 'comparison',
            field: 'amount',
            operator: 'gt',
            value: 1000
          },
          {
            type: 'comparison',
            field: 'currency',
            operator: 'eq',
            value: 'USD'
          }
        ]
      };

      const workflow: WorkflowDefinition = {
        id: 'test',
        name: 'Test',
        description: '',
        version: '1.0.0',
        documentType: 'INVOICE',
        status: 'ACTIVE',
        steps: [
          {
            id: 'step-1',
            sequence: 1,
            name: 'Test Step',
            predicate: andPredicate,
            approverRoleIds: ['test'],
            approvalMode: 'SERIAL'
          }
        ],
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        createdBy: 'test'
      };

      // Should match (amount > 1000 AND currency = USD)
      const matchingDoc: DocumentContext = {
        id: 'test-doc-1',
        type: 'INVOICE',
        amount: 2000,
        currency: 'USD',
        metadata: {}
      };

      const matchingResult = await engine.evaluate(workflow, matchingDoc);
      expect(matchingResult.approvers.length).toBe(1);

      // Should not match (amount > 1000 but currency != USD)
      const nonMatchingDoc: DocumentContext = {
        id: 'test-doc-2',
        type: 'INVOICE',
        amount: 2000,
        currency: 'EUR',
        metadata: {}
      };

      const nonMatchingResult = await engine.evaluate(workflow, nonMatchingDoc);
      expect(nonMatchingResult.approvers.length).toBe(0);
    });

    it('should handle nested field access', async () => {
      const workflow: WorkflowDefinition = {
        id: 'test',
        name: 'Test',
        description: '',
        version: '1.0.0',
        documentType: 'INVOICE',
        status: 'ACTIVE',
        steps: [
          {
            id: 'step-1',
            sequence: 1,
            name: 'Test Step',
            predicate: {
              type: 'comparison',
              field: 'supplier.country',
              operator: 'eq',
              value: 'US'
            },
            approverRoleIds: ['test'],
            approvalMode: 'SERIAL'
          }
        ],
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        createdBy: 'test'
      };

      const document: DocumentContext = {
        id: 'test-doc',
        type: 'INVOICE',
        amount: 1000,
        currency: 'USD',
        supplier: 'Test Supplier',
        metadata: {},
        country: 'US' // This should be accessed via supplier.country in a real scenario
      };

      const result = await engine.evaluate(workflow, document);
      // This test demonstrates the field access pattern
      expect(result).toBeDefined();
    });
  });

  describe('performance', () => {
    it('should evaluate workflows within 5ms performance target', async () => {
      const workflow: WorkflowDefinition = {
        id: 'perf-test',
        name: 'Performance Test',
        description: '',
        version: '1.0.0',
        documentType: 'INVOICE',
        status: 'ACTIVE',
        steps: [
          {
            id: 'step-1',
            sequence: 1,
            name: 'Test Step',
            predicate: {
              type: 'comparison',
              field: 'amount',
              operator: 'lt',
              value: 5000
            },
            approverRoleIds: ['test'],
            approvalMode: 'SERIAL'
          }
        ],
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        createdBy: 'test'
      };

      const document: DocumentContext = {
        id: 'perf-doc',
        type: 'INVOICE',
        amount: 2500,
        currency: 'USD',
        metadata: {}
      };

      const result = await engine.evaluate(workflow, document);

      expect(result.evaluationTimeMs).toBeLessThan(5);
    });
  });
});

describe('WorkflowUtils', () => {
  describe('createAmountPredicate', () => {
    it('should create correct amount predicate', () => {
      const predicate = WorkflowUtils.createAmountPredicate('gte', 1000);

      expect(predicate).toEqual({
        type: 'comparison',
        field: 'amount',
        operator: 'gte',
        value: 1000
      });
    });
  });

  describe('combineWithAnd', () => {
    it('should combine multiple predicates with AND logic', () => {
      const predicate1 = WorkflowUtils.createAmountPredicate('gt', 1000);
      const predicate2 = WorkflowUtils.createCurrencyPredicate(['USD', 'EUR']);

      const combined = WorkflowUtils.combineWithAnd([predicate1, predicate2]);

      expect(combined).toEqual({
        type: 'logical',
        operator: 'AND',
        children: [predicate1, predicate2]
      });
    });

    it('should return single predicate when only one provided', () => {
      const predicate = WorkflowUtils.createAmountPredicate('gt', 1000);
      const result = WorkflowUtils.combineWithAnd([predicate]);

      expect(result).toEqual(predicate);
    });
  });

  describe('validateWorkflow', () => {
    it('should validate correct workflow', () => {
      const workflow: WorkflowDefinition = {
        id: 'test',
        name: 'Test Workflow',
        description: '',
        version: '1.0.0',
        documentType: 'INVOICE',
        status: 'ACTIVE',
        steps: [
          {
            id: 'step-1',
            sequence: 1,
            name: 'Test Step',
            predicate: {
              type: 'comparison',
              field: 'amount',
              operator: 'lt',
              value: 5000
            },
            approverRoleIds: ['test'],
            approvalMode: 'SERIAL'
          }
        ],
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        createdBy: 'test'
      };

      const errors = WorkflowUtils.validateWorkflow(workflow);
      expect(errors).toHaveLength(0);
    });

    it('should detect duplicate step sequences', () => {
      const workflow: WorkflowDefinition = {
        id: 'test',
        name: 'Test Workflow',
        description: '',
        version: '1.0.0',
        documentType: 'INVOICE',
        status: 'ACTIVE',
        steps: [
          {
            id: 'step-1',
            sequence: 1,
            name: 'Test Step 1',
            predicate: { type: 'comparison', field: 'amount', operator: 'lt', value: 5000 },
            approverRoleIds: ['test'],
            approvalMode: 'SERIAL'
          },
          {
            id: 'step-2',
            sequence: 1, // Duplicate sequence
            name: 'Test Step 2',
            predicate: { type: 'comparison', field: 'amount', operator: 'gte', value: 5000 },
            approverRoleIds: ['test'],
            approvalMode: 'SERIAL'
          }
        ],
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        createdBy: 'test'
      };

      const errors = WorkflowUtils.validateWorkflow(workflow);
      expect(errors).toContain('Duplicate step sequences found');
    });
  });
});

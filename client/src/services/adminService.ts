import { Organization, Member, ApprovalPredicate } from "@/types/admin";

// Helper function for API requests
async function apiRequest<T>(url: string, options?: RequestInit): Promise<T> {
  const response = await fetch(url, {
    headers: {
      "Content-Type": "application/json",
    },
    ...options,
  });
  
  if (!response.ok) {
    const error = await response.json().catch(() => ({}));
    throw new Error(error.message || "An error occurred with the request");
  }
  
  return response.json();
}

export const adminService = {
  // Organization endpoints
  getOrganization: async (): Promise<Organization> => {
    return apiRequest<Organization>("/api/admin/organization");
  },
  
  updateOrganization: async (data: Organization): Promise<Organization> => {
    return apiRequest<Organization>("/api/admin/organization", {
      method: "PUT",
      body: JSON.stringify(data),
    });
  },
  
  // Member endpoints
  getMembers: async (): Promise<Member[]> => {
    return apiRequest<Member[]>("/api/admin/members");
  },
  
  createMember: async (data: Omit<Member, "id">): Promise<Member> => {
    return apiRequest<Member>("/api/admin/members", {
      method: "POST",
      body: JSON.stringify(data),
    });
  },
  
  updateMember: async (id: string, data: Partial<Member>): Promise<Member> => {
    return apiRequest<Member>(`/api/admin/members/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  },
  
  disableMember: async (id: string): Promise<Member> => {
    return apiRequest<Member>(`/api/admin/members/${id}/disable`, {
      method: "PATCH",
    });
  },
  
  resendInvite: async (id: string): Promise<void> => {
    return apiRequest<void>(`/api/admin/members/${id}/resend-invite`, {
      method: "POST",
    });
  },
  
  // Approval predicate endpoints
  getApprovalPredicate: async (): Promise<ApprovalPredicate> => {
    return apiRequest<ApprovalPredicate>("/api/admin/approval-predicate");
  },
  
  updateApprovalPredicate: async (data: ApprovalPredicate): Promise<ApprovalPredicate> => {
    return apiRequest<ApprovalPredicate>("/api/admin/approval-predicate", {
      method: "PUT",
      body: JSON.stringify(data),
    });
  },
};
import { 
  <PERSON>P<PERSON><PERSON>or, 
  ERPImportResult, 
  WorkflowDefinition, 
  WorkflowStep,
  ExpressionNode,
  FieldMapping 
} from "@/types/workflow";

/**
 * ERP Parser Service
 * 
 * Handles parsing of workflow definitions from various ERP systems
 * into our canonical workflow format. Supports multiple file formats
 * and provides field mapping capabilities.
 */

export interface ParsedWorkflow {
  name: string;
  description?: string;
  steps: ParsedStep[];
  metadata: Record<string, any>;
}

export interface ParsedStep {
  name: string;
  sequence: number;
  conditions: ParsedCondition[];
  approvers: string[];
  mode: "SERIAL" | "PARALLEL";
  autoApprove?: boolean;
  timeout?: number;
}

export interface ParsedCondition {
  field: string;
  operator: string;
  value: any;
  logic?: "AND" | "OR";
}

/**
 * SAP Parser
 * Handles SAP workflow definitions from various formats
 */
export class SAPParser {
  static parse(content: string, format: string): ParsedWorkflow[] {
    try {
      switch (format.toLowerCase()) {
        case 'json':
          return this.parseJSON(content);
        case 'xml':
          return this.parseXML(content);
        default:
          throw new Error(`Unsupported SAP format: ${format}`);
      }
    } catch (error) {
      throw new Error(`SAP parsing failed: ${error.message}`);
    }
  }
  
  private static parseJSON(content: string): ParsedWorkflow[] {
    const data = JSON.parse(content);
    
    // Handle SAP BPM JSON format
    if (data.workflowDefinitions) {
      return data.workflowDefinitions.map((wf: any) => ({
        name: wf.name || wf.id,
        description: wf.description,
        steps: this.parseSAPSteps(wf.steps || wf.tasks),
        metadata: {
          sapId: wf.id,
          version: wf.version,
          category: wf.category
        }
      }));
    }
    
    // Handle single workflow
    return [{
      name: data.name || "SAP Workflow",
      description: data.description,
      steps: this.parseSAPSteps(data.steps || data.tasks || []),
      metadata: { sapId: data.id }
    }];
  }
  
  private static parseXML(content: string): ParsedWorkflow[] {
    // Basic XML parsing for SAP workflow definitions
    // In production, use a proper XML parser like DOMParser
    const workflows: ParsedWorkflow[] = [];
    
    // Extract workflow definitions from XML
    const workflowMatches = content.match(/<workflow[^>]*>(.*?)<\/workflow>/gs);
    
    if (workflowMatches) {
      workflowMatches.forEach(match => {
        const nameMatch = match.match(/name="([^"]+)"/);
        const name = nameMatch ? nameMatch[1] : "SAP Workflow";
        
        workflows.push({
          name,
          description: `Imported from SAP XML`,
          steps: this.parseXMLSteps(match),
          metadata: { format: 'xml' }
        });
      });
    }
    
    return workflows;
  }
  
  private static parseSAPSteps(steps: any[]): ParsedStep[] {
    return steps.map((step, index) => ({
      name: step.name || step.taskName || `Step ${index + 1}`,
      sequence: step.sequence || index + 1,
      conditions: this.parseSAPConditions(step.conditions || step.rules || []),
      approvers: this.extractApprovers(step.approvers || step.agents || []),
      mode: step.mode === "parallel" ? "PARALLEL" : "SERIAL",
      autoApprove: step.autoApprove || false,
      timeout: step.timeout || step.timeoutHours
    }));
  }
  
  private static parseXMLSteps(xmlContent: string): ParsedStep[] {
    // Basic XML step parsing
    const stepMatches = xmlContent.match(/<step[^>]*>(.*?)<\/step>/gs);
    
    if (!stepMatches) return [];
    
    return stepMatches.map((match, index) => {
      const nameMatch = match.match(/name="([^"]+)"/);
      return {
        name: nameMatch ? nameMatch[1] : `Step ${index + 1}`,
        sequence: index + 1,
        conditions: [],
        approvers: [],
        mode: "SERIAL" as const
      };
    });
  }
  
  private static parseSAPConditions(conditions: any[]): ParsedCondition[] {
    return conditions.map(cond => ({
      field: this.mapSAPField(cond.field || cond.attribute),
      operator: this.mapSAPOperator(cond.operator || cond.condition),
      value: cond.value,
      logic: cond.logic
    }));
  }
  
  private static mapSAPField(sapField: string): string {
    const fieldMap: Record<string, string> = {
      'CEKKO-WRBTR': 'amount',
      'CEKKO-WAERS': 'currency',
      'CEKKO-LIFNR': 'supplier.id',
      'CEKKO-BUKRS': 'organization.costCenter'
    };
    
    return fieldMap[sapField] || sapField;
  }
  
  private static mapSAPOperator(sapOp: string): string {
    const opMap: Record<string, string> = {
      'EQ': 'eq',
      'NE': 'ne',
      'GT': 'gt',
      'GE': 'gte',
      'LT': 'lt',
      'LE': 'lte'
    };
    
    return opMap[sapOp] || 'eq';
  }
  
  private static extractApprovers(approvers: any[]): string[] {
    return approvers.map(app => app.id || app.userId || app.role).filter(Boolean);
  }
}

/**
 * Oracle Parser
 * Handles Oracle BPM and Fusion workflow definitions
 */
export class OracleParser {
  static parse(content: string, format: string): ParsedWorkflow[] {
    try {
      switch (format.toLowerCase()) {
        case 'json':
          return this.parseJSON(content);
        case 'xlsx':
          return this.parseExcel(content);
        default:
          throw new Error(`Unsupported Oracle format: ${format}`);
      }
    } catch (error) {
      throw new Error(`Oracle parsing failed: ${error.message}`);
    }
  }
  
  private static parseJSON(content: string): ParsedWorkflow[] {
    const data = JSON.parse(content);
    
    // Handle Oracle BPM format
    if (data.processes) {
      return data.processes.map((process: any) => ({
        name: process.name,
        description: process.description,
        steps: this.parseOracleSteps(process.activities || []),
        metadata: {
          oracleId: process.id,
          version: process.version
        }
      }));
    }
    
    return [{
      name: data.name || "Oracle Workflow",
      description: data.description,
      steps: this.parseOracleSteps(data.activities || []),
      metadata: { oracleId: data.id }
    }];
  }
  
  private static parseExcel(content: string): ParsedWorkflow[] {
    // In production, use a library like xlsx to parse Excel files
    // For now, assume CSV-like content
    const lines = content.split('\n');
    const headers = lines[0]?.split(',') || [];
    
    const workflows: ParsedWorkflow[] = [];
    
    // Group rows by workflow name
    const workflowGroups = new Map<string, any[]>();
    
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',');
      if (values.length >= headers.length) {
        const row = Object.fromEntries(headers.map((h, idx) => [h.trim(), values[idx]?.trim()]));
        const workflowName = row['Workflow Name'] || 'Oracle Workflow';
        
        if (!workflowGroups.has(workflowName)) {
          workflowGroups.set(workflowName, []);
        }
        workflowGroups.get(workflowName)!.push(row);
      }
    }
    
    // Convert groups to workflows
    workflowGroups.forEach((rows, name) => {
      workflows.push({
        name,
        description: `Imported from Oracle Excel`,
        steps: this.parseExcelSteps(rows),
        metadata: { format: 'excel' }
      });
    });
    
    return workflows;
  }
  
  private static parseOracleSteps(activities: any[]): ParsedStep[] {
    return activities
      .filter(activity => activity.type === 'approval' || activity.type === 'human')
      .map((activity, index) => ({
        name: activity.name || `Step ${index + 1}`,
        sequence: activity.sequence || index + 1,
        conditions: this.parseOracleConditions(activity.conditions || []),
        approvers: activity.assignees || [],
        mode: activity.mode === "parallel" ? "PARALLEL" : "SERIAL",
        autoApprove: activity.autoComplete || false,
        timeout: activity.timeout
      }));
  }
  
  private static parseExcelSteps(rows: any[]): ParsedStep[] {
    return rows.map((row, index) => ({
      name: row['Step Name'] || `Step ${index + 1}`,
      sequence: parseInt(row['Sequence']) || index + 1,
      conditions: [{
        field: 'amount',
        operator: 'gte',
        value: parseFloat(row['Min Amount']) || 0
      }],
      approvers: [row['Approver Role']].filter(Boolean),
      mode: row['Mode'] === 'Parallel' ? "PARALLEL" : "SERIAL"
    }));
  }
  
  private static parseOracleConditions(conditions: any[]): ParsedCondition[] {
    return conditions.map(cond => ({
      field: this.mapOracleField(cond.attribute),
      operator: this.mapOracleOperator(cond.operator),
      value: cond.value,
      logic: cond.logic
    }));
  }
  
  private static mapOracleField(oracleField: string): string {
    const fieldMap: Record<string, string> = {
      'invoice.amount': 'amount',
      'invoice.currency': 'currency',
      'supplier.id': 'supplier.id',
      'cost.center': 'organization.costCenter'
    };
    
    return fieldMap[oracleField] || oracleField;
  }
  
  private static mapOracleOperator(oracleOp: string): string {
    const opMap: Record<string, string> = {
      'equals': 'eq',
      'not_equals': 'ne',
      'greater_than': 'gt',
      'greater_equal': 'gte',
      'less_than': 'lt',
      'less_equal': 'lte'
    };
    
    return opMap[oracleOp] || 'eq';
  }
}

/**
 * Main ERP Parser Factory
 */
export class ERPParserFactory {
  static async parseFile(
    vendor: ERPVendor, 
    content: string, 
    filename: string
  ): Promise<ERPImportResult> {
    const format = this.detectFormat(filename);
    let parsedWorkflows: ParsedWorkflow[] = [];
    const warnings: string[] = [];
    const errors: string[] = [];
    
    try {
      switch (vendor) {
        case "SAP":
          parsedWorkflows = SAPParser.parse(content, format);
          break;
        case "Oracle":
          parsedWorkflows = OracleParser.parse(content, format);
          break;
        case "Dynamics365":
          // TODO: Implement Dynamics365 parser
          warnings.push("Dynamics365 parser not yet implemented");
          break;
        case "NetSuite":
          // TODO: Implement NetSuite parser
          warnings.push("NetSuite parser not yet implemented");
          break;
        default:
          throw new Error(`Unsupported vendor: ${vendor}`);
      }
      
      // Convert parsed workflows to our canonical format
      const workflows = parsedWorkflows.map(pw => this.convertToCanonical(pw, vendor));
      
      return {
        vendor,
        format,
        workflows,
        fieldMappings: this.generateFieldMappings(vendor),
        warnings,
        errors
      };
      
    } catch (error) {
      errors.push(error.message);
      return {
        vendor,
        format,
        workflows: [],
        fieldMappings: {},
        warnings,
        errors
      };
    }
  }
  
  private static detectFormat(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'json': return 'json';
      case 'xml': return 'xml';
      case 'xlsx': case 'xls': return 'xlsx';
      case 'csv': return 'csv';
      default: return 'unknown';
    }
  }
  
  private static convertToCanonical(
    parsed: ParsedWorkflow, 
    vendor: ERPVendor
  ): Partial<WorkflowDefinition> {
    return {
      name: parsed.name,
      description: parsed.description,
      version: "1.0.0",
      documentType: "INVOICE", // Default, can be configured
      status: "DRAFT",
      steps: parsed.steps.map(step => this.convertStep(step)),
      source: {
        vendor,
        importedAt: new Date().toISOString(),
        originalFormat: 'imported'
      }
    };
  }
  
  private static convertStep(step: ParsedStep): WorkflowStep {
    return {
      id: `step-${step.sequence}`,
      sequence: step.sequence,
      name: step.name,
      predicate: this.convertConditions(step.conditions),
      approverRoleIds: step.approvers,
      approvalMode: step.mode,
      autoApprove: step.autoApprove,
      timeoutHours: step.timeout
    };
  }
  
  private static convertConditions(conditions: ParsedCondition[]): ExpressionNode {
    if (conditions.length === 0) {
      // Default condition that always matches
      return {
        type: "comparison",
        field: "amount",
        operator: "gte",
        value: 0
      };
    }
    
    if (conditions.length === 1) {
      return {
        type: "comparison",
        field: conditions[0].field,
        operator: conditions[0].operator as any,
        value: conditions[0].value
      };
    }
    
    // Multiple conditions - combine with AND by default
    return {
      type: "logical",
      operator: "AND",
      children: conditions.map(cond => ({
        type: "comparison" as const,
        field: cond.field,
        operator: cond.operator as any,
        value: cond.value
      }))
    };
  }
  
  private static generateFieldMappings(vendor: ERPVendor): Record<string, string> {
    const mappings: Record<ERPVendor, Record<string, string>> = {
      SAP: {
        'CEKKO-WRBTR': 'amount',
        'CEKKO-WAERS': 'currency',
        'CEKKO-LIFNR': 'supplier.id',
        'CEKKO-BUKRS': 'organization.costCenter'
      },
      Oracle: {
        'invoice.amount': 'amount',
        'invoice.currency': 'currency',
        'supplier.id': 'supplier.id',
        'cost.center': 'organization.costCenter'
      },
      Dynamics365: {
        'Amount': 'amount',
        'CurrencyCode': 'currency',
        'VendorAccount': 'supplier.id'
      },
      NetSuite: {
        'amount': 'amount',
        'currency': 'currency',
        'vendor': 'supplier.id'
      }
    };
    
    return mappings[vendor] || {};
  }
}

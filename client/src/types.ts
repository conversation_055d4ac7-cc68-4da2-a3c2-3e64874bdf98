/**
 * Client-side type definitions
 * 
 * This file contains TypeScript interfaces for use in the client application,
 * particularly for state management and component props.
 */

import { Payment as DbPayment } from "@shared/schema";

/**
 * Payment interface that extends the database Payment type
 * with additional client-side properties.
 */
export interface Payment extends DbPayment {
  // Add nullable signature and message fields for BLS signatures
  // Using null instead of undefined to match the database schema
  signature: string | null;
  message: string | null;
}

/**
 * Type definition for transitioning payments in the UI
 * Used for animations when payments move between columns
 */
export interface TransitioningPayment {
  id: number;
  fromStatus: string;
  toStatus: string;
  startTime: number;
}
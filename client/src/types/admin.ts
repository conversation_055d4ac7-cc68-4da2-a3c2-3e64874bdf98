// Role and tier types
export type Role = "Approver" | "Accounts Payable" | "Accounts Receivable";
export type ApproverTier = "Tier 1" | "Tier 2";

// Member interface
export interface Member {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: Role;
  tier?: ApproverTier;
  filters: string[];  // customer account names
  status: "Active" | "Pending" | "Disabled";
}

// Organization interface
export interface Organization {
  legalName: string;
  displayName: string;
  accountNumber: string;
  emailDomain: string;
  industry: string;
  fiscalCurrency: string;
  erpBaseUrl: string;
  erpApiKey: string;
}

// Approval predicate interface
export interface ApprovalPredicate {
  tier1: string;  // member.id or email
  tier2: string;  // member.id or email
  accountOwner: string;  // member.id or email
}

// Demo data
export const demoData = {
  organization: {
    legalName: "Global Logistics Ltd.",
    displayName: "GlobalLogi",
    accountNumber: "GL-001",
    emailDomain: "globallogistics.com",
    industry: "Logistics & Supply Chain",
    fiscalCurrency: "USD",
    erpBaseUrl: "https://sap-gl-prod.example.com",
    erpApiKey: "demo-erp-api-key"
  },
  members: [
    { 
      id: "1", 
      email: "<EMAIL>", 
      firstName: "Maria", 
      lastName: "Hughes", 
      role: "Approver" as Role, 
      tier: "Tier 1" as ApproverTier, 
      filters: ["Boeing", "Mod Pizza"], 
      status: "Active" as const 
    },
    { 
      id: "2", 
      email: "<EMAIL>", 
      firstName: "David", 
      lastName: "Nguyen", 
      role: "Approver" as Role, 
      tier: "Tier 2" as ApproverTier, 
      filters: ["Kratos Defense"], 
      status: "Active" as const 
    },
    { 
      id: "3", 
      email: "<EMAIL>", 
      firstName: "Alex", 
      lastName: "Choi", 
      role: "Accounts Payable" as Role, 
      filters: ["All"], 
      status: "Active" as const 
    },
    { 
      id: "4", 
      email: "<EMAIL>", 
      firstName: "Sara", 
      lastName: "Wilson", 
      role: "Accounts Receivable" as Role, 
      filters: ["Bumble Bee Foods"], 
      status: "Pending" as const 
    }
  ],
  approvalPredicate: {
    tier1: "<EMAIL>",
    tier2: "<EMAIL>",
    accountOwner: "<EMAIL>"
  }
};

// Validation utilities
export const validateEmail = (email: string, domain: string): boolean => {
  if (!email || !domain) return false;
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) return false;
  
  const emailDomain = email.split('@')[1];
  return emailDomain.toLowerCase() === domain.toLowerCase();
};

export const validateApprovalPredicate = (
  predicate: ApprovalPredicate
): boolean => {
  const { tier1, tier2, accountOwner } = predicate;
  
  // Check if all fields are filled
  if (!tier1 || !tier2 || !accountOwner) return false;
  
  // Check if all approvers are unique
  return tier1 !== tier2 && tier1 !== accountOwner && tier2 !== accountOwner;
};
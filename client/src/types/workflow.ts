// Workflow Engine Types
// This file defines the complete type system for the pluggable workflow engine

export type ERPVendor = "SAP" | "Oracle" | "Dynamics365" | "NetSuite";

export type ComparisonOperator = "eq" | "ne" | "gt" | "gte" | "lt" | "lte" | "in" | "contains" | "startsWith" | "endsWith";

export type LogicalOperator = "AND" | "OR";

export type ApprovalMode = "SERIAL" | "PARALLEL";

export type DocumentType = "INVOICE" | "PAYMENT" | "PURCHASE_ORDER" | "EXPENSE_REPORT";

export type WorkflowStatus = "ACTIVE" | "INACTIVE" | "DRAFT";

// Expression tree for complex rule evaluation
export interface ExpressionNode {
  type: "comparison" | "logical";
  field?: string;
  operator?: ComparisonOperator | LogicalOperator;
  value?: any;
  children?: ExpressionNode[];
}

// Workflow step definition
export interface WorkflowStep {
  id: string;
  sequence: number;
  name: string;
  predicate: ExpressionNode;
  approverRoleIds: string[];
  approvalMode: ApprovalMode;
  autoApprove?: boolean;
  timeoutHours?: number;
  escalationRoleIds?: string[];
}

// Main workflow definition
export interface WorkflowDefinition {
  id: string;
  name: string;
  description?: string;
  version: string;
  documentType: DocumentType;
  status: WorkflowStatus;
  steps: WorkflowStep[];
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  source?: {
    vendor: ERPVendor;
    importedAt: string;
    originalFormat: string;
    mappingId?: string;
  };
}

// Field mapping for ERP imports
export interface FieldMapping {
  id: string;
  name: string;
  vendor: ERPVendor;
  mappings: Record<string, string>; // vendor field -> canonical field
  createdAt: string;
  updatedAt: string;
}

// Document evaluation context
export interface DocumentContext {
  id: string;
  type: DocumentType;
  amount: number;
  currency: string;
  supplier?: string;
  customer?: string;
  costCenter?: string;
  department?: string;
  country?: string;
  urgency?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  riskScore?: number;
  metadata: Record<string, any>;
}

// Evaluation result
export interface EvaluationResult {
  workflowId: string;
  documentId: string;
  approvers: ApprovalStep[];
  autoApproved: boolean;
  estimatedCompletionTime?: number; // hours
  evaluatedAt: string;
  evaluationTimeMs: number;
}

export interface ApprovalStep {
  stepId: string;
  sequence: number;
  approverUserIds: string[];
  approvalMode: ApprovalMode;
  status: "PENDING" | "APPROVED" | "REJECTED" | "TIMEOUT" | "ESCALATED";
  requiredApprovals?: number;
  currentApprovals?: number;
  timeoutAt?: string;
}

// Import connector configuration
export interface ImportConnector {
  id: string;
  name: string;
  vendor: ERPVendor;
  type: "API" | "FILE";
  config: {
    baseUrl?: string;
    apiKey?: string;
    username?: string;
    password?: string;
    pollIntervalMinutes?: number;
    enabled: boolean;
  };
  lastSync?: string;
  status: "ACTIVE" | "ERROR" | "DISABLED";
  errorMessage?: string;
}

// Workflow version history
export interface WorkflowVersion {
  id: string;
  workflowId: string;
  version: string;
  definition: WorkflowDefinition;
  createdAt: string;
  createdBy: string;
  changeLog?: string;
}

// Simulation request/response
export interface SimulationRequest {
  workflowId: string;
  document: DocumentContext;
}

export interface SimulationResponse {
  result: EvaluationResult;
  executionPath: {
    stepId: string;
    stepName: string;
    matched: boolean;
    reason?: string;
  }[];
  warnings: string[];
}

// Audit event
export interface WorkflowAuditEvent {
  id: string;
  eventType: "WORKFLOW_CREATED" | "WORKFLOW_UPDATED" | "WORKFLOW_EVALUATED" | "APPROVAL_GRANTED" | "APPROVAL_REJECTED";
  workflowId: string;
  documentId?: string;
  userId: string;
  timestamp: string;
  details: Record<string, any>;
}

// ERP-specific import formats
export interface ERPImportResult {
  vendor: ERPVendor;
  format: string;
  workflows: Partial<WorkflowDefinition>[];
  fieldMappings: Record<string, string>;
  warnings: string[];
  errors: string[];
}

// Canonical field definitions for mapping
export const CANONICAL_FIELDS = {
  // Document fields
  "document.id": "Document ID",
  "document.type": "Document Type",
  "document.amount": "Amount",
  "document.currency": "Currency",
  "document.date": "Document Date",
  "document.dueDate": "Due Date",
  
  // Party fields
  "supplier.id": "Supplier ID",
  "supplier.name": "Supplier Name",
  "supplier.country": "Supplier Country",
  "supplier.riskScore": "Supplier Risk Score",
  "customer.id": "Customer ID",
  "customer.name": "Customer Name",
  "customer.country": "Customer Country",
  
  // Organization fields
  "organization.costCenter": "Cost Center",
  "organization.department": "Department",
  "organization.division": "Division",
  "organization.location": "Location",
  
  // Workflow fields
  "workflow.urgency": "Urgency Level",
  "workflow.category": "Category",
  "workflow.subcategory": "Subcategory",
  "workflow.tags": "Tags",
} as const;

export type CanonicalField = keyof typeof CANONICAL_FIELDS;

// Validation utilities
export const validateWorkflowDefinition = (workflow: Partial<WorkflowDefinition>): string[] => {
  const errors: string[] = [];
  
  if (!workflow.name?.trim()) {
    errors.push("Workflow name is required");
  }
  
  if (!workflow.documentType) {
    errors.push("Document type is required");
  }
  
  if (!workflow.steps || workflow.steps.length === 0) {
    errors.push("At least one workflow step is required");
  }
  
  if (workflow.steps) {
    workflow.steps.forEach((step, index) => {
      if (!step.name?.trim()) {
        errors.push(`Step ${index + 1}: Name is required`);
      }
      
      if (!step.approverRoleIds || step.approverRoleIds.length === 0) {
        errors.push(`Step ${index + 1}: At least one approver role is required`);
      }
      
      if (!step.predicate) {
        errors.push(`Step ${index + 1}: Predicate is required`);
      }
    });
  }
  
  return errors;
};

export const validateExpressionNode = (node: ExpressionNode): string[] => {
  const errors: string[] = [];
  
  if (node.type === "comparison") {
    if (!node.field) {
      errors.push("Comparison node must have a field");
    }
    if (!node.operator) {
      errors.push("Comparison node must have an operator");
    }
    if (node.value === undefined || node.value === null) {
      errors.push("Comparison node must have a value");
    }
  } else if (node.type === "logical") {
    if (!node.operator || !["AND", "OR"].includes(node.operator)) {
      errors.push("Logical node must have AND or OR operator");
    }
    if (!node.children || node.children.length < 2) {
      errors.push("Logical node must have at least 2 children");
    }
    if (node.children) {
      node.children.forEach((child, index) => {
        const childErrors = validateExpressionNode(child);
        errors.push(...childErrors.map(err => `Child ${index + 1}: ${err}`));
      });
    }
  }
  
  return errors;
};

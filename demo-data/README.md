# Demo Workflow Rules Package

This package contains sample workflow definitions from various ERP systems to demonstrate the Workflow Engine's import capabilities.

## Files Included

### 1. `sap_release_strategy.json`
- **Source**: SAP S/4HANA Release Strategy
- **Format**: JSON
- **Contains**: 
  - Invoice approval workflow with 4 steps
  - Payment approval workflow with dual authorization
  - Field mappings for SAP-specific fields (CEKKO-*, REGUH-*)
  - Auto-approval rules for small amounts

### 2. `oracle_bpm.xlsx` 
- **Source**: Oracle BPM Suite
- **Format**: Excel/CSV
- **Contains**:
  - Invoice approval workflow (4 steps)
  - Payment authorization workflow (2 steps) 
  - Purchase order workflow (2 steps)
  - Structured as tabular data with columns for workflow steps

### 3. `d365_workflow.xml`
- **Source**: Microsoft Dynamics 365 Finance & Operations
- **Format**: XML
- **Contains**:
  - Invoice approval workflow (3 steps)
  - Expense report workflow (3 steps including auto-approval)
  - XML schema-based definition with conditions and approvers

### 4. `netsuite_bundle.xml`
- **Source**: NetSuite SuiteFlow
- **Format**: XML
- **Contains**:
  - Bill approval workflow (state-based)
  - Purchase order workflow
  - NetSuite-specific state machine format with transitions

## Demo Users Mapping

All workflows are pre-configured to map to the demo users:

- **Maria Hughes** (`<EMAIL>`) - Tier 1 / Manager / Department Manager
- **David Nguyen** (`<EMAIL>`) - Tier 2 / Director / Finance Manager  
- **Alex Choi** (`<EMAIL>`) - Account Owner / CFO / Executive

## Usage Instructions

1. **Upload via Admin Interface**:
   - Go to Admin → Workflow Engine → Import & Connectors
   - Select the appropriate ERP vendor
   - Drag and drop the files or click to upload
   - Follow the mapping wizard to map vendor fields to canonical fields

2. **Test with Simulation**:
   - Go to Admin → Workflow Engine → Simulation Sandbox
   - Select an imported workflow
   - Use the sample documents to test approval routing:
     - Low value invoice ($2,500) → Manager approval
     - High value invoice ($75,000) → Multi-tier approval
     - Foreign invoice (€150,000) → Executive approval

3. **Expected Behavior**:
   - **Small amounts** (< $100 from trusted vendors) → Auto-approved
   - **Medium amounts** ($100 - $5,000) → Single manager approval
   - **Large amounts** ($5,000 - $25,000) → Director/Finance approval
   - **Very large amounts** (> $25,000) → Executive/CFO approval

## Field Mappings

### SAP Mappings
- `CEKKO-WRBTR` → `amount` (Invoice amount)
- `CEKKO-WAERS` → `currency` (Currency)
- `CEKKO-LIFNR` → `supplier.id` (Vendor number)
- `CEKKO-BUKRS` → `organization.costCenter` (Company code)

### Oracle Mappings  
- `invoice.amount` → `amount`
- `invoice.currency` → `currency`
- `supplier.id` → `supplier.id`
- `cost.center` → `organization.costCenter`

### Dynamics 365 Mappings
- `Amount` → `amount`
- `CurrencyCode` → `currency`
- `VendorAccount` → `supplier.id`

### NetSuite Mappings
- `amount` → `amount`
- `currency` → `currency`
- `vendor` → `supplier.id`
- `subsidiary` → `organization.costCenter`

## Testing Scenarios

### Scenario 1: Low Value Invoice
```json
{
  "id": "INV-001",
  "type": "INVOICE",
  "amount": 2500,
  "currency": "USD",
  "supplier": "Acme Corp"
}
```
**Expected**: Single manager approval (Maria Hughes)

### Scenario 2: High Value Invoice  
```json
{
  "id": "INV-002",
  "type": "INVOICE", 
  "amount": 75000,
  "currency": "USD",
  "supplier": "Enterprise Solutions Inc"
}
```
**Expected**: Multi-tier approval (David Nguyen → Alex Choi)

### Scenario 3: Foreign Currency
```json
{
  "id": "INV-003",
  "type": "INVOICE",
  "amount": 150000,
  "currency": "EUR", 
  "supplier": "European Vendor GmbH"
}
```
**Expected**: Executive approval (Alex Choi)

### Scenario 4: Auto-Approval
```json
{
  "id": "INV-004",
  "type": "INVOICE",
  "amount": 75,
  "currency": "USD",
  "supplier": "VENDOR_001"
}
```
**Expected**: Auto-approved (no human intervention)

## Performance Expectations

- **Import time**: < 5 seconds per file
- **Evaluation time**: < 5ms per document
- **Field mapping**: Interactive wizard with validation
- **Version control**: Full audit trail with rollback capability

## Compliance Features

- **Audit logging**: All evaluations and approvals tracked
- **Version history**: Complete change tracking with diffs
- **Role-based access**: Integration with existing RBAC system
- **Timeout handling**: Automatic escalation for overdue approvals
- **Dual authorization**: Parallel approval support for high-risk transactions

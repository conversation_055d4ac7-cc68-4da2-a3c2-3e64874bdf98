<?xml version="1.0" encoding="UTF-8"?>
<WorkflowDefinitions xmlns="http://schemas.microsoft.com/dynamics/2023/workflow">
  <Workflow id="D365_INVOICE_001" name="Dynamics 365 Invoice Approval" version="1.0">
    <Description>Standard invoice approval workflow for Dynamics 365 Finance</Description>
    <DocumentType>Invoice</DocumentType>
    <Steps>
      <Step id="STEP_001" sequence="1" name="Department Approval">
        <Conditions>
          <Condition field="Amount" operator="LessThan" value="5000" />
          <Condition field="CurrencyCode" operator="Equals" value="USD" />
        </Conditions>
        <Approvers>
          <Approver role="Department Manager" userId="<EMAIL>" />
        </Approvers>
        <Mode>Serial</Mode>
        <TimeoutHours>24</TimeoutHours>
        <AutoApprove>false</AutoApprove>
      </Step>
      <Step id="STEP_002" sequence="2" name="Finance Approval">
        <Conditions>
          <Condition field="Amount" operator="GreaterThanOrEqual" value="5000" />
          <Condition field="Amount" operator="LessThan" value="25000" />
        </Conditions>
        <Approvers>
          <Approver role="Finance Manager" userId="<EMAIL>" />
        </Approvers>
        <Mode>Serial</Mode>
        <TimeoutHours>48</TimeoutHours>
        <AutoApprove>false</AutoApprove>
      </Step>
      <Step id="STEP_003" sequence="3" name="Executive Approval">
        <Conditions>
          <Condition field="Amount" operator="GreaterThanOrEqual" value="25000" />
        </Conditions>
        <Approvers>
          <Approver role="Executive" userId="<EMAIL>" />
        </Approvers>
        <Mode>Serial</Mode>
        <TimeoutHours>72</TimeoutHours>
        <AutoApprove>false</AutoApprove>
      </Step>
    </Steps>
    <Metadata>
      <Property name="CreatedBy" value="D365_ADMIN" />
      <Property name="CreatedDate" value="2024-01-22T09:00:00Z" />
      <Property name="Environment" value="PROD" />
    </Metadata>
  </Workflow>
  
  <Workflow id="D365_EXPENSE_001" name="Dynamics 365 Expense Report" version="1.2">
    <Description>Employee expense report approval workflow</Description>
    <DocumentType>ExpenseReport</DocumentType>
    <Steps>
      <Step id="EXP_STEP_001" sequence="1" name="Manager Approval">
        <Conditions>
          <Condition field="Amount" operator="LessThan" value="1000" />
        </Conditions>
        <Approvers>
          <Approver role="Manager" userId="<EMAIL>" />
        </Approvers>
        <Mode>Serial</Mode>
        <TimeoutHours>48</TimeoutHours>
        <AutoApprove>false</AutoApprove>
      </Step>
      <Step id="EXP_STEP_002" sequence="2" name="Finance Review">
        <Conditions>
          <Condition field="Amount" operator="GreaterThanOrEqual" value="1000" />
        </Conditions>
        <Approvers>
          <Approver role="Finance" userId="<EMAIL>" />
        </Approvers>
        <Mode>Serial</Mode>
        <TimeoutHours>72</TimeoutHours>
        <AutoApprove>false</AutoApprove>
      </Step>
      <Step id="EXP_STEP_003" sequence="0" name="Auto Approval">
        <Conditions>
          <Condition field="Amount" operator="LessThan" value="25" />
          <Condition field="Category" operator="Equals" value="Meals" />
        </Conditions>
        <Approvers />
        <Mode>Serial</Mode>
        <TimeoutHours>0</TimeoutHours>
        <AutoApprove>true</AutoApprove>
      </Step>
    </Steps>
    <Metadata>
      <Property name="CreatedBy" value="D365_ADMIN" />
      <Property name="CreatedDate" value="2024-01-20T11:30:00Z" />
      <Property name="Environment" value="PROD" />
    </Metadata>
  </Workflow>
</WorkflowDefinitions>

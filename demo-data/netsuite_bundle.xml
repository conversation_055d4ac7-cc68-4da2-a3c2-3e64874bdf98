<?xml version="1.0" encoding="UTF-8"?>
<NetSuiteWorkflows xmlns="http://www.netsuite.com/workflow/2024">
  <Bundle id="APPROVAL_WORKFLOWS" version="2.0.1">
    <Workflow id="NS_BILL_APPROVAL" name="NetSuite Bill Approval" type="Bill">
      <Description>Vendor bill approval workflow with multi-tier authorization</Description>
      <Triggers>
        <Trigger event="onCreate" />
        <Trigger event="onEdit" />
      </Triggers>
      <States>
        <State id="PENDING_APPROVAL" name="Pending Approval" initial="true">
          <Actions>
            <Action type="SetField" field="approvalstatus" value="1" />
            <Action type="SendEmail" template="APPROVAL_REQUEST" />
          </Actions>
          <Transitions>
            <Transition to="MANAGER_REVIEW" condition="amount &lt; 5000" />
            <Transition to="DIRECTOR_REVIEW" condition="amount &gt;= 5000 and amount &lt; 25000" />
            <Transition to="EXECUTIVE_REVIEW" condition="amount &gt;= 25000" />
            <Transition to="AUTO_APPROVED" condition="amount &lt; 100 and vendor in ('VENDOR001', 'VENDOR002')" />
          </Transitions>
        </State>
        
        <State id="MANAGER_REVIEW" name="Manager Review">
          <Approvers>
            <Approver role="Manager" user="<EMAIL>" />
          </Approvers>
          <TimeoutHours>24</TimeoutHours>
          <Actions>
            <Action type="SetField" field="approver" value="{user.id}" />
          </Actions>
          <Transitions>
            <Transition to="APPROVED" condition="approved = true" />
            <Transition to="REJECTED" condition="approved = false" />
          </Transitions>
        </State>
        
        <State id="DIRECTOR_REVIEW" name="Director Review">
          <Approvers>
            <Approver role="Director" user="<EMAIL>" />
          </Approvers>
          <TimeoutHours>48</TimeoutHours>
          <Actions>
            <Action type="SetField" field="approver" value="{user.id}" />
          </Actions>
          <Transitions>
            <Transition to="APPROVED" condition="approved = true" />
            <Transition to="REJECTED" condition="approved = false" />
          </Transitions>
        </State>
        
        <State id="EXECUTIVE_REVIEW" name="Executive Review">
          <Approvers>
            <Approver role="Executive" user="<EMAIL>" />
          </Approvers>
          <TimeoutHours>72</TimeoutHours>
          <Actions>
            <Action type="SetField" field="approver" value="{user.id}" />
          </Actions>
          <Transitions>
            <Transition to="APPROVED" condition="approved = true" />
            <Transition to="REJECTED" condition="approved = false" />
          </Transitions>
        </State>
        
        <State id="AUTO_APPROVED" name="Auto Approved" final="true">
          <Actions>
            <Action type="SetField" field="approvalstatus" value="2" />
            <Action type="SetField" field="approver" value="SYSTEM" />
          </Actions>
        </State>
        
        <State id="APPROVED" name="Approved" final="true">
          <Actions>
            <Action type="SetField" field="approvalstatus" value="2" />
            <Action type="SendEmail" template="APPROVAL_NOTIFICATION" />
          </Actions>
        </State>
        
        <State id="REJECTED" name="Rejected" final="true">
          <Actions>
            <Action type="SetField" field="approvalstatus" value="3" />
            <Action type="SendEmail" template="REJECTION_NOTIFICATION" />
          </Actions>
        </State>
      </States>
    </Workflow>
    
    <Workflow id="NS_PURCHASE_ORDER" name="NetSuite Purchase Order" type="PurchaseOrder">
      <Description>Purchase order approval workflow</Description>
      <Triggers>
        <Trigger event="onCreate" />
      </Triggers>
      <States>
        <State id="PENDING_APPROVAL" name="Pending Approval" initial="true">
          <Transitions>
            <Transition to="PROCUREMENT_REVIEW" condition="amount &lt; 10000" />
            <Transition to="EXECUTIVE_APPROVAL" condition="amount &gt;= 10000" />
          </Transitions>
        </State>
        
        <State id="PROCUREMENT_REVIEW" name="Procurement Review">
          <Approvers>
            <Approver role="Procurement" user="<EMAIL>" />
          </Approvers>
          <TimeoutHours>24</TimeoutHours>
          <Transitions>
            <Transition to="APPROVED" condition="approved = true" />
            <Transition to="REJECTED" condition="approved = false" />
          </Transitions>
        </State>
        
        <State id="EXECUTIVE_APPROVAL" name="Executive Approval">
          <Approvers>
            <Approver role="Executive" user="<EMAIL>" />
          </Approvers>
          <TimeoutHours>48</TimeoutHours>
          <Transitions>
            <Transition to="APPROVED" condition="approved = true" />
            <Transition to="REJECTED" condition="approved = false" />
          </Transitions>
        </State>
        
        <State id="APPROVED" name="Approved" final="true">
          <Actions>
            <Action type="SetField" field="status" value="Approved" />
          </Actions>
        </State>
        
        <State id="REJECTED" name="Rejected" final="true">
          <Actions>
            <Action type="SetField" field="status" value="Rejected" />
          </Actions>
        </State>
      </States>
    </Workflow>
  </Bundle>
  
  <FieldMappings>
    <Mapping source="amount" target="amount" />
    <Mapping source="currency" target="currency" />
    <Mapping source="vendor" target="supplier.id" />
    <Mapping source="subsidiary" target="organization.costCenter" />
    <Mapping source="department" target="organization.department" />
    <Mapping source="location" target="organization.location" />
  </FieldMappings>
  
  <ExportInfo>
    <ExportedAt>2024-01-25T16:45:00Z</ExportedAt>
    <ExportedBy>NETSUITE_ADMIN</ExportedBy>
    <NetSuiteVersion>2024.1</NetSuiteVersion>
    <AccountId>ACCT_12345</AccountId>
  </ExportInfo>
</NetSuiteWorkflows>

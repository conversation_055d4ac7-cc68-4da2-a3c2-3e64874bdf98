{"workflowDefinitions": [{"id": "SAP_INVOICE_APPROVAL_001", "name": "SAP Invoice Release Strategy", "description": "Standard SAP invoice approval workflow with amount-based routing", "version": "2.1.0", "category": "INVOICE_PROCESSING", "steps": [{"id": "STEP_001", "name": "Department Manager <PERSON><PERSON><PERSON><PERSON>", "sequence": 1, "conditions": [{"field": "CEKKO-WRBTR", "operator": "LT", "value": 5000, "logic": "AND"}, {"field": "CEKKO-BUKRS", "operator": "EQ", "value": "1000", "logic": "AND"}], "approvers": [{"id": "DEPT_MGR_001", "role": "Department Manager", "userId": "<EMAIL>"}], "mode": "serial", "timeout": 24, "autoApprove": false}, {"id": "STEP_002", "name": "Finance Director <PERSON><PERSON><PERSON><PERSON>", "sequence": 2, "conditions": [{"field": "CEKKO-WRBTR", "operator": "GE", "value": 5000, "logic": "AND"}, {"field": "CEKKO-WRBTR", "operator": "LT", "value": 25000, "logic": "AND"}], "approvers": [{"id": "FIN_DIR_001", "role": "Finance Director", "userId": "<EMAIL>"}], "mode": "serial", "timeout": 48, "autoApprove": false}, {"id": "STEP_003", "name": "CFO Approval", "sequence": 3, "conditions": [{"field": "CEKKO-WRBTR", "operator": "GE", "value": 25000, "logic": "AND"}], "approvers": [{"id": "CFO_001", "role": "Chief Financial Officer", "userId": "<EMAIL>"}], "mode": "serial", "timeout": 72, "autoApprove": false}, {"id": "STEP_004", "name": "Auto-Approval for Small Amounts", "sequence": 0, "conditions": [{"field": "CEKKO-WRBTR", "operator": "LT", "value": 100, "logic": "AND"}, {"field": "CEKKO-LIFNR", "operator": "IN", "value": ["VENDOR_001", "VENDOR_002", "VENDOR_003"], "logic": "AND"}], "approvers": [], "mode": "serial", "timeout": 0, "autoApprove": true}], "metadata": {"sapSystem": "PRD", "releaseGroup": "INV", "lastModified": "2024-01-20T10:30:00Z", "createdBy": "SAP_ADMIN"}}, {"id": "SAP_PAYMENT_APPROVAL_001", "name": "SAP Payment Release Strategy", "description": "Payment approval workflow with dual authorization for high amounts", "version": "1.5.0", "category": "PAYMENT_PROCESSING", "steps": [{"id": "PAY_STEP_001", "name": "Single Approval", "sequence": 1, "conditions": [{"field": "REGUH-RWBTR", "operator": "LT", "value": 10000, "logic": "AND"}], "approvers": [{"id": "PAY_MGR_001", "role": "Payment Manager", "userId": "<EMAIL>"}], "mode": "serial", "timeout": 12, "autoApprove": false}, {"id": "PAY_STEP_002", "name": "Dual Authorization", "sequence": 1, "conditions": [{"field": "REGUH-RWBTR", "operator": "GE", "value": 10000, "logic": "AND"}], "approvers": [{"id": "PAY_AUTH_001", "role": "Payment Authorizer 1", "userId": "<EMAIL>"}, {"id": "PAY_AUTH_002", "role": "Payment Authorizer 2", "userId": "<EMAIL>"}], "mode": "parallel", "timeout": 24, "autoApprove": false}], "metadata": {"sapSystem": "PRD", "releaseGroup": "PAY", "lastModified": "2024-01-18T14:15:00Z", "createdBy": "SAP_ADMIN"}}], "fieldMappings": {"CEKKO-WRBTR": "amount", "CEKKO-WAERS": "currency", "CEKKO-LIFNR": "supplier.id", "CEKKO-BUKRS": "organization.costCenter", "CEKKO-EKGRP": "organization.department", "REGUH-RWBTR": "amount", "REGUH-WAERS": "currency", "REGUH-LIFNR": "supplier.id"}, "exportInfo": {"exportedAt": "2024-01-26T10:00:00Z", "exportedBy": "SAP_WORKFLOW_ADMIN", "sapVersion": "S/4HANA 2023", "systemId": "PRD_001"}}
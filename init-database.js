import pkg from 'pg';
const { Pool } = pkg;
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';

// Initialize dotenv
dotenv.config();

// Get current file and directory paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function initDatabase() {
  console.log("Initializing database with required tables and seed data...");
  
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL
  });
  
  try {
    // Step 1: Create all tables based on schema
    await pool.query(`
      -- Drop tables if they exist (in reverse order of dependencies)
      DROP TABLE IF EXISTS remittances CASCADE;
      DROP TABLE IF EXISTS payments CASCADE;
      DROP TABLE IF EXISTS received_payments CASCADE;
      DROP TABLE IF EXISTS invoices CASCADE;
      DROP TABLE IF EXISTS users CASCADE;
      
      -- Create users table
      CREATE TABLE users (
        id SERIAL PRIMARY KEY,
        username TEXT NOT NULL UNIQUE,
        password TEXT NOT NULL
      );
      
      -- Create payments table
      CREATE TABLE payments (
        id SERIAL PRIMARY KEY,
        reference TEXT NOT NULL,
        amount DOUBLE PRECISION NOT NULL,
        sender TEXT NOT NULL,
        recipient TEXT NOT NULL,
        recipient_address TEXT,
        recipient_account TEXT,
        status TEXT NOT NULL DEFAULT 'Not Approved',
        file_type TEXT NOT NULL,
        approved BOOLEAN NOT NULL DEFAULT false,
        approved_at TIMESTAMP,
        sent_at TIMESTAMP,
        file_content TEXT NOT NULL,
        remittance_generated BOOLEAN NOT NULL DEFAULT false,
        remittance_generated_at TIMESTAMP,
        remittance_id INTEGER,
        signature TEXT,
        message TEXT,
        receipt_imported BOOLEAN NOT NULL DEFAULT false,
        created_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
      
      -- Create remittances table
      CREATE TABLE remittances (
        id SERIAL PRIMARY KEY,
        payment_id INTEGER NOT NULL,
        status TEXT NOT NULL DEFAULT 'Generated',
        format TEXT NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        file_path TEXT NOT NULL,
        amount DOUBLE PRECISION NOT NULL,
        sender TEXT NOT NULL,
        recipient TEXT NOT NULL,
        reference TEXT NOT NULL
      );
      
      -- Create invoices table
      CREATE TABLE invoices (
        id SERIAL PRIMARY KEY,
        customer TEXT NOT NULL,
        amount DOUBLE PRECISION NOT NULL,
        reference TEXT NOT NULL,
        description TEXT NOT NULL,
        due_date TIMESTAMP NOT NULL,
        status TEXT NOT NULL DEFAULT 'Open',
        file_type TEXT,
        file_content TEXT,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        payment_id INTEGER,
        remittance_generated BOOLEAN NOT NULL DEFAULT false,
        remittance_generated_at TIMESTAMP,
        remittance_id INTEGER
      );
      
      -- Create received_payments table
      CREATE TABLE received_payments (
        id SERIAL PRIMARY KEY,
        sender TEXT NOT NULL,
        amount DOUBLE PRECISION NOT NULL,
        reference TEXT NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        invoice_id INTEGER,
        status TEXT NOT NULL DEFAULT 'Received',
        recipient TEXT NOT NULL DEFAULT 'Your Company',
        remittance_generated BOOLEAN NOT NULL DEFAULT false,
        remittance_generated_at TIMESTAMP,
        remittance_id INTEGER
      );
    `);
    
    // Step 2: Insert seed data for testing
    await pool.query(`
      -- Sample invoice
      INSERT INTO invoices (customer, amount, reference, description, due_date, status, created_at)
      VALUES ('TechCorp Inc.', 3500.00, 'INV-2025-001', 'IT Services - Q1 2025', NOW() + INTERVAL '30 days', 'Open', NOW());
      
      -- Sample payment
      INSERT INTO payments (reference, amount, sender, recipient, recipient_account, status, file_type, file_content, created_at)
      VALUES ('REF-********-001', 2500.00, 'Your Company', 'Supplier Corp', '**********', 'Not Approved', 'MT103', 'Sample payment file content', NOW());
      
      -- Sample received payment
      INSERT INTO received_payments (sender, amount, reference, created_at)
      VALUES ('TechCorp Inc.', 3500.00, 'PAYMENT-2025-001', NOW());
    `);
    
    console.log("Database successfully initialized with required tables and seed data!");
    process.exit(0);
  } catch (error) {
    console.error("Error initializing database:", error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the initialization
initDatabase();

-- Initial migration
CREATE TABLE IF NOT EXISTS users (
  id SERIAL PRIMARY KEY,
  username TEXT NOT NULL UNIQUE,
  password TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS payments (
  id SERIAL PRIMARY KEY,
  reference TEXT NOT NULL,
  amount DOUBLE PRECISION NOT NULL,
  sender TEXT NOT NULL,
  recipient TEXT NOT NULL,
  recipient_address TEXT,
  recipient_account TEXT,
  status TEXT NOT NULL DEFAULT 'Not Approved',
  file_type TEXT NOT NULL,
  approved BOOLEAN NOT NULL DEFAULT false,
  approved_at TIMESTAMP,
  sent_at TIMESTAMP,
  file_content TEXT NOT NULL,
  remittance_generated BOOLEAN NOT NULL DEFAULT false,
  remittance_generated_at TIMESTAMP,
  remittance_id INTEGER,
  signature TEXT,
  message TEXT,
  receipt_imported BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS remittances (
  id SERIAL PRIMARY KEY,
  payment_id INTEGER NOT NULL,
  status TEXT NOT NULL DEFAULT 'Generated',
  format TEXT NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  file_path TEXT NOT NULL,
  amount DOUBLE PRECISION NOT NULL,
  sender TEXT NOT NULL,
  recipient TEXT NOT NULL,
  reference TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS invoices (
  id SERIAL PRIMARY KEY,
  customer TEXT NOT NULL,
  amount DOUBLE PRECISION NOT NULL,
  reference TEXT NOT NULL,
  description TEXT NOT NULL,
  due_date TIMESTAMP NOT NULL,
  status TEXT NOT NULL DEFAULT 'Open',
  file_type TEXT,
  file_content TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  payment_id INTEGER,
  remittance_generated BOOLEAN NOT NULL DEFAULT false,
  remittance_generated_at TIMESTAMP,
  remittance_id INTEGER
);

CREATE TABLE IF NOT EXISTS received_payments (
  id SERIAL PRIMARY KEY,
  sender TEXT NOT NULL,
  amount DOUBLE PRECISION NOT NULL,
  reference TEXT NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  invoice_id INTEGER,
  status TEXT NOT NULL DEFAULT 'Received',
  recipient TEXT NOT NULL DEFAULT 'Your Company',
  remittance_generated BOOLEAN NOT NULL DEFAULT false,
  remittance_generated_at TIMESTAMP,
  remittance_id INTEGER
);

const { exec } = require('child_process');
require('dotenv').config();

function resetDatabase() {
  console.log("Resetting database...");
  
  // Run our custom initialization script
  exec('node init-database.js', (error, stdout, stderr) => {
    if (error) {
      console.error(`Error: ${error.message}`);
      return;
    }
    if (stderr) {
      console.error(`stderr: ${stderr}`);
    }
    console.log(`Database reset complete: ${stdout}`);
  });
}

// Run the reset
resetDatabase();
#!/bin/bash

# A simple script to run the test suite for Proofpay

# Show help
show_help() {
  echo "Proofpay Test Runner"
  echo "Usage: ./run-tests.sh [options]"
  echo ""
  echo "Options:"
  echo "  -h, --help       Show this help message"
  echo "  -w, --watch      Run tests in watch mode"
  echo "  -u, --ui         Run tests with UI"
  echo "  -c, --coverage   Generate coverage report"
  echo "  -f, --filter     Filter tests by name"
  echo ""
  echo "Examples:"
  echo "  ./run-tests.sh                   # Run all tests"
  echo "  ./run-tests.sh -w                # Run tests in watch mode"
  echo "  ./run-tests.sh -c                # Generate coverage report"
  echo "  ./run-tests.sh -f PaymentCard    # Run only PaymentCard tests"
  echo "  ./run-tests.sh -w -f usePayments # Run usePayments tests in watch mode"
}

# Initialize variables
WATCH=false
UI=false
COVERAGE=false
FILTER=""

# Parse arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    -h|--help)
      show_help
      exit 0
      ;;
    -w|--watch)
      WATCH=true
      shift
      ;;
    -u|--ui)
      UI=true
      shift
      ;;
    -c|--coverage)
      COVERAGE=true
      shift
      ;;
    -f|--filter)
      FILTER="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      show_help
      exit 1
      ;;
  esac
done

# Build command
COMMAND="npx vitest"

# Add options
if [ "$WATCH" = false ] && [ "$UI" = false ]; then
  COMMAND="$COMMAND run"
fi

if [ "$UI" = true ]; then
  COMMAND="$COMMAND --ui"
fi

if [ "$COVERAGE" = true ]; then
  COMMAND="$COMMAND --coverage"
fi

if [ -n "$FILTER" ]; then
  COMMAND="$COMMAND -t '$FILTER'"
fi

# Display command
echo "Running: $COMMAND"

# Execute command
eval "$COMMAND"
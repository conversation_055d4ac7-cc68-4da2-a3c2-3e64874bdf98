import { Pool, neonConfig } from '@neondatabase/serverless';
import ws from 'ws';

// Configure websocket to use ws module
neonConfig.webSocketConstructor = ws;

async function addMissingColumns() {
  const pool = new Pool({ connectionString: process.env.DATABASE_URL });
  
  try {
    console.log('Adding missing columns to received_payments table...');
    
    // Check if file_type column exists
    const fileTypeResult = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'received_payments' AND column_name = 'file_type'
    `);
    
    if (fileTypeResult.rows.length === 0) {
      console.log('Adding file_type column...');
      await pool.query(`ALTER TABLE received_payments ADD COLUMN file_type TEXT`);
    } else {
      console.log('file_type column already exists.');
    }
    
    // Check if file_content column exists
    const fileContentResult = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'received_payments' AND column_name = 'file_content'
    `);
    
    if (fileContentResult.rows.length === 0) {
      console.log('Adding file_content column...');
      await pool.query(`ALTER TABLE received_payments ADD COLUMN file_content TEXT`);
    } else {
      console.log('file_content column already exists.');
    }
    
    // Check if sender_address column exists
    const senderAddressResult = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'received_payments' AND column_name = 'sender_address'
    `);
    
    if (senderAddressResult.rows.length === 0) {
      console.log('Adding sender_address column...');
      await pool.query(`ALTER TABLE received_payments ADD COLUMN sender_address TEXT`);
    } else {
      console.log('sender_address column already exists.');
    }
    
    // Check if sender_account column exists
    const senderAccountResult = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'received_payments' AND column_name = 'sender_account'
    `);
    
    if (senderAccountResult.rows.length === 0) {
      console.log('Adding sender_account column...');
      await pool.query(`ALTER TABLE received_payments ADD COLUMN sender_account TEXT`);
    } else {
      console.log('sender_account column already exists.');
    }
    
    // Check if recipient column exists
    const recipientResult = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'received_payments' AND column_name = 'recipient'
    `);
    
    if (recipientResult.rows.length === 0) {
      console.log('Adding recipient column...');
      await pool.query(`ALTER TABLE received_payments ADD COLUMN recipient TEXT NOT NULL DEFAULT 'Your Company'`);
    } else {
      console.log('recipient column already exists.');
    }
    
    console.log('All missing columns added successfully!');
  } catch (error) {
    console.error('Error adding missing columns:', error);
  } finally {
    await pool.end();
  }
}

addMissingColumns().catch(console.error);
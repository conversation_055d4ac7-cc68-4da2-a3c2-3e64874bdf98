import { db } from "../server/db";
import * as schema from "../shared/schema";
import { sql } from "drizzle-orm";

/**
 * This script adds the receipt_imported field to the payments table
 * 
 * The receipt_imported field is used to track when a receipt has been imported
 * for a payment, allowing it to appear in the reconciliation column without
 * changing its status from "Paid" to "Reconciled". This enables a more 
 * flexible workflow where receipt import and reconciliation generation
 * are separate steps.
 */
async function addReceiptImportedField() {
  console.log("Adding receipt_imported field to the payments table...");
  
  try {
    // Add receipt_imported column if it doesn't exist
    await db.execute(sql`
      ALTER TABLE payments
      ADD COLUMN IF NOT EXISTS receipt_imported BOOLEAN NOT NULL DEFAULT FALSE;
    `);
    
    console.log("Successfully added receipt_imported field to the payments table");
    process.exit(0);
  } catch (error) {
    console.error("Error adding receipt_imported field:", error);
    process.exit(1);
  }
}

// Run the script
addReceiptImportedField();
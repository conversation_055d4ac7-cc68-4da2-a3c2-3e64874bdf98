import { db } from "../server/db";
import * as schema from "../shared/schema";
import { sql } from "drizzle-orm";

/**
 * This script adds the signature and message fields to the payments table
 * for supporting BLS signatures in the application
 */
async function addSignatureFields() {
  console.log("Adding signature fields to the payments table...");
  
  try {
    // Add signature column if it doesn't exist
    await db.execute(sql`
      ALTER TABLE payments
      ADD COLUMN IF NOT EXISTS signature TEXT;
    `);
    
    // Add message column if it doesn't exist
    await db.execute(sql`
      ALTER TABLE payments
      ADD COLUMN IF NOT EXISTS message TEXT;
    `);
    
    console.log("Successfully added signature fields to the payments table");
    process.exit(0);
  } catch (error) {
    console.error("Error adding signature fields:", error);
    process.exit(1);
  }
}

// Run the script
addSignatureFields();
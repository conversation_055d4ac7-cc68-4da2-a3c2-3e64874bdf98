import { Pool, neonConfig } from '@neondatabase/serverless';
import ws from 'ws';

// Configure websocket to use ws module
neonConfig.webSocketConstructor = ws;

async function fixRecipientColumn() {
  const pool = new Pool({ connectionString: process.env.DATABASE_URL });
  
  try {
    console.log('Fixing recipient column in received_payments table...');
    
    // First, check if the recipient column exists
    const recipientResult = await pool.query(`
      SELECT column_name, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'received_payments' AND column_name = 'recipient'
    `);
    
    if (recipientResult.rows.length === 0) {
      // Column doesn't exist, add it with default value
      console.log('Adding recipient column with default value...');
      await pool.query(`ALTER TABLE received_payments ADD COLUMN recipient TEXT NOT NULL DEFAULT 'Your Company'`);
      console.log('Added recipient column successfully.');
    } else {
      // Column exists, check if it's nullable
      const column = recipientResult.rows[0];
      if (column.is_nullable === 'NO' && !column.column_default) {
        // It's not nullable but has no default - add the default value to existing rows
        console.log('Updating existing rows with default recipient value...');
        await pool.query(`UPDATE received_payments SET recipient = 'Your Company' WHERE recipient IS NULL`);
        
        // And add a default value for future insertions
        console.log('Adding default value constraint...');
        await pool.query(`ALTER TABLE received_payments ALTER COLUMN recipient SET DEFAULT 'Your Company'`);
      } else {
        console.log('Recipient column already exists with appropriate constraints.');
      }
    }
    
    console.log('Recipient column fixed successfully!');
  } catch (error) {
    console.error('Error fixing recipient column:', error);
  } finally {
    await pool.end();
  }
}

fixRecipientColumn().catch(console.error);
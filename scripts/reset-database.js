import { db, pool } from '../server/db.ts';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function resetDatabase() {
  console.log("Starting database reset...");
  
  try {
    // Read the SQL script file
    const sqlFilePath = path.join(__dirname, 'reset-database.sql');
    const sqlScript = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL script directly
    console.log("Executing SQL script...");
    await pool.query(sqlScript);
    
    console.log("Database reset complete!");
  } catch (error) {
    console.error("Error during database reset:", error);
  } finally {
    await pool.end();
  }
}

// Run the reset function
resetDatabase();
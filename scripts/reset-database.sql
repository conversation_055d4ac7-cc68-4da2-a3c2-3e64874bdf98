-- <PERSON><PERSON><PERSON> to reset the database with correct schema to match code

-- Drop tables if they exist (in reverse order of creation to respect foreign keys)
DROP TABLE IF EXISTS received_payments CASCADE;
DROP TABLE IF EXISTS remittances CASCADE;
DROP TABLE IF EXISTS invoices CASCADE;
DROP TABLE IF EXISTS payments CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id SERIAL PRIMARY KEY,
  username TEXT NOT NULL UNIQUE,
  password TEXT NOT NULL
);

-- Create payments table
CREATE TABLE IF NOT EXISTS payments (
  id SERIAL PRIMARY KEY,
  reference TEXT NOT NULL,
  amount DOUBLE PRECISION NOT NULL,
  sender TEXT NOT NULL,
  recipient TEXT NOT NULL,
  recipient_address TEXT,
  recipient_account TEXT,
  status TEXT NOT NULL DEFAULT 'Not Approved',
  file_type TEXT NOT NULL,
  approved BOOLEAN NOT NULL DEFAULT false,
  approved_at TIMESTAMP,
  sent_at TIMESTAMP,
  file_content TEXT NOT NULL,
  remittance_generated BOOLEAN NOT NULL DEFAULT false,
  remittance_generated_at TIMESTAMP,
  remittance_id INTEGER,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create remittances table
CREATE TABLE IF NOT EXISTS remittances (
  id SERIAL PRIMARY KEY,
  payment_id INTEGER NOT NULL,
  status TEXT NOT NULL DEFAULT 'Generated',
  format TEXT NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  file_path TEXT NOT NULL,
  amount DOUBLE PRECISION NOT NULL,
  sender TEXT NOT NULL,
  recipient TEXT NOT NULL,
  reference TEXT NOT NULL
);

-- Create invoices table
CREATE TABLE IF NOT EXISTS invoices (
  id SERIAL PRIMARY KEY,
  customer TEXT NOT NULL,
  amount DOUBLE PRECISION NOT NULL,
  reference TEXT NOT NULL,
  due_date TIMESTAMP NOT NULL,
  status TEXT NOT NULL DEFAULT 'Open',
  file_type TEXT,
  file_content TEXT,
  description TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  payment_id INTEGER,
  remittance_generated BOOLEAN NOT NULL DEFAULT false,
  remittance_generated_at TIMESTAMP,
  remittance_id INTEGER
);

-- Create received_payments table (to match schema in code)
CREATE TABLE IF NOT EXISTS received_payments (
  id SERIAL PRIMARY KEY,
  sender TEXT NOT NULL,
  amount DOUBLE PRECISION NOT NULL,
  reference TEXT NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  invoice_id INTEGER,
  status TEXT NOT NULL DEFAULT 'Received',
  file_type TEXT,
  file_content TEXT,
  sender_address TEXT,
  sender_account TEXT,
  remittance_generated BOOLEAN NOT NULL DEFAULT false,
  remittance_generated_at TIMESTAMP,
  remittance_id INTEGER
);

-- No sample data, starting with an empty database
-- Uncomment the following sections if you want to add sample data later

/*
-- 1. Sample payment data
INSERT INTO payments (reference, amount, sender, recipient, file_type, file_content, created_at)
VALUES 
  ('REF-********-001', 1250.75, 'Proofbase Inc.', 'ABC Suppliers', 'MT103', 'Sample MT103 content for payment 1', '2025-04-18T10:00:00Z'),
  ('REF-********-002', 2780.50, 'Proofbase Inc.', 'XYZ Distributors', 'ISO20022', 'Sample ISO20022 content for payment 2', '2025-04-18T11:30:00Z'),
  ('REF-********-003', 950.25, 'Proofbase Inc.', 'Global Logistics', 'PEXR2002', 'Sample PEXR2002 content for payment 3', '2025-04-18T14:45:00Z');

-- 2. Sample invoice data with description field
INSERT INTO invoices (reference, amount, customer, description, due_date, file_type, file_content, created_at)
VALUES 
  ('INV-********-001', 3450.80, 'TechCorp Inc.', 'Technology services Q2', '2025-05-15', 'EDI X12', 'Sample EDI X12 content for invoice 1', '2025-04-18T09:15:00Z'),
  ('INV-********-002', 1875.25, 'MediPharma GmbH', 'Medical supplies', '2025-05-20', 'ISO20022', 'Sample ISO20022 content for invoice 2', '2025-04-18T13:45:00Z');

-- 3. Sample received payment (no recipient column)
INSERT INTO received_payments (reference, amount, sender, file_type, file_content, created_at)
VALUES 
  ('RCPT-********-001', 3450.80, 'TechCorp Inc.', 'MT103', 'Sample MT103 content for received payment', '2025-04-18T16:30:00Z');
*/
import { db, pool } from '../server/db';
import { sql } from 'drizzle-orm';

// Function to reset all tables and re-create them based on the actual schema
async function resetDatabase() {
  console.log("Starting database reset...");
  
  try {
    // Drop tables if they exist (in reverse order of creation to respect foreign keys)
    await db.execute(sql`DROP TABLE IF EXISTS received_payments CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS remittances CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS invoices CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS payments CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS users CASCADE`);
    
    console.log("Tables dropped successfully");
    
    // Create database schema from scratch based on the exact schema in shared/schema.ts
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username TEXT NOT NULL UNIQUE,
        password TEXT NOT NULL
      )
    `);
    
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS payments (
        id SERIAL PRIMARY KEY,
        reference TEXT NOT NULL,
        amount DOUBLE PRECISION NOT NULL,
        sender TEXT NOT NULL,
        recipient TEXT NOT NULL,
        recipient_address TEXT,
        recipient_account TEXT,
        status TEXT NOT NULL DEFAULT 'Not Approved',
        file_type TEXT NOT NULL,
        approved BOOLEAN NOT NULL DEFAULT false,
        approved_at TIMESTAMP,
        sent_at TIMESTAMP,
        file_content TEXT NOT NULL,
        remittance_generated BOOLEAN NOT NULL DEFAULT false,
        remittance_generated_at TIMESTAMP,
        remittance_id INTEGER,
        signature TEXT,
        message TEXT,
        receipt_imported BOOLEAN NOT NULL DEFAULT false,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS remittances (
        id SERIAL PRIMARY KEY,
        payment_id INTEGER NOT NULL,
        status TEXT NOT NULL DEFAULT 'Generated',
        format TEXT NOT NULL,
        file_path TEXT NOT NULL,
        amount DOUBLE PRECISION NOT NULL,
        sender TEXT NOT NULL,
        recipient TEXT NOT NULL,
        reference TEXT NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS invoices (
        id SERIAL PRIMARY KEY,
        reference TEXT NOT NULL,
        amount DOUBLE PRECISION NOT NULL,
        customer TEXT NOT NULL,
        description TEXT NOT NULL,
        due_date TIMESTAMP NOT NULL,
        status TEXT NOT NULL DEFAULT 'Open',
        file_type TEXT,
        file_content TEXT,
        payment_id INTEGER,
        remittance_generated BOOLEAN NOT NULL DEFAULT false,
        remittance_generated_at TIMESTAMP,
        remittance_id INTEGER,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS received_payments (
        id SERIAL PRIMARY KEY,
        reference TEXT NOT NULL,
        amount DOUBLE PRECISION NOT NULL,
        sender TEXT NOT NULL,
        recipient TEXT NOT NULL,
        invoice_id INTEGER,
        status TEXT NOT NULL DEFAULT 'Received',
        remittance_generated BOOLEAN NOT NULL DEFAULT false,
        remittance_generated_at TIMESTAMP,
        remittance_id INTEGER,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log("Tables created successfully");
    
    // Insert some sample data
    // 1. Sample payment data
    await db.execute(sql`
      INSERT INTO payments (reference, amount, sender, recipient, file_type, file_content, created_at)
      VALUES 
        ('REF-20250418-001', 1250.75, 'Proofbase Inc.', 'ABC Suppliers', 'MT103', 'Sample MT103 content for payment 1', '2025-04-18T10:00:00Z'),
        ('REF-20250418-002', 2780.50, 'Proofbase Inc.', 'XYZ Distributors', 'ISO20022', 'Sample ISO20022 content for payment 2', '2025-04-18T11:30:00Z'),
        ('REF-20250418-003', 950.25, 'Proofbase Inc.', 'Global Logistics', 'PEXR2002', 'Sample PEXR2002 content for payment 3', '2025-04-18T14:45:00Z')
    `);
    
    // 2. Sample invoice data
    await db.execute(sql`
      INSERT INTO invoices (reference, amount, customer, description, due_date, file_type, file_content, created_at)
      VALUES 
        ('INV-20250418-001', 3450.80, 'TechCorp Inc.', 'Technology services Q2', '2025-05-15', 'EDI X12', 'Sample EDI X12 content for invoice 1', '2025-04-18T09:15:00Z'),
        ('INV-20250418-002', 1875.25, 'MediPharma GmbH', 'Medical supplies', '2025-05-20', 'ISO20022', 'Sample ISO20022 content for invoice 2', '2025-04-18T13:45:00Z')
    `);
    
    // 3. Sample received payment
    await db.execute(sql`
      INSERT INTO received_payments (reference, amount, sender, recipient, created_at)
      VALUES 
        ('RCPT-20250418-001', 3450.80, 'TechCorp Inc.', 'Proofbase Inc.', '2025-04-18T16:30:00Z')
    `);
    
    console.log("Sample data inserted successfully");
    console.log("Database reset complete!");
    
  } catch (error) {
    console.error("Error during database reset:", error);
  } finally {
    await pool.end();
  }
}

// Run the reset function
resetDatabase();
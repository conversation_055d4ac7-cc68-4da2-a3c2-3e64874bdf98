import { db } from "../server/db";
import { receivedPayments } from "../shared/schema";
import { eq } from "drizzle-orm";

/**
 * This script updates all received payments to use the new status values:
 * - Payments without invoice links are set to "Unlinked"
 * - Payments with invoice links are set to "Linked"
 * - Payments with remittance_generated remain "Remitted"
 */
async function updateReceivedPaymentStatuses() {
  try {
    console.log("Starting to update received payment statuses...");
    
    // Get all received payments
    const allReceivedPayments = await db.select().from(receivedPayments);
    console.log(`Found ${allReceivedPayments.length} received payments to update.`);

    // Update each payment according to its state
    for (const payment of allReceivedPayments) {
      if (payment.remittance_generated) {
        // Already remitted - skip
        console.log(`Payment ${payment.id} already has status "Remitted" - skipping.`);
        continue;
      }
      
      // Determine new status
      const newStatus = payment.invoice_id ? "Linked" : "Unlinked";
      console.log(`Updating payment ${payment.id} to status "${newStatus}"`);
      
      // Update the payment
      await db
        .update(receivedPayments)
        .set({ status: newStatus })
        .where(eq(receivedPayments.id, payment.id));
    }
    
    console.log("Received payment status update completed successfully.");
  } catch (error) {
    console.error("Error updating received payment statuses:", error);
  } finally {
    process.exit(0);
  }
}

// Execute the function
updateReceivedPaymentStatuses();
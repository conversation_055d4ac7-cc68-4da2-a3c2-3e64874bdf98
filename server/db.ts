import pkg from 'pg';
const { Pool } = pkg;
import { drizzle } from 'drizzle-orm/node-postgres';
import * as schema from "@shared/schema";
import { fileURLToPath } from 'url';
import path from 'path';
import fs from 'fs';
import dotenv from 'dotenv';
import process from 'process';

// Load environment variables from .env file if present
const dotenvPath = path.resolve(process.cwd(), '.env');
if (fs.existsSync(dotenvPath)) {
  dotenv.config({ path: dotenvPath });
  console.log('Loaded environment variables from .env file');
}

if (!process.env.DATABASE_URL) {
  throw new Error(
    "DATABASE_URL must be set. Did you forget to provision a database or create a .env file?",
  );
}

let pool;
let db;

// Determine if the database URL is for Neon or a standard PostgreSQL database
if (process.env.DATABASE_URL.includes('neon.tech')) {
  // Neon database connection
  import('@neondatabase/serverless').then(({ Pool: NeonPool, neonConfig }) => {
    import('ws').then((ws) => {
      neonConfig.webSocketConstructor = ws.default;
      pool = new NeonPool({ connectionString: process.env.DATABASE_URL });
      db = drizzle(pool, { schema });
      console.log('Connected to Neon PostgreSQL database');
    });
  }).catch(err => {
    console.error('Failed to load Neon PostgreSQL driver:', err);
    process.exit(1);
  });
} else {
  // Standard PostgreSQL connection
  pool = new Pool({ connectionString: process.env.DATABASE_URL });
  db = drizzle(pool, { schema });
  console.log('Connected to standard PostgreSQL database');
}

export { pool, db };

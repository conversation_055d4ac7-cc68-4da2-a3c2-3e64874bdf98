import express, { type Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { z } from "zod";
import { insertPaymentSchema, insertInvoiceSchema, insertRemittanceSchema, insertReceivedPaymentSchema, fileFormats, invoices } from "@shared/schema";
import { processPaymentFile, processInvoiceFile, generateSamplePaymentFile, generateSampleInvoiceFile, detectFileFormat } from "./utils/fileProcessors";
import { generateRemittanceFile, generateSampleRemittanceFile } from "./utils/fileGenerators";
import multer from "multer";
import path from "path";
import { db } from "./db";
import { eq } from "drizzle-orm";

// Import the network simulator conditionally based on environment flag
import { networkSimulator } from "./utils/networkSimulator";
// Create a dummy simulator for when the real one is disabled
const dummySimulator = {
  sendPayment: async (id: number, callback: () => Promise<void>) => {
    // Execute the callback immediately with no delay
    return callback();
  },
  receivePayment: async (id: number, callback: () => Promise<void>) => {
    // Execute the callback immediately with no delay
    return callback();
  }
};

// Use the real simulator only if explicitly enabled
const simulator = process.env.SIMULATOR_ENABLED === 'true' ? networkSimulator : dummySimulator;

// Configure multer for file uploads
const upload = multer({ storage: multer.memoryStorage() });

export async function registerRoutes(app: Express): Promise<Server> {
  const httpServer = createServer(app);
  
  // Payment API
  app.get("/api/payments", async (req: Request, res: Response) => {
    try {
      const payments = await storage.getAllPayments();
      res.json(payments);
    } catch (error) {
      console.error("Error fetching payments:", error);
      res.status(500).json({ error: "Failed to fetch payments" });
    }
  });
  
  app.get("/api/payments/:id", async (req: Request, res: Response) => {
    try {
      const payment = await storage.getPayment(parseInt(req.params.id));
      if (!payment) {
        return res.status(404).json({ error: "Payment not found" });
      }
      res.json(payment);
    } catch (error) {
      console.error("Error fetching payment:", error);
      res.status(500).json({ error: "Failed to fetch payment" });
    }
  });
  
  app.post("/api/payments/:id/approve", async (req: Request, res: Response) => {
    try {
      // Check if request contains signature and message fields
      const { signature, message } = req.body;
      
      // Approve the payment and include signature if provided
      const payment = await storage.approvePayment(
        parseInt(req.params.id), 
        signature, 
        message
      );
      
      if (!payment) {
        return res.status(404).json({ error: "Payment not found" });
      }
      
      res.json(payment);
    } catch (error) {
      console.error("Error approving payment:", error);
      res.status(500).json({ error: "Failed to approve payment" });
    }
  });
  
  app.post("/api/payments/:id/revoke", async (req: Request, res: Response) => {
    try {
      const payment = await storage.revokePaymentApproval(parseInt(req.params.id));
      if (!payment) {
        return res.status(404).json({ error: "Payment not found" });
      }
      res.json(payment);
    } catch (error) {
      console.error("Error revoking payment approval:", error);
      res.status(500).json({ error: "Failed to revoke payment approval" });
    }
  });
  
  app.post("/api/payments/:id/send", async (req: Request, res: Response) => {
    try {
      const paymentId = parseInt(req.params.id);
      const payment = await storage.getPayment(paymentId);
      
      if (!payment) {
        return res.status(404).json({ error: "Payment not found" });
      }
      
      if (!payment.approved) {
        return res.status(400).json({ error: "Payment must be approved before sending" });
      }
      
      // Start the network simulator and immediately return a pending status
      res.json({ status: "pending", message: "Payment is being processed" });
      
      // Process the payment with or without delay based on simulator setting
      await simulator.sendPayment(paymentId, async () => {
        try {
          // Update payment status to "Paid"
          const updatedPayment = await storage.sendPayment(paymentId);
          console.log("Updated payment to PAID:", updatedPayment);
          
          if (updatedPayment) {
            // Auto-loading of sent payments into received payments is now disabled
            // This allows for manual import of payments via the Import Receipt modal
            console.log(`Payment #${paymentId} marked as sent successfully`);
            
            // Note: The following code is commented out to prevent automatic creation of received payments
            // This was causing issues with the manual import flow
            /*
            const paymentData = {
              reference: updatedPayment.reference,
              amount: updatedPayment.amount,
              sender: updatedPayment.sender,
              recipient: "Your Company",
              invoice_id: null
            };
            
            // Automatically create corresponding received payment in AR
            const receivedPayment = await storage.createReceivedPayment(paymentData);
            
            // Auto-link to matching invoice if one exists
            const allInvoices = await storage.getAllInvoices();
            const matchingInvoice = allInvoices.find(invoice => 
              invoice.reference === receivedPayment.reference ||
              (invoice.reference.includes('-') && receivedPayment.reference.includes('-') &&
               invoice.reference.split('-').slice(1).join('-') === receivedPayment.reference.split('-').slice(1).join('-'))
            );
            
            if (matchingInvoice) {
              await storage.linkReceivedPaymentToInvoice(receivedPayment.id, matchingInvoice.id);
              await storage.updateInvoiceStatus(matchingInvoice.id, "Paid");
            }
            */
          }
        } catch (error) {
          console.error("Error in payment callback:", error);
        }
      });
    } catch (error) {
      console.error("Error sending payment:", error);
      // Error will be logged but not returned to client since response is already sent
    }
  });
  
  app.post("/api/payments/:id/generate-remittance", async (req: Request, res: Response) => {
    try {
      const formatSchema = z.enum(fileFormats.remittance);
      const parsedBody = z.object({ format: formatSchema }).parse(req.body);
      const format = parsedBody.format;
      
      const paymentId = parseInt(req.params.id);
      const payment = await storage.getPayment(paymentId);
      
      if (!payment) {
        return res.status(404).json({ error: "Payment not found" });
      }
      
      if (!payment.sent_at) {
        return res.status(400).json({ error: "Payment must be sent before generating remittance" });
      }
      
      // Generate remittance content
      const remittanceContent = generateRemittanceFile(payment, null, format);
      
      // Save the file
      const filePath = await storage.saveFile(remittanceContent, "remittance", format);
      
      // Create remittance record
      const remittance = await storage.createRemittance({
        payment_id: payment.id,
        format,
        file_path: filePath,
        amount: payment.amount,
        sender: payment.sender,
        recipient: "Your Company", // Add a default recipient value
        reference: payment.reference
      });
      
      // Update payment's remittance status
      await storage.updatePaymentRemittanceStatus(payment.id, remittance.id);
      
      res.json(remittance);
    } catch (error) {
      console.error("Error generating remittance:", error);
      res.status(500).json({ error: "Failed to generate remittance" });
    }
  });
  
  app.post("/api/upload_payment", upload.single("file"), async (req: Request, res: Response) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: "No file uploaded" });
      }
      
      // Convert buffer to string
      const fileContent = req.file.buffer.toString('utf8');
      
      // Auto-detect format and process file
      const paymentData = processPaymentFile(fileContent);
      console.log("Payment data parsed successfully:", paymentData.reference);
      
      // Check for duplicate reference number
      const existingPayment = await storage.getPaymentByReference(paymentData.reference);
      if (existingPayment) {
        return res.status(400).json({ 
          error: `Payment with reference number "${paymentData.reference}" already exists (ID: ${existingPayment.id})` 
        });
      }
      
      // Create payment record
      const payment = await storage.createPayment(paymentData);
      console.log("Payment created successfully with ID:", payment.id);
      
      res.json(payment);
    } catch (error) {
      console.error("Error uploading payment file:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to upload payment file" });
    }
  });
  
  app.get("/api/sample_payment/:format", async (req: Request, res: Response) => {
    try {
      const formatSchema = z.enum(fileFormats.payment);
      const format = formatSchema.parse(req.params.format);
      
      // Generate sample file
      const sampleContent = generateSamplePaymentFile(format);
      
      // Set appropriate content type
      res.setHeader('Content-Type', 'text/plain');
      res.setHeader('Content-Disposition', `attachment; filename=sample_payment_${format}.txt`);
      res.send(sampleContent);
    } catch (error) {
      console.error("Error generating sample payment file:", error);
      res.status(500).json({ error: "Failed to generate sample payment file" });
    }
  });
  
  // Remittance API
  app.get("/api/remittances/:id", async (req: Request, res: Response) => {
    try {
      const remittance = await storage.getRemittance(parseInt(req.params.id));
      if (!remittance) {
        return res.status(404).json({ error: "Remittance not found" });
      }
      res.json(remittance);
    } catch (error) {
      console.error("Error fetching remittance:", error);
      res.status(500).json({ error: "Failed to fetch remittance" });
    }
  });
  
  app.get("/api/remittances/:id/download", async (req: Request, res: Response) => {
    try {
      const remittance = await storage.getRemittance(parseInt(req.params.id));
      if (!remittance) {
        return res.status(404).json({ error: "Remittance not found" });
      }
      
      // Get file content
      const fileContent = await storage.getFileContent(remittance.file_path);
      
      // Set appropriate headers for download
      res.setHeader('Content-Type', 'text/plain');
      res.setHeader('Content-Disposition', `attachment; filename=remittance_${remittance.id}_${remittance.format}.txt`);
      res.send(fileContent);
    } catch (error) {
      console.error("Error downloading remittance:", error);
      res.status(500).json({ error: "Failed to download remittance" });
    }
  });
  
  app.get("/api/remittances/:id/debug", async (req: Request, res: Response) => {
    try {
      const remittance = await storage.getRemittance(parseInt(req.params.id));
      if (!remittance) {
        return res.status(404).json({ error: "Remittance not found" });
      }
      
      // Get file content
      const fileContent = await storage.getFileContent(remittance.file_path);
      
      // Return content as text
      res.setHeader('Content-Type', 'text/plain');
      res.send(fileContent);
    } catch (error) {
      console.error("Error debugging remittance:", error);
      res.status(500).json({ error: "Failed to debug remittance" });
    }
  });
  
  // Invoice API
  app.get("/api/invoices", async (req: Request, res: Response) => {
    try {
      // Check and update invoice statuses based on due dates
      await updateOverdueInvoices();
      
      const status = req.query.status as string | undefined;
      const invoices = status 
        ? await storage.getInvoicesByStatus(status)
        : await storage.getAllInvoices();
      
      res.json(invoices);
    } catch (error) {
      console.error("Error fetching invoices:", error);
      res.status(500).json({ error: "Failed to fetch invoices" });
    }
  });
  
  // Helper function to update invoice statuses based on due dates
  async function updateOverdueInvoices() {
    try {
      // Get all open invoices
      const openInvoices = await storage.getInvoicesByStatus("Open");
      const today = new Date();
      
      // Check each invoice if it's overdue
      for (const invoice of openInvoices) {
        const dueDate = new Date(invoice.due_date);
        
        // If due date has passed, update status to "Overdue"
        if (dueDate < today) {
          console.log(`Updating invoice ${invoice.id} status to Overdue (due date: ${dueDate.toDateString()})`);
          await storage.updateInvoiceStatus(invoice.id, "Overdue");
        }
      }
    } catch (error) {
      console.error("Error updating overdue invoices:", error);
    }
  }
  
  app.get("/api/invoices/:id", async (req: Request, res: Response) => {
    try {
      const invoice = await storage.getInvoice(parseInt(req.params.id));
      if (!invoice) {
        return res.status(404).json({ error: "Invoice not found" });
      }
      res.json(invoice);
    } catch (error) {
      console.error("Error fetching invoice:", error);
      res.status(500).json({ error: "Failed to fetch invoice" });
    }
  });
  
  app.post("/api/invoices", async (req: Request, res: Response) => {
    try {
      const invoiceData = insertInvoiceSchema.parse(req.body);
      const invoice = await storage.createInvoice(invoiceData);
      res.json(invoice);
    } catch (error) {
      console.error("Error creating invoice:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to create invoice" });
    }
  });
  
  app.post("/api/invoices/:id/update-status", async (req: Request, res: Response) => {
    try {
      const statusSchema = z.enum(["Open", "Overdue", "Paid", "Remitted"]);
      const { status } = z.object({ status: statusSchema }).parse(req.body);
      
      const invoice = await storage.updateInvoiceStatus(parseInt(req.params.id), status);
      if (!invoice) {
        return res.status(404).json({ error: "Invoice not found" });
      }
      
      res.json(invoice);
    } catch (error) {
      console.error("Error updating invoice status:", error);
      res.status(500).json({ error: "Failed to update invoice status" });
    }
  });
  
  app.post("/api/invoices/:id/generate-remittance", async (req: Request, res: Response) => {
    try {
      const formatSchema = z.enum(fileFormats.remittance);
      const parsedBody = z.object({ format: formatSchema }).parse(req.body);
      const format = parsedBody.format;
      
      const invoiceId = parseInt(req.params.id);
      const invoice = await storage.getInvoice(invoiceId);
      
      if (!invoice) {
        return res.status(404).json({ error: "Invoice not found" });
      }
      
      // Allow regeneration of remittance for both Paid and Remitted statuses
      if (!["Paid", "Remitted"].includes(invoice.status) || !invoice.payment_id) {
        return res.status(400).json({ error: "Invoice must be paid before generating remittance" });
      }
      
      // Get the associated payment
      const payment = await storage.getReceivedPayment(invoice.payment_id);
      if (!payment) {
        return res.status(400).json({ error: "Associated payment not found" });
      }
      
      // Generate remittance content
      const remittanceContent = generateRemittanceFile(payment, invoice, format);
      
      // Save the file
      const filePath = await storage.saveFile(remittanceContent, "invoice_remittance", format);
      
      // Create remittance record
      const remittance = await storage.createRemittance({
        payment_id: payment.id,
        format,
        file_path: filePath,
        amount: invoice.amount,
        sender: payment.sender,
        recipient: invoice.customer, // Use the invoice customer as recipient
        reference: invoice.reference
      });
      
      // Update payment's remittance status and invoice status
      await storage.updateReceivedPaymentRemittanceStatus(payment.id, remittance.id);
      
      // Also update the invoice's remittance info
      await storage.updateInvoiceStatus(invoiceId, "Remitted");
      
      // Update additional invoice fields for remittance
      const [updatedInvoice] = await db
        .update(invoices)
        .set({ 
          remittance_id: remittance.id,
          remittance_generated: true,
          remittance_generated_at: new Date()
        })
        .where(eq(invoices.id, invoiceId))
        .returning();
      
      res.json(remittance);
    } catch (error) {
      console.error("Error generating invoice remittance:", error);
      res.status(500).json({ error: "Failed to generate invoice remittance" });
    }
  });
  
  app.get("/api/invoices/:id/download-remittance", async (req: Request, res: Response) => {
    try {
      const invoice = await storage.getInvoice(parseInt(req.params.id));
      if (!invoice || !invoice.remittance_id) {
        return res.status(404).json({ error: "Invoice remittance not found" });
      }
      
      const remittance = await storage.getRemittance(invoice.remittance_id);
      if (!remittance) {
        return res.status(404).json({ error: "Remittance not found" });
      }
      
      // Get file content
      const fileContent = await storage.getFileContent(remittance.file_path);
      
      // Set appropriate headers for download
      res.setHeader('Content-Type', 'text/plain');
      res.setHeader('Content-Disposition', `attachment; filename=invoice_remittance_${invoice.id}_${remittance.format}.txt`);
      res.send(fileContent);
    } catch (error) {
      console.error("Error downloading invoice remittance:", error);
      res.status(500).json({ error: "Failed to download invoice remittance" });
    }
  });
  
  app.post("/api/upload_invoice", upload.single("file"), async (req: Request, res: Response) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: "No file uploaded" });
      }
      
      // Convert buffer to string
      const fileContent = req.file.buffer.toString('utf8');
      
      // Force the file format based on the content and route context
      // First, try to detect the format
      const detectedFormat = detectFileFormat(fileContent);
      console.log(`Detected format in upload_invoice: ${JSON.stringify(detectedFormat)}`);
      
      let invoiceData;
      
      // If it's detected as a payment file but we're in the invoice route,
      // try to process it as an invoice anyway since the context indicates it should be an invoice
      if (detectedFormat.type === 'payment') {
        console.log(`File detected as payment, but uploaded to invoice endpoint. Attempting to treat as invoice...`);
        
        // Try each invoice format
        try {
          if (fileContent.includes('ISA*') || fileContent.includes('GS*')) {
            console.log("Trying to process as EDI X12 invoice");
            invoiceData = processInvoiceFile(fileContent, 'EDI X12');
          } else if (fileContent.includes('<?xml')) {
            console.log("Trying to process as ISO20022 invoice");
            invoiceData = processInvoiceFile(fileContent, 'ISO20022');
          } else {
            // Create a mock ISO20022 structure from a PEXR2002-like format
            console.log("Converting PEXR2002-like format to ISO20022 for invoice processing");
            const lines = fileContent.trim().split('\n');
            
            // Extract potential invoice fields from PEXR2002-like format
            const reference = lines.find(l => l.startsWith('REF:'))?.substring(4).trim() || 
                           `INV-${new Date().toISOString().split('T')[0].replace(/-/g, '')}-${Math.floor(Math.random() * 1000)}`;
            const amountStr = lines.find(l => l.startsWith('AMT:'))?.substring(4).trim() || '1000.00';
            const amount = parseFloat(amountStr);
            const customer = lines.find(l => l.startsWith('SND:'))?.substring(4).trim() || 'Unknown Customer';
            
            // Create a simple invoice-like XML
            const mockXml = `<?xml version="1.0" encoding="UTF-8"?>
<Document>
  <CstmrInvcData>
    <Invc>
      <InvcId>${reference}</InvcId>
      <TtlInvcAmt>${amount}</TtlInvcAmt>
      <Dbtr><Nm>${customer}</Nm></Dbtr>
      <RmtInf><AddtlInf>Invoice generated from file upload</AddtlInf></RmtInf>
    </Invc>
  </CstmrInvcData>
</Document>`;
            
            invoiceData = processInvoiceFile(mockXml, 'ISO20022');
          }
        } catch (error) {
          console.error("Failed to convert payment format to invoice:", error);
          throw new Error(`Could not process file as invoice: ${error instanceof Error ? error.message : String(error)}`);
        }
      } else {
        // Normal processing for detected invoice files
        invoiceData = processInvoiceFile(fileContent);
      }
      
      console.log("Invoice data parsed successfully:", invoiceData.reference);
    
      // Check for duplicate reference number
      const existingInvoice = await storage.getInvoiceByReference(invoiceData.reference);
      if (existingInvoice) {
        return res.status(400).json({ 
          error: `Invoice with reference number "${invoiceData.reference}" already exists (ID: ${existingInvoice.id})` 
        });
      }
      
      // Create invoice record
      const invoice = await storage.createInvoice(invoiceData);
      
      // Auto-link to any matching received payment if one exists
      // Look for any received payment with the same reference number
      const allReceivedPayments = await storage.getAllReceivedPayments();
      const matchingPayment = allReceivedPayments.find(payment => 
        // Exact match for reference numbers
        payment.reference === invoice.reference || 
        // Handle REF prefix variations but keep the unique part
        (payment.reference.includes('-') && invoice.reference.includes('-') &&
         payment.reference.split('-').slice(1).join('-') === invoice.reference.split('-').slice(1).join('-'))
      );
      
      if (matchingPayment && !matchingPayment.invoice_id) {
        console.log(`Auto-linking invoice ${invoice.id} to received payment ${matchingPayment.id}`);
        // Link the matching payment to the new invoice
        await storage.linkReceivedPaymentToInvoice(matchingPayment.id, invoice.id);
        
        // Update the invoice status to "Paid"
        await storage.updateInvoiceStatus(invoice.id, "Paid");
        
        // Return the updated invoice
        const updatedInvoice = await storage.getInvoice(invoice.id);
        res.json(updatedInvoice);
      } else {
        res.json(invoice);
      }
    } catch (error) {
      console.error("Error uploading invoice file:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to upload invoice file" });
    }
  });
  
  app.get("/api/sample_invoice/:format", async (req: Request, res: Response) => {
    try {
      const formatSchema = z.enum(fileFormats.invoice);
      const format = formatSchema.parse(req.params.format);
      
      // Generate sample file
      const sampleContent = generateSampleInvoiceFile(format);
      console.log(`Generated sample ${format} invoice content (first 100 chars): ${sampleContent.substring(0, 100)}`);
      
      // Validate the sample by testing our detection logic
      const detectedFormat = detectFileFormat(sampleContent);
      console.log(`Sample invoice detection result: ${JSON.stringify(detectedFormat)}`);
      
      if (detectedFormat.type !== 'invoice') {
        console.error(`Warning: Generated sample ${format} invoice was detected as ${detectedFormat.type}`);
      }
      
      // Set appropriate content type and filename extension based on format
      let filename = `sample_invoice_${format}.txt`;
      let contentType = 'text/plain';
      
      if (format === 'ISO20022') {
        filename = `sample_invoice_${format}.xml`;
        contentType = 'application/xml';
      }
      
      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
      res.send(sampleContent);
    } catch (error) {
      console.error("Error generating sample invoice file:", error);
      res.status(500).json({ error: "Failed to generate sample invoice file" });
    }
  });
  
  // Sample Remittance Files API endpoint
  app.get("/api/sample_remittance/:format", async (req: Request, res: Response) => {
    try {
      const formatSchema = z.enum(fileFormats.remittance);
      const format = formatSchema.parse(req.params.format);
      
      // Generate sample remittance file
      const sampleContent = generateSampleRemittanceFile(format);
      
      // Set appropriate content type
      res.setHeader('Content-Type', 'text/plain');
      res.setHeader('Content-Disposition', `attachment; filename=sample_remittance_${format}.txt`);
      res.send(sampleContent);
    } catch (error) {
      console.error("Error generating sample remittance file:", error);
      res.status(500).json({ error: "Failed to generate sample remittance file" });
    }
  });
  
  // Received Payments API
  app.get("/api/received-payments", async (req: Request, res: Response) => {
    try {
      const receivedPayments = await storage.getAllReceivedPayments();
      res.json(receivedPayments);
    } catch (error) {
      console.error("Error fetching received payments:", error);
      res.status(500).json({ error: "Failed to fetch received payments" });
    }
  });
  
  app.get("/api/received-payments/:id", async (req: Request, res: Response) => {
    try {
      const receivedPayment = await storage.getReceivedPayment(parseInt(req.params.id));
      if (!receivedPayment) {
        return res.status(404).json({ error: "Received payment not found" });
      }
      res.json(receivedPayment);
    } catch (error) {
      console.error("Error fetching received payment:", error);
      res.status(500).json({ error: "Failed to fetch received payment" });
    }
  });
  
  app.post("/api/received-payments/:id/link-invoice", async (req: Request, res: Response) => {
    try {
      const { invoiceId } = z.object({ invoiceId: z.number() }).parse(req.body);
      
      const paymentId = parseInt(req.params.id);
      
      // First link the payment to the invoice
      const receivedPayment = await storage.linkReceivedPaymentToInvoice(paymentId, invoiceId);
      
      if (!receivedPayment) {
        return res.status(404).json({ error: "Received payment or invoice not found" });
      }
      
      // Ensure that we also update the invoice status to "Paid"
      // This is a crucial step to maintain data consistency
      const updatedInvoice = await storage.updateInvoiceStatus(invoiceId, "Paid");
      
      if (!updatedInvoice) {
        console.warn(`Failed to update invoice ${invoiceId} status to Paid after linking to payment ${paymentId}`);
      } else {
        console.log(`Successfully updated invoice ${invoiceId} status to Paid after linking to payment ${paymentId}`);
      }
      
      res.json({
        receivedPayment,
        invoice: updatedInvoice
      });
    } catch (error) {
      console.error("Error linking received payment to invoice:", error);
      res.status(500).json({ error: "Failed to link received payment to invoice" });
    }
  });
  
  app.post("/api/simulate_received_payment", async (req: Request, res: Response) => {
    try {
      // Only use fields that exist in the database
      const paymentData = {
        reference: req.body.reference,
        amount: req.body.amount,
        sender: req.body.sender,
        recipient: req.body.recipient || "Your Company",
        invoice_id: req.body.invoice_id || null
      };
      const receivedPayment = await storage.createReceivedPayment(paymentData);
      
      // Auto-linking to invoices has been disabled to allow for manual linking
      // This enables testing the full AR workflow with manual reconciliation
      console.log(`Created received payment ${receivedPayment.id} with reference ${receivedPayment.reference}`);
      
      res.json(receivedPayment);
    } catch (error) {
      console.error("Error creating received payment:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to create received payment" });
    }
  });
  
  /**
   * Import Received Payment (Accounts Receivable)
   * 
   * This endpoint allows manual import of payment data for the Accounts Receivable workflow.
   * It accepts a JSON object with payment details and creates a received payment record.
   * 
   * The endpoint handles both "sender" and "from" field names for flexibility,
   * accommodating both UI requirements and simulation formats.
   * 
   * Request body format:
   * {
   *   "from": "Sender Company Name",  // or "sender"
   *   "amount": 1000.00,
   *   "reference": "REF-12345"
   * }
   */
  app.post('/api/import/received-payment', async (req: Request, res: Response) => {
    try {
      // Accept both "sender" (from frontend) and "from" (from network simulator) field names
      const { sender, from, amount, reference } = req.body;
      const senderName = sender || from; // Use sender if provided, otherwise use from
      
      if (!senderName) {
        return res.status(400).json({ error: "Sender name is required" });
      }
      
      const payment = await storage.markPaymentReceived({ from: senderName, amount, reference });
      if (!payment) return res.status(404).send('Reference not found');
      res.json(payment);
    } catch (error) {
      console.error("Error importing received payment:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to import received payment" });
    }
  });
  
  /**
   * Import Receipt (Accounts Payable)
   * 
   * This endpoint allows manual import of receipt data for the Accounts Payable workflow.
   * It accepts a JSON object with receipt details and marks a payment as having its receipt imported.
   * 
   * The endpoint maintains the payment's "Paid" status while making it appear in the reconciliation
   * column, enabling manual verification before generating reconciliation documents.
   * 
   * Request body format:
   * {
   *   "account": "Recipient Account ID",
   *   "amount": 1000.00,
   *   "reference": "REF-12345"
   * }
   */
  app.post('/api/import/receipt', async (req: Request, res: Response) => {
    try {
      const { account, amount, reference } = req.body;
      const pay = await storage.markReceiptImported({ account, amount, reference });
      if (!pay) return res.status(404).send('Reference not found');
      res.json(pay);
    } catch (error) {
      console.error("Error importing receipt:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to import receipt" });
    }
  });
  
  return httpServer;
}

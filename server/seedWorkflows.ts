import fs from 'fs';
import path from 'path';
import { ERPParserFactory } from '../client/src/services/erpParsers';
import { WorkflowDefinition, ERPVendor } from '../client/src/types/workflow';

/**
 * Seed script to automatically import demo workflow definitions
 * This script loads the demo-rules package and imports all workflows
 */

interface SeedFile {
  filename: string;
  vendor: ERPVendor;
  description: string;
}

const DEMO_FILES: SeedFile[] = [
  {
    filename: 'sap_release_strategy.json',
    vendor: 'SAP',
    description: 'SAP S/4HANA Release Strategy workflows'
  },
  {
    filename: 'oracle_bpm.xlsx',
    vendor: 'Oracle', 
    description: 'Oracle BPM Suite approval workflows'
  },
  {
    filename: 'd365_workflow.xml',
    vendor: 'Dynamics365',
    description: 'Microsoft Dynamics 365 F&O workflows'
  },
  {
    filename: 'netsuite_bundle.xml',
    vendor: 'NetSuite',
    description: 'NetSuite SuiteFlow approval workflows'
  }
];

export async function seedDemoWorkflows(): Promise<void> {
  console.log('🌱 Starting workflow seed process...');
  
  const demoDataPath = path.join(__dirname, '..', 'demo-data');
  
  if (!fs.existsSync(demoDataPath)) {
    console.error('❌ Demo data directory not found:', demoDataPath);
    return;
  }

  const results = {
    imported: 0,
    failed: 0,
    workflows: [] as WorkflowDefinition[]
  };

  for (const seedFile of DEMO_FILES) {
    try {
      console.log(`📁 Processing ${seedFile.filename} (${seedFile.vendor})...`);
      
      const filePath = path.join(demoDataPath, seedFile.filename);
      
      if (!fs.existsSync(filePath)) {
        console.warn(`⚠️  File not found: ${seedFile.filename}`);
        results.failed++;
        continue;
      }

      const content = fs.readFileSync(filePath, 'utf-8');
      
      // Parse the file using the ERP parser
      const importResult = await ERPParserFactory.parseFile(
        seedFile.vendor,
        content,
        seedFile.filename
      );

      if (importResult.errors.length > 0) {
        console.error(`❌ Errors parsing ${seedFile.filename}:`, importResult.errors);
        results.failed++;
        continue;
      }

      if (importResult.warnings.length > 0) {
        console.warn(`⚠️  Warnings for ${seedFile.filename}:`, importResult.warnings);
      }

      // Convert parsed workflows to full definitions
      const workflows = importResult.workflows.map((workflow, index) => {
        const fullWorkflow: WorkflowDefinition = {
          id: `demo-${seedFile.vendor.toLowerCase()}-${index + 1}`,
          name: workflow.name || `${seedFile.vendor} Workflow ${index + 1}`,
          description: workflow.description || seedFile.description,
          version: workflow.version || '1.0.0',
          documentType: workflow.documentType || 'INVOICE',
          status: 'ACTIVE',
          steps: workflow.steps || [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: 'DEMO_SEED',
          source: {
            vendor: seedFile.vendor,
            importedAt: new Date().toISOString(),
            originalFormat: path.extname(seedFile.filename).slice(1)
          }
        };

        return fullWorkflow;
      });

      results.workflows.push(...workflows);
      results.imported++;

      console.log(`✅ Successfully imported ${workflows.length} workflow(s) from ${seedFile.filename}`);

    } catch (error) {
      console.error(`❌ Failed to process ${seedFile.filename}:`, error.message);
      results.failed++;
    }
  }

  // Save the imported workflows (in a real app, this would save to database)
  const outputPath = path.join(__dirname, '..', 'demo-workflows.json');
  fs.writeFileSync(outputPath, JSON.stringify({
    metadata: {
      importedAt: new Date().toISOString(),
      totalFiles: DEMO_FILES.length,
      successfulImports: results.imported,
      failedImports: results.failed,
      totalWorkflows: results.workflows.length
    },
    workflows: results.workflows
  }, null, 2));

  console.log('\n📊 Seed Summary:');
  console.log(`   Files processed: ${DEMO_FILES.length}`);
  console.log(`   Successful imports: ${results.imported}`);
  console.log(`   Failed imports: ${results.failed}`);
  console.log(`   Total workflows: ${results.workflows.length}`);
  console.log(`   Output saved to: ${outputPath}`);

  if (results.workflows.length > 0) {
    console.log('\n🎯 Demo Workflows Created:');
    results.workflows.forEach(workflow => {
      console.log(`   • ${workflow.name} (${workflow.source?.vendor}) - ${workflow.steps.length} steps`);
    });
  }

  console.log('\n🚀 Workflow seeding complete!');
}

// Auto-run if called directly
if (require.main === module) {
  seedDemoWorkflows().catch(console.error);
}

/**
 * Demo workflow evaluation scenarios for testing
 */
export const DEMO_SCENARIOS = [
  {
    name: 'Low Value Invoice',
    document: {
      id: 'INV-001',
      type: 'INVOICE' as const,
      amount: 2500,
      currency: 'USD',
      supplier: 'Acme Corp',
      costCenter: 'IT',
      department: 'Technology',
      country: 'US',
      urgency: 'MEDIUM' as const,
      riskScore: 0.2,
      metadata: {
        invoiceDate: '2024-01-26',
        dueDate: '2024-02-25',
        category: 'Software License'
      }
    },
    expectedApprovers: ['<EMAIL>'],
    expectedMode: 'SERIAL'
  },
  {
    name: 'High Value Invoice',
    document: {
      id: 'INV-002',
      type: 'INVOICE' as const,
      amount: 75000,
      currency: 'USD',
      supplier: 'Enterprise Solutions Inc',
      costCenter: 'CAPEX',
      department: 'Operations',
      country: 'US',
      urgency: 'HIGH' as const,
      riskScore: 0.8,
      metadata: {
        invoiceDate: '2024-01-26',
        dueDate: '2024-02-15',
        category: 'Equipment Purchase'
      }
    },
    expectedApprovers: ['<EMAIL>', '<EMAIL>'],
    expectedMode: 'SERIAL'
  },
  {
    name: 'Foreign Currency Invoice',
    document: {
      id: 'INV-003',
      type: 'INVOICE' as const,
      amount: 150000,
      currency: 'EUR',
      supplier: 'European Vendor GmbH',
      costCenter: 'SALES',
      department: 'Sales',
      country: 'DE',
      urgency: 'CRITICAL' as const,
      riskScore: 0.6,
      metadata: {
        invoiceDate: '2024-01-26',
        dueDate: '2024-02-10',
        category: 'Professional Services'
      }
    },
    expectedApprovers: ['<EMAIL>'],
    expectedMode: 'SERIAL'
  },
  {
    name: 'Auto-Approval Scenario',
    document: {
      id: 'INV-004',
      type: 'INVOICE' as const,
      amount: 75,
      currency: 'USD',
      supplier: 'VENDOR_001',
      costCenter: 'OFFICE',
      department: 'Administration',
      country: 'US',
      urgency: 'LOW' as const,
      riskScore: 0.1,
      metadata: {
        invoiceDate: '2024-01-26',
        dueDate: '2024-02-26',
        category: 'Office Supplies'
      }
    },
    expectedApprovers: [],
    expectedMode: 'AUTO_APPROVED'
  }
];

/**
 * Test all demo scenarios against imported workflows
 */
export async function testDemoScenarios(): Promise<void> {
  console.log('🧪 Testing demo scenarios...');
  
  // This would integrate with the workflow engine in a real implementation
  for (const scenario of DEMO_SCENARIOS) {
    console.log(`\n📋 Testing: ${scenario.name}`);
    console.log(`   Amount: ${scenario.document.currency} ${scenario.document.amount.toLocaleString()}`);
    console.log(`   Expected: ${scenario.expectedMode} - ${scenario.expectedApprovers.join(', ') || 'Auto-approved'}`);
    
    // In a real implementation, this would call:
    // const result = await workflowEngine.evaluate(workflow, scenario.document);
    // console.log(`   Result: ${result.autoApproved ? 'Auto-approved' : result.approvers.length + ' approver(s)'}`);
  }
  
  console.log('\n✅ Demo scenario testing complete!');
}

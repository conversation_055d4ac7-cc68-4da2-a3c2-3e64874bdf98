import { 
  users, payments, remittances, invoices, receivedPayments,
  type User, type InsertUser, type Payment, type InsertPayment,
  type Remittance, type InsertRemittance, type Invoice, type InsertInvoice,
  type ReceivedPayment, type InsertReceivedPayment
} from "@shared/schema";
import { db } from "./db";
import { eq, and, isNull } from "drizzle-orm";
import { PaymentFileFormat, RemittanceFileFormat } from "@shared/schema";
import fs from 'fs';
import path from 'path';

// Storage interface with all CRUD methods needed
export interface IStorage {
  // User methods (keeping existing ones)
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Payment methods
  createPayment(payment: InsertPayment): Promise<Payment>;
  getPayment(id: number): Promise<Payment | undefined>;
  getPaymentByReference(reference: string): Promise<Payment | undefined>;
  getAllPayments(): Promise<Payment[]>;
  getPaymentsByStatus(status: string): Promise<Payment[]>;
  approvePayment(id: number): Promise<Payment | undefined>;
  revokePaymentApproval(id: number): Promise<Payment | undefined>; 
  sendPayment(id: number): Promise<Payment | undefined>;
  updatePaymentRemittanceStatus(id: number, remittanceId: number): Promise<Payment | undefined>;
  
  // Remittance methods
  createRemittance(remittance: InsertRemittance): Promise<Remittance>;
  getRemittance(id: number): Promise<Remittance | undefined>;
  getAllRemittances(): Promise<Remittance[]>;
  getRemittancesByPaymentId(paymentId: number): Promise<Remittance[]>;
  
  // Invoice methods
  createInvoice(invoice: InsertInvoice): Promise<Invoice>;
  getInvoice(id: number): Promise<Invoice | undefined>;
  getInvoiceByReference(reference: string): Promise<Invoice | undefined>;
  getAllInvoices(): Promise<Invoice[]>;
  getInvoicesByStatus(status: string): Promise<Invoice[]>;
  updateInvoiceStatus(id: number, status: string): Promise<Invoice | undefined>;
  linkInvoiceToPayment(id: number, paymentId: number): Promise<Invoice | undefined>;
  
  // Received Payment methods
  createReceivedPayment(receivedPayment: InsertReceivedPayment): Promise<ReceivedPayment>;
  getReceivedPayment(id: number): Promise<ReceivedPayment | undefined>;
  getAllReceivedPayments(): Promise<ReceivedPayment[]>;
  getReceivedPaymentsByStatus(status: string): Promise<ReceivedPayment[]>;
  linkReceivedPaymentToInvoice(id: number, invoiceId: number): Promise<ReceivedPayment | undefined>;
  updateReceivedPaymentRemittanceStatus(id: number, remittanceId: number): Promise<ReceivedPayment | undefined>;
  
  // Manual import methods (for payment reconciliation)
  markPaymentReceived(data: { from: string, amount: number, reference: string }): Promise<ReceivedPayment | undefined>;
  markReceiptImported(data: { account: string, amount: number, reference: string }): Promise<Payment | undefined>;
  
  // File storage methods
  saveFile(content: string, type: string, format: string): Promise<string>;
  getFileContent(filePath: string): Promise<string>;
}

// Database implementation of Storage interface
export class DatabaseStorage implements IStorage {
  private filesDir: string;
  
  constructor() {
    // Create directory for storing files if it doesn't exist
    this.filesDir = path.join(process.cwd(), 'files');
    if (!fs.existsSync(this.filesDir)) {
      fs.mkdirSync(this.filesDir, { recursive: true });
    }
  }
  
  // User methods
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user;
  }

  async createUser(user: InsertUser): Promise<User> {
    const [createdUser] = await db.insert(users).values(user).returning();
    return createdUser;
  }
  
  // Payment methods
  async getPaymentByReference(reference: string): Promise<Payment | undefined> {
    const [payment] = await db.select().from(payments).where(eq(payments.reference, reference));
    return payment;
  }

  async createPayment(payment: InsertPayment): Promise<Payment> {
    const [createdPayment] = await db.insert(payments).values(payment).returning();
    return createdPayment;
  }
  
  async getPayment(id: number): Promise<Payment | undefined> {
    const [payment] = await db.select().from(payments).where(eq(payments.id, id));
    return payment;
  }
  
  async getAllPayments(): Promise<Payment[]> {
    return await db.select().from(payments);
  }
  
  async getPaymentsByStatus(status: string): Promise<Payment[]> {
    return await db.select().from(payments).where(eq(payments.status, status));
  }
  
  async approvePayment(id: number, signature?: string, message?: string): Promise<Payment | undefined> {
    const timestamp = new Date();
    const [updatedPayment] = await db
      .update(payments)
      .set({ 
        approved: true, 
        status: "Approved",
        approved_at: timestamp,
        signature: signature || null,
        message: message || null
      })
      .where(eq(payments.id, id))
      .returning();
    
    return updatedPayment;
  }
  
  async revokePaymentApproval(id: number): Promise<Payment | undefined> {
    const [updatedPayment] = await db
      .update(payments)
      .set({ 
        approved: false, 
        status: "Not Approved" 
      })
      .where(eq(payments.id, id))
      .returning();
    
    return updatedPayment;
  }
  
  async sendPayment(id: number): Promise<Payment | undefined> {
    const [updatedPayment] = await db
      .update(payments)
      .set({ 
        sent_at: new Date(), 
        status: "Paid" // Ensuring we use "Paid" status consistently
      })
      .where(eq(payments.id, id))
      .returning();
    
    return updatedPayment;
  }
  
  async updatePaymentRemittanceStatus(id: number, remittanceId: number): Promise<Payment | undefined> {
    const timestamp = new Date();
    const [updatedPayment] = await db
      .update(payments)
      .set({ 
        remittance_generated: true,
        remittance_generated_at: timestamp,
        remittance_id: remittanceId,
        status: "Remitted"
      })
      .where(eq(payments.id, id))
      .returning();
    
    return updatedPayment;
  }
  
  // Remittance methods
  async createRemittance(remittance: InsertRemittance): Promise<Remittance> {
    const [createdRemittance] = await db.insert(remittances).values(remittance).returning();
    return createdRemittance;
  }
  
  async getRemittance(id: number): Promise<Remittance | undefined> {
    const [remittance] = await db.select().from(remittances).where(eq(remittances.id, id));
    return remittance;
  }
  
  async getAllRemittances(): Promise<Remittance[]> {
    return await db.select().from(remittances);
  }
  
  async getRemittancesByPaymentId(paymentId: number): Promise<Remittance[]> {
    return await db.select().from(remittances).where(eq(remittances.payment_id, paymentId));
  }
  
  // Invoice methods
  async getInvoiceByReference(reference: string): Promise<Invoice | undefined> {
    const [invoice] = await db.select().from(invoices).where(eq(invoices.reference, reference));
    return invoice;
  }
  
  async createInvoice(invoice: InsertInvoice): Promise<Invoice> {
    const [createdInvoice] = await db.insert(invoices).values(invoice).returning();
    return createdInvoice;
  }
  
  async getInvoice(id: number): Promise<Invoice | undefined> {
    const [invoice] = await db.select().from(invoices).where(eq(invoices.id, id));
    return invoice;
  }
  
  async getAllInvoices(): Promise<Invoice[]> {
    return await db.select().from(invoices);
  }
  
  async getInvoicesByStatus(status: string): Promise<Invoice[]> {
    return await db.select().from(invoices).where(eq(invoices.status, status));
  }
  
  async updateInvoiceStatus(id: number, status: string): Promise<Invoice | undefined> {
    const [updatedInvoice] = await db
      .update(invoices)
      .set({ status })
      .where(eq(invoices.id, id))
      .returning();
    
    return updatedInvoice;
  }
  
  async linkInvoiceToPayment(id: number, paymentId: number): Promise<Invoice | undefined> {
    const [updatedInvoice] = await db
      .update(invoices)
      .set({ 
        payment_id: paymentId,
        status: "Paid" 
      })
      .where(eq(invoices.id, id))
      .returning();
    
    return updatedInvoice;
  }
  
  // Received Payment methods
  async createReceivedPayment(receivedPayment: InsertReceivedPayment): Promise<ReceivedPayment> {
    // Set initial status to Unlinked
    const receivedPaymentWithStatus = {
      ...receivedPayment,
      status: "Unlinked"
    };
    
    const [createdPayment] = await db.insert(receivedPayments).values(receivedPaymentWithStatus).returning();
    
    // Auto-link to invoice if reference matches
    if (createdPayment.reference) {
      const [matchingInvoice] = await db
        .select()
        .from(invoices)
        .where(eq(invoices.reference, createdPayment.reference));
      
      if (matchingInvoice) {
        // Update the received payment with invoice ID and Linked status
        const [updatedPayment] = await db
          .update(receivedPayments)
          .set({ 
            invoice_id: matchingInvoice.id,
            status: "Linked"
          })
          .where(eq(receivedPayments.id, createdPayment.id))
          .returning();
        
        // Update the invoice status
        await db
          .update(invoices)
          .set({ 
            status: "Paid",
            payment_id: createdPayment.id 
          })
          .where(eq(invoices.id, matchingInvoice.id));
        
        return updatedPayment;
      }
    }
    
    return createdPayment;
  }
  
  async getReceivedPayment(id: number): Promise<any | undefined> {
    const [receivedPayment] = await db.select({
      id: receivedPayments.id,
      sender: receivedPayments.sender,
      amount: receivedPayments.amount,
      reference: receivedPayments.reference,
      created_at: receivedPayments.created_at,
      invoice_id: receivedPayments.invoice_id,
      status: receivedPayments.status,
      remittance_generated: receivedPayments.remittance_generated,
      remittance_generated_at: receivedPayments.remittance_generated_at,
      remittance_id: receivedPayments.remittance_id
    }).from(receivedPayments).where(eq(receivedPayments.id, id));
    return receivedPayment;
  }
  
  async getAllReceivedPayments(): Promise<any[]> {
    // Only select the columns that exist in the actual database
    return await db.select({
      id: receivedPayments.id,
      sender: receivedPayments.sender,
      amount: receivedPayments.amount,
      reference: receivedPayments.reference,
      created_at: receivedPayments.created_at,
      invoice_id: receivedPayments.invoice_id,
      status: receivedPayments.status,
      remittance_generated: receivedPayments.remittance_generated,
      remittance_generated_at: receivedPayments.remittance_generated_at,
      remittance_id: receivedPayments.remittance_id
    }).from(receivedPayments);
  }
  
  async getReceivedPaymentsByStatus(status: string): Promise<any[]> {
    // Only select the columns that exist in the actual database
    return await db.select({
      id: receivedPayments.id,
      sender: receivedPayments.sender,
      amount: receivedPayments.amount,
      reference: receivedPayments.reference,
      created_at: receivedPayments.created_at,
      invoice_id: receivedPayments.invoice_id,
      status: receivedPayments.status,
      remittance_generated: receivedPayments.remittance_generated,
      remittance_generated_at: receivedPayments.remittance_generated_at,
      remittance_id: receivedPayments.remittance_id
    }).from(receivedPayments).where(eq(receivedPayments.status, status));
  }
  
  async linkReceivedPaymentToInvoice(id: number, invoiceId: number): Promise<any | undefined> {
    console.log(`Linking received payment ${id} to invoice ${invoiceId}`);
    
    const [updatedPayment] = await db
      .update(receivedPayments)
      .set({ 
        invoice_id: invoiceId,
        status: "Linked"
      })
      .where(eq(receivedPayments.id, id))
      .returning({
        id: receivedPayments.id,
        sender: receivedPayments.sender,
        amount: receivedPayments.amount,
        reference: receivedPayments.reference,
        created_at: receivedPayments.created_at,
        invoice_id: receivedPayments.invoice_id,
        status: receivedPayments.status,
        remittance_generated: receivedPayments.remittance_generated,
        remittance_generated_at: receivedPayments.remittance_generated_at,
        remittance_id: receivedPayments.remittance_id
      });
    
    console.log(`Setting invoice ${invoiceId} status to Paid and payment_id to ${id}`);
    // Also update the invoice status and payment_id
    const [updatedInvoice] = await db
      .update(invoices)
      .set({ 
        status: "Paid",
        payment_id: id 
      })
      .where(eq(invoices.id, invoiceId))
      .returning();
    
    console.log(`Invoice updated:`, updatedInvoice ? `Status: ${updatedInvoice.status}, Payment ID: ${updatedInvoice.payment_id}` : 'No invoice returned');
    
    return updatedPayment;
  }
  
  async updateReceivedPaymentRemittanceStatus(id: number, remittanceId: number): Promise<ReceivedPayment | undefined> {
    const timestamp = new Date();
    const [updatedPayment] = await db
      .update(receivedPayments)
      .set({ 
        remittance_generated: true,
        remittance_generated_at: timestamp,
        remittance_id: remittanceId,
        status: "Remitted" 
      })
      .where(eq(receivedPayments.id, id))
      .returning();
    
    // Update linked invoice if exists
    if (updatedPayment.invoice_id) {
      await db
        .update(invoices)
        .set({ 
          status: "Remitted",
          remittance_id: remittanceId 
        })
        .where(eq(invoices.id, updatedPayment.invoice_id));
    }
    
    return updatedPayment;
  }
  
  // File storage methods
  async saveFile(content: string, type: string, format: string): Promise<string> {
    const timestamp = Date.now();
    const filename = `${type}_${format}_${timestamp}.txt`;
    const filePath = path.join(this.filesDir, filename);
    
    await fs.promises.writeFile(filePath, content, 'utf8');
    return filename;
  }
  
  async getFileContent(filePath: string): Promise<string> {
    const fullPath = path.join(this.filesDir, filePath);
    return await fs.promises.readFile(fullPath, 'utf8');
  }

  // Manual import methods
  /**
   * Import a received payment manually
   * 
   * This method creates a new received payment record based on imported JSON data.
   * It supports the Accounts Receivable workflow by allowing manual import of
   * payment data that would normally come from external systems.
   * 
   * The method accommodates both "sender" and "from" field names for flexibility,
   * allowing users to import payments in various formats.
   * 
   * @param data - Object containing sender name, amount, and reference number
   * @returns The created received payment record
   */
  async markPaymentReceived(data: { from: string, amount: number, reference: string }): Promise<ReceivedPayment | undefined> {
    // First check if the reference exists in our system
    const reference = data.reference;
    
    // Create a received payment record (similar to what the simulator does)
    const receivedPaymentData = {
      reference: reference,
      amount: data.amount,
      sender: data.from,
      recipient: "Your Company",
      invoice_id: null
    };
    
    // Create the received payment record
    const receivedPayment = await this.createReceivedPayment(receivedPaymentData);
    
    // The createReceivedPayment method already handles auto-linking to invoices
    // and setting the appropriate statuses, so we don't need to do anything else
    
    return receivedPayment;
  }
  
  /**
   * Mark a payment as having its receipt imported
   * 
   * This method updates a payment record to indicate a receipt has been imported
   * without changing its status from "Paid" to "Reconciled". This allows payments 
   * to appear in the reconciliation column for manual verification while maintaining
   * their Paid status.
   * 
   * The approach fixes the workflow so that:
   * 1. Sent payments don't automatically populate the reconciliation column
   * 2. Payments with imported receipts show in the reconciliation column
   * 3. Status remains "Paid" until reconciliation is manually generated
   * 
   * @param data - Object containing account ID, amount, and reference number
   * @returns The updated payment record or undefined if not found
   */
  async markReceiptImported(data: { account: string, amount: number, reference: string }): Promise<Payment | undefined> {
    // Find the payment by reference
    const payment = await this.getPaymentByReference(data.reference);
    
    if (!payment) {
      return undefined; // Reference not found
    }
    
    // Keep the status as "Paid" - don't change to "Reconciled"
    // This will make it show up in the reconciliation column without changing its status
    // Only setting receipt_imported to true to mark it for the reconciliation column
    const [updatedPayment] = await db
      .update(payments)
      .set({
        receipt_imported: true // Add a flag to indicate receipt was imported
      })
      .where(eq(payments.reference, data.reference))
      .returning();
      
    return updatedPayment;
  }
}

// Initialize storage as a database storage
export const storage = new DatabaseStorage();

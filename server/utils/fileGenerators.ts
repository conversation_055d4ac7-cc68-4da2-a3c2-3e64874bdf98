import { Payment, Remittance, ReceivedPayment, Invoice, RemittanceFileFormat } from "@shared/schema";

/**
 * Generates remittance files in different formats based on payment information
 */
export function generateRemittanceFile(
  payment: Payment | ReceivedPayment,
  invoice: Invoice | null,
  format: RemittanceFileFormat
): string {
  switch (format) {
    case "MT940":
      return generateMT940(payment, invoice);
    case "BAI2":
      return generateBAI2(payment, invoice);
    case "ISO20022":
      return generateISO20022Remittance(payment, invoice);
    default:
      throw new Error(`Unsupported remittance format: ${format}`);
  }
}

/**
 * Generates a sample remittance file in the specified format
 * This is used for downloadable examples
 */
export function generateSampleRemittanceFile(format: RemittanceFileFormat): string {
  // Create mock payment and invoice data for the sample
  const mockPayment = {
    id: 0,
    reference: `SAMPLE-${new Date().getTime()}`,
    amount: 1250.75,
    sender: "Sample Corporation",
    recipient: "Test Company Ltd",
    recipient_address: "123 Business Street, Finance District, 12345",
    status: "sent",
    file_type: "SAMPLE",
    file_content: "",
    created_at: new Date(),
    remittance_id: null,
    approved: true,
    sent_at: new Date(),
    remittance_generated: false
  };
  
  const mockInvoice = {
    id: 0,
    reference: `INV-SAMPLE-${new Date().getTime()}`,
    customer: "Test Company Ltd",
    amount: 1250.75,
    description: "Sample invoice for demonstration",
    status: "paid",
    due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    created_at: new Date(),
    payment_id: null,
    remittance_id: null
  };
  
  return generateRemittanceFile(mockPayment, mockInvoice, format);
}

// MT940 format generator
function generateMT940(payment: Payment | ReceivedPayment, invoice: Invoice | null): string {
  const now = new Date();
  const dateStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}`;
  
  // For MT940, we need to format amounts in a specific way
  const amountFormatted = payment.amount.toFixed(2).replace('.', ',');
  
  // Build MT940 statement
  let mt940 = `:20:${payment.reference}\n`;
  mt940 += `:25:********\n`;  // Account number
  mt940 += `:28C:1/1\n`;      // Statement number/sequence
  mt940 += `:60F:C${dateStr}USD${amountFormatted}\n`; // Opening balance
  
  // Transaction details
  mt940 += `:61:${dateStr}C${amountFormatted}NTRF\n`;
  mt940 += `:86:Payment reference ${payment.reference}\n`;
  
  if (invoice) {
    mt940 += `Invoice ${invoice.reference}\n`;
  }
  
  // Include sender/recipient info
  mt940 += `From: ${payment.sender}\n`;
  mt940 += `To: ${payment.recipient}\n`;
  if ('recipient_address' in payment && payment.recipient_address) {
    mt940 += `Address: ${payment.recipient_address}\n`;
  }
  
  // Closing balance (same as opening in this simple example)
  mt940 += `:62F:C${dateStr}USD${amountFormatted}\n`;
  
  return mt940;
}

// BAI2 format generator
function generateBAI2(payment: Payment | ReceivedPayment, invoice: Invoice | null): string {
  const now = new Date();
  const dateStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}`;
  
  // Format amount without decimal point (as per BAI2 spec)
  const amountFormatted = Math.round(payment.amount * 100).toString();
  
  // Build BAI2 file
  let bai2 = `01,${dateStr},${dateStr},${now.getHours()}${now.getMinutes()},001,${payment.sender.substring(0, 8)},,,1\n`;
  bai2 += `02,********,1,USD,4,${dateStr},${dateStr},,,01\n`;
  bai2 += `03,${amountFormatted},,,${payment.reference},${payment.sender},${payment.recipient}\n`;
  
  // Add recipient address if available
  if ('recipient_address' in payment && payment.recipient_address) {
    bai2 += `88,RCPT_ADR,${payment.recipient_address}\n`;
  }
  
  if (invoice) {
    bai2 += `16,${invoice.reference},Invoice Payment\n`;
  }
  
  bai2 += `49,1,${amountFormatted}\n`;
  bai2 += `98,1,1,${amountFormatted}\n`;
  bai2 += `99,1,${amountFormatted}\n`;
  
  return bai2;
}

// ISO20022 remittance format generator
function generateISO20022Remittance(payment: Payment | ReceivedPayment, invoice: Invoice | null): string {
  const now = new Date();
  
  // Build ISO20022 XML structure
  let xml = `<?xml version="1.0" encoding="UTF-8"?>\n`;
  xml += `<Document xmlns="urn:iso:std:iso:20022:tech:xsd:camt.054.001.04">\n`;
  xml += `  <BkToCstmrDbtCdtNtfctn>\n`;
  xml += `    <GrpHdr>\n`;
  xml += `      <MsgId>${payment.reference}</MsgId>\n`;
  xml += `      <CreDtTm>${now.toISOString()}</CreDtTm>\n`;
  xml += `    </GrpHdr>\n`;
  xml += `    <Ntfctn>\n`;
  xml += `      <Id>REMIT-${payment.reference}</Id>\n`;
  xml += `      <CreDtTm>${now.toISOString()}</CreDtTm>\n`;
  xml += `      <Acct>\n`;
  xml += `        <Id><IBAN>**********************</IBAN></Id>\n`;
  xml += `        <Ownr><Nm>${payment.recipient}</Nm></Ownr>\n`;
  xml += `      </Acct>\n`;
  xml += `      <Ntry>\n`;
  xml += `        <Amt Ccy="USD">${payment.amount.toFixed(2)}</Amt>\n`;
  xml += `        <CdtDbtInd>CRDT</CdtDbtInd>\n`;
  xml += `        <RmtInf>\n`;
  xml += `          <Strd>\n`;
  xml += `            <RfrdDocInf>\n`;
  xml += `              <Tp><CdOrPrtry><Cd>CINV</Cd></CdOrPrtry></Tp>\n`;
  xml += `              <Nb>${payment.reference}</Nb>\n`;
  xml += `            </RfrdDocInf>\n`;
  
  if (invoice) {
    xml += `            <RfrdDocAmt><DuePyblAmt Ccy="USD">${invoice.amount.toFixed(2)}</DuePyblAmt></RfrdDocAmt>\n`;
    xml += `            <CdtrRefInf><Ref>${invoice.reference}</Ref></CdtrRefInf>\n`;
  }
  
  xml += `          </Strd>\n`;
  xml += `        </RmtInf>\n`;
  xml += `        <AddtlNtryInf>Payment from ${payment.sender}</AddtlNtryInf>\n`;
  xml += `      </Ntry>\n`;
  xml += `    </Ntfctn>\n`;
  xml += `  </BkToCstmrDbtCdtNtfctn>\n`;
  xml += `</Document>`;
  
  return xml;
}

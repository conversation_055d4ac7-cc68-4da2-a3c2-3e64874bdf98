/**
 * NetworkSimulator class to simulate network delays and callbacks
 * for payment processing operations.
 */
export class NetworkSimulator {
  private pendingOperations: Map<string, NodeJS.Timeout>;
  
  constructor() {
    this.pendingOperations = new Map();
  }
  
  /**
   * Simulates sending a payment with a network delay
   * @param paymentId The ID of the payment being sent
   * @param callback Function to call after the delay
   * @param delay Delay in milliseconds (defaults to 5000ms/5s)
   */
  async sendPayment(paymentId: number, callback: () => Promise<void>, delay = 5000): Promise<void> {
    const operationId = `send_payment_${paymentId}_${Date.now()}`;
    
    // Return a promise that resolves after the delay
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(async () => {
        try {
          await callback();
          resolve();
        } catch (error) {
          reject(error);
        } finally {
          this.pendingOperations.delete(operationId);
        }
      }, delay);
      
      this.pendingOperations.set(operationId, timeoutId);
    });
  }
  
  /**
   * Simulates receiving a payment with a network delay
   * @param paymentId The ID of the payment being received
   * @param callback Function to call after the delay
   * @param delay Delay in milliseconds (defaults to 3000ms/3s)
   */
  async receivePayment(paymentId: number, callback: () => Promise<void>, delay = 3000): Promise<void> {
    const operationId = `receive_payment_${paymentId}_${Date.now()}`;
    
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(async () => {
        try {
          await callback();
          resolve();
        } catch (error) {
          reject(error);
        } finally {
          this.pendingOperations.delete(operationId);
        }
      }, delay);
      
      this.pendingOperations.set(operationId, timeoutId);
    });
  }
  
  /**
   * Cancels a pending operation
   * @param operationId The ID of the operation to cancel
   */
  cancelOperation(operationId: string): boolean {
    const timeoutId = this.pendingOperations.get(operationId);
    if (timeoutId) {
      clearTimeout(timeoutId);
      this.pendingOperations.delete(operationId);
      return true;
    }
    return false;
  }
  
  /**
   * Lists all pending operations
   */
  getPendingOperations(): string[] {
    return Array.from(this.pendingOperations.keys());
  }
}

// Create a singleton instance
export const networkSimulator = new NetworkSimulator();

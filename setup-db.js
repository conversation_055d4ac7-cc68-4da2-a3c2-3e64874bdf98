import pkg from 'pg';
const { Pool } = pkg;
import { drizzle } from 'drizzle-orm/node-postgres';
import { migrate } from 'drizzle-orm/node-postgres/migrator';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Handle ESM compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const main = async () => {
  console.log('Setting up database...');
  
  if (!process.env.DATABASE_URL) {
    console.error('ERROR: DATABASE_URL environment variable is not set.');
    console.log('Please create a .env file in the project root with the following content:');
    console.log('DATABASE_URL=postgres://postgres:postgres@localhost:5432/proofpay');
    console.log('VITE_BLS_PRIVATE_KEY=0x0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef');
    console.log('SIMULATOR_ENABLED=false');
    process.exit(1);
  }

  try {
    // Create the database pool
    const pool = new Pool({
      connectionString: process.env.DATABASE_URL,
    });

    // Test the connection
    await pool.query('SELECT NOW()');
    console.log('Connected to PostgreSQL server.');

    // Create the drizzle instance
    const db = drizzle(pool);

    // Create the migrations directory if it doesn't exist
    const migrationsDir = path.join(__dirname, 'migrations');
    if (!fs.existsSync(migrationsDir)) {
      fs.mkdirSync(migrationsDir);
      console.log('Created migrations directory.');
    }

    // Check if there are any migration files
    const migrationFiles = fs.readdirSync(migrationsDir);
    if (migrationFiles.length === 0) {
      console.log('No migration files found. Creating initial migration file...');
      
      // Create an empty migration file
      const timestamp = new Date().getTime();
      const migrationFileName = `${timestamp}_initial_migration.sql`;
      const migrationFilePath = path.join(migrationsDir, migrationFileName);
      
      // Import the schema to generate SQL
      const { payments, remittances, invoices, receivedPayments, users } = await import('./shared/schema.js');
      
      // Generate a basic SQL migration file
      const migrationSQL = `
-- Initial migration
CREATE TABLE IF NOT EXISTS users (
  id SERIAL PRIMARY KEY,
  username TEXT NOT NULL UNIQUE,
  password TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS payments (
  id SERIAL PRIMARY KEY,
  reference TEXT NOT NULL,
  amount DOUBLE PRECISION NOT NULL,
  sender TEXT NOT NULL,
  recipient TEXT NOT NULL,
  recipient_address TEXT,
  recipient_account TEXT,
  status TEXT NOT NULL DEFAULT 'Not Approved',
  file_type TEXT NOT NULL,
  approved BOOLEAN NOT NULL DEFAULT false,
  approved_at TIMESTAMP,
  sent_at TIMESTAMP,
  file_content TEXT NOT NULL,
  remittance_generated BOOLEAN NOT NULL DEFAULT false,
  remittance_generated_at TIMESTAMP,
  remittance_id INTEGER,
  signature TEXT,
  message TEXT,
  receipt_imported BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS remittances (
  id SERIAL PRIMARY KEY,
  payment_id INTEGER NOT NULL,
  status TEXT NOT NULL DEFAULT 'Generated',
  format TEXT NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  file_path TEXT NOT NULL,
  amount DOUBLE PRECISION NOT NULL,
  sender TEXT NOT NULL,
  recipient TEXT NOT NULL,
  reference TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS invoices (
  id SERIAL PRIMARY KEY,
  customer TEXT NOT NULL,
  amount DOUBLE PRECISION NOT NULL,
  reference TEXT NOT NULL,
  description TEXT NOT NULL,
  due_date TIMESTAMP NOT NULL,
  status TEXT NOT NULL DEFAULT 'Open',
  file_type TEXT,
  file_content TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  payment_id INTEGER,
  remittance_generated BOOLEAN NOT NULL DEFAULT false,
  remittance_generated_at TIMESTAMP,
  remittance_id INTEGER
);

CREATE TABLE IF NOT EXISTS received_payments (
  id SERIAL PRIMARY KEY,
  sender TEXT NOT NULL,
  amount DOUBLE PRECISION NOT NULL,
  reference TEXT NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  invoice_id INTEGER,
  status TEXT NOT NULL DEFAULT 'Received',
  recipient TEXT NOT NULL DEFAULT 'Your Company',
  remittance_generated BOOLEAN NOT NULL DEFAULT false,
  remittance_generated_at TIMESTAMP,
  remittance_id INTEGER
);
`;
      
      fs.writeFileSync(migrationFilePath, migrationSQL);
      console.log(`Created initial migration file: ${migrationFileName}`);
    }

    // Create files directory if it doesn't exist
    const filesDir = path.join(__dirname, 'files');
    if (!fs.existsSync(filesDir)) {
      fs.mkdirSync(filesDir);
      console.log('Created files directory for storing uploads and generated files.');
    }

    console.log('Database setup complete!');
    console.log('\nYou can now run the application with:');
    console.log('npm run dev:local');
    
    // Close the connection
    await pool.end();
    process.exit(0);
  } catch (error) {
    console.error('Error setting up database:', error);
    process.exit(1);
  }
};

main(); 
/**
 * Database Schema Definitions
 * 
 * This file defines the database schema for the entire application using Drizzle ORM.
 * It includes table definitions, relationships, and Zod validation schemas for data insertion.
 * These schemas are shared between the client and server to ensure type safety and data integrity.
 */

import { pgTable, text, serial, integer, boolean, timestamp, doublePrecision } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

/**
 * User Model
 * 
 * Basic user authentication model for the application.
 * Currently only used for very basic authentication purposes.
 */
export const users = pgTable("users", {
  id: serial("id").primaryKey(),              // Unique user identifier
  username: text("username").notNull().unique(), // Unique username for login
  password: text("password").notNull(),       // Password (should be stored as a hash in production)
});

// Schema for user creation with Zod validation
export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

// Type definitions for TypeScript type safety
export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

/**
 * Payment Model
 * 
 * Represents outgoing payments in the Accounts Payable workflow.
 * Tracks the full lifecycle of a payment from import through approval, 
 * sending via blockchain, and reconciliation.
 */
export const payments = pgTable("payments", {
  id: serial("id").primaryKey(),                      // Unique payment identifier
  reference: text("reference").notNull(),             // Reference number (must be unique)
  amount: doublePrecision("amount").notNull(),        // Payment amount
  sender: text("sender").notNull(),                   // Company sending the payment
  recipient: text("recipient").notNull(),             // Payment recipient
  recipient_address: text("recipient_address"),       // Optional shipping/billing address
  recipient_account: text("recipient_account"),       // Optional account number
  status: text("status").notNull().default("Not Approved"), // Payment status (Not Approved, Approved, Paid, Reconciled)
  file_type: text("file_type").notNull(),             // Format of import file (PEXR2002, MT103, ISO20022)
  approved: boolean("approved").notNull().default(false), // Flag for approval status
  approved_at: timestamp("approved_at"),              // When payment was approved
  sent_at: timestamp("sent_at"),                      // When payment was sent to blockchain
  file_content: text("file_content").notNull(),       // Original imported file content
  remittance_generated: boolean("remittance_generated").notNull().default(false), // Whether reconciliation file was generated
  remittance_generated_at: timestamp("remittance_generated_at"), // When reconciliation was generated
  remittance_id: integer("remittance_id"),            // ID of linked reconciliation record
  signature: text("signature"),                       // BLS signature for the payment
  message: text("message"),                           // Message hash that was signed
  receipt_imported: boolean("receipt_imported").notNull().default(false), // Flag for tracking manual receipt imports; used to control when payments appear in reconciliation column
  created_at: timestamp("created_at").defaultNow().notNull(), // Creation timestamp
});

/**
 * Schema for creating new payments
 * Excludes system-generated fields that shouldn't be provided during creation
 */
export const insertPaymentSchema = createInsertSchema(payments).omit({
  id: true,                 // Auto-generated primary key
  status: true,             // System-managed status
  approved: true,           // System-managed approval flag
  sent_at: true,            // System-managed timestamp
  remittance_generated: true, // System-managed flag
  remittance_id: true,      // System-managed relation
  created_at: true,         // Auto-generated timestamp
  receipt_imported: true,   // System-managed flag for tracking receipt imports
});

// Type definitions for TypeScript type safety
export type InsertPayment = z.infer<typeof insertPaymentSchema>;
export type Payment = typeof payments.$inferSelect;

/**
 * Reconciliation/Remittance Model
 * 
 * Represents reconciliation documents generated for payments or invoices.
 * These files provide standardized financial records in various formats.
 * Previously called "Remittance" in code but displayed as "Reconciliation" to users.
 */
export const remittances = pgTable("remittances", {
  id: serial("id").primaryKey(),                      // Unique reconciliation identifier
  payment_id: integer("payment_id").notNull(),        // ID of related payment or invoice
  status: text("status").notNull().default("Generated"), // Reconciliation status
  format: text("format").notNull(),                   // File format (MT940, BAI2, ISO20022)
  created_at: timestamp("created_at").defaultNow().notNull(), // Creation timestamp
  file_path: text("file_path").notNull(),             // Location of generated file
  amount: doublePrecision("amount").notNull(),        // Transaction amount
  sender: text("sender").notNull(),                   // Payment sender 
  recipient: text("recipient").notNull(),             // Payment recipient
  reference: text("reference").notNull(),             // Original payment reference
});

/**
 * Schema for creating new reconciliation records
 * Excludes system-generated fields that shouldn't be provided during creation
 */
export const insertRemittanceSchema = createInsertSchema(remittances).omit({
  id: true,                 // Auto-generated primary key
  status: true,             // System-managed status
  created_at: true,         // Auto-generated timestamp
});

// Type definitions for TypeScript type safety
export type InsertRemittance = z.infer<typeof insertRemittanceSchema>;
export type Remittance = typeof remittances.$inferSelect;

/**
 * Invoice Model
 * 
 * Represents invoices in the Accounts Receivable workflow.
 * Tracks lifecycle from creation through payment receipt and reconciliation.
 * Invoices can be created manually or imported from standardized file formats.
 */
export const invoices = pgTable("invoices", {
  id: serial("id").primaryKey(),                      // Unique invoice identifier
  customer: text("customer").notNull(),               // Customer/client name
  amount: doublePrecision("amount").notNull(),        // Invoice amount
  reference: text("reference").notNull(),             // Invoice reference number
  description: text("description").notNull(),         // Invoice description/details
  due_date: timestamp("due_date").notNull(),          // Invoice payment due date
  status: text("status").notNull().default("Open"),   // Invoice status (Open, Overdue, Paid, Reconciled)
  file_type: text("file_type"),                       // Format of import file (if imported)
  file_content: text("file_content"),                 // Original imported file content (if imported)
  created_at: timestamp("created_at").defaultNow().notNull(), // Creation timestamp
  payment_id: integer("payment_id"),                  // ID of linked received payment (if linked)
  remittance_generated: boolean("remittance_generated").notNull().default(false), // Whether reconciliation was generated
  remittance_generated_at: timestamp("remittance_generated_at"), // When reconciliation was generated
  remittance_id: integer("remittance_id"),            // ID of linked reconciliation record
});

/**
 * Schema for creating new invoices
 * Excludes system-generated fields that shouldn't be provided during creation
 */
export const insertInvoiceSchema = createInsertSchema(invoices).omit({
  id: true,                 // Auto-generated primary key
  status: true,             // System-managed status
  created_at: true,         // Auto-generated timestamp
  payment_id: true,         // System-managed relation
  remittance_id: true,      // System-managed relation
});

// Type definitions for TypeScript type safety
export type InsertInvoice = z.infer<typeof insertInvoiceSchema>;
export type Invoice = typeof invoices.$inferSelect;

/**
 * ReceivedPayment Model
 * 
 * Represents incoming payments in the Accounts Receivable workflow.
 * Payments can be linked to specific invoices for reconciliation purposes.
 * Status tracking includes Unlinked, Linked, and Reconciled states.
 */
export const receivedPayments = pgTable("received_payments", {
  id: serial("id").primaryKey(),                      // Unique received payment identifier
  sender: text("sender").notNull(),                   // Payment sender name
  amount: doublePrecision("amount").notNull(),        // Payment amount
  reference: text("reference").notNull(),             // Payment reference number
  created_at: timestamp("created_at").defaultNow().notNull(), // Receipt timestamp
  invoice_id: integer("invoice_id"),                  // ID of linked invoice (if linked)
  status: text("status").notNull().default("Received"), // Payment status (Unlinked, Linked, Reconciled)
  recipient: text("recipient").notNull().default("Your Company"), // Payment recipient (your company)
  remittance_generated: boolean("remittance_generated").notNull().default(false), // Whether reconciliation was generated
  remittance_generated_at: timestamp("remittance_generated_at"), // When reconciliation was generated
  remittance_id: integer("remittance_id"),            // ID of linked reconciliation record
});

/**
 * Schema for creating new received payments
 * Excludes system-generated fields that shouldn't be provided during creation
 */
export const insertReceivedPaymentSchema = createInsertSchema(receivedPayments).omit({
  id: true,                 // Auto-generated primary key
  created_at: true,         // Auto-generated timestamp
  status: true,             // System-managed status
  remittance_generated: true, // System-managed flag
  remittance_id: true,      // System-managed relation
});

// Type definitions for TypeScript type safety
export type InsertReceivedPayment = z.infer<typeof insertReceivedPaymentSchema>;
export type ReceivedPayment = typeof receivedPayments.$inferSelect;

/**
 * File Format Definitions
 * 
 * Defines the supported file formats for different types of financial documents.
 * These are used for validation during file imports and for format selection in exports.
 */
export const fileFormats = {
  payment: ["PEXR2002", "MT103", "ISO20022"],     // Supported payment file formats
  invoice: ["EDI X12", "ISO20022"],               // Supported invoice file formats
  remittance: ["MT940", "BAI2", "ISO20022"],      // Supported reconciliation file formats
} as const;

// Type definitions for TypeScript type safety with file formats
export type PaymentFileFormat = typeof fileFormats.payment[number];
export type InvoiceFileFormat = typeof fileFormats.invoice[number];
export type RemittanceFileFormat = typeof fileFormats.remittance[number];

#!/bin/bash

# ProofPay Startup Script
# -----------------------
# This script handles:
# 1. Killing any existing processes
# 2. Reinitializing the database
# 3. Starting the application

# Text formatting
BOLD="\033[1m"
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
RESET="\033[0m"

echo -e "${BOLD}ProofPay Startup Script${RESET}"
echo "========================="
echo ""

# Function to display steps with formatting
function step() {
  echo -e "${BOLD}${GREEN}$1${RESET}"
}

# Function to display warnings with formatting
function warn() {
  echo -e "${BOLD}${YELLOW}$1${RESET}"
}

# Function to display errors with formatting
function error() {
  echo -e "${BOLD}${RED}$1${RESET}"
  exit 1
}

# Step 1: Kill any existing Node.js processes
step "Step 1: Killing any existing Node.js processes..."

# Find and kill any Node.js processes running the app
NODE_PIDS=$(pgrep -f "node.*proofpay")
if [ -n "$NODE_PIDS" ]; then
  echo "Found Node.js processes: $NODE_PIDS"
  kill $NODE_PIDS
  echo "Processes killed."
else
  echo "No existing Node.js processes found."
fi

# Also try to find any processes on ports 5001 and 5173
PORT_5001_PID=$(lsof -i :5001 -t 2>/dev/null)
if [ -n "$PORT_5001_PID" ]; then
  echo "Found process on port 5001: $PORT_5001_PID"
  kill $PORT_5001_PID
  echo "Process killed."
fi

PORT_5173_PID=$(lsof -i :5173 -t 2>/dev/null)
if [ -n "$PORT_5173_PID" ]; then
  echo "Found process on port 5173: $PORT_5173_PID"
  kill $PORT_5173_PID
  echo "Process killed."
fi

# Sleep briefly to allow processes to terminate
sleep 2

# Step 2: Ensure PostgreSQL is running
step "Step 2: Checking PostgreSQL..."
if ! pg_isready >/dev/null 2>&1; then
  warn "PostgreSQL is not running. Attempting to start it..."
  
  # Try to start PostgreSQL (this varies by system)
  if [ -f /usr/local/bin/brew ]; then
    # macOS with Homebrew
    brew services start postgresql
  elif [ -f /etc/init.d/postgresql ]; then
    # Linux with systemd
    sudo systemctl start postgresql
  else
    error "Could not start PostgreSQL. Please start it manually and run this script again."
  fi
  
  # Wait for PostgreSQL to start
  echo "Waiting for PostgreSQL to start..."
  sleep 5
  
  if ! pg_isready >/dev/null 2>&1; then
    error "PostgreSQL failed to start. Please start it manually and run this script again."
  fi
fi
echo "PostgreSQL is running."

# Step 3: Check for .env file
step "Step 3: Checking environment configuration..."
if [ ! -f .env ]; then
  warn ".env file not found. Creating it..."
  echo "DATABASE_URL=postgres://$(whoami)@localhost:5432/proofpay" > .env
  echo "VITE_BLS_PRIVATE_KEY=0x0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef" >> .env
  echo "SIMULATOR_ENABLED=false" >> .env
  echo ".env file created."
fi
echo "Environment configuration is ready."

# Step 4: Make sure dependencies are installed
step "Step 4: Checking dependencies..."
if [ ! -d "node_modules" ]; then
  warn "Dependencies not found. Installing..."
  npm install
  if [ $? -ne 0 ]; then
    error "Failed to install dependencies. Please run 'npm install' manually."
  fi
fi
echo "Dependencies are installed."

# Step 5: Reinitialize the database
step "Step 5: Reinitializing database..."
echo "Running database initialization..."
npm run setup
if [ $? -ne 0 ]; then
  error "Failed to initialize database. Check the error message above."
fi
echo "Database initialized successfully."

# Create files directory if it doesn't exist
if [ ! -d "files" ]; then
  echo "Creating files directory..."
  mkdir -p files
fi

# Step 6: Start the application
step "Step 6: Starting the application..."
echo "Starting ProofPay with npm run dev:local..."
echo "The application will be available at http://localhost:5173"
echo "Press Ctrl+C to stop the application."
echo ""

# Start the application
npm run dev:local

# This will never be reached unless the app crashes
error "The application has stopped unexpectedly." 
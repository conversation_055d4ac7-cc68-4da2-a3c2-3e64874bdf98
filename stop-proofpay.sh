#!/bin/bash

# ProofPay Shutdown Script
# ------------------------
# This script handles graceful shutdown of all ProofPay processes

# Text formatting
BOLD="\033[1m"
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
RESET="\033[0m"

echo -e "${BOLD}ProofPay Shutdown Script${RESET}"
echo "=========================="
echo ""

# Function to display messages with formatting
function info() {
  echo -e "${BOLD}${BLUE}$1${RESET}"
}

function success() {
  echo -e "${BOLD}${GREEN}$1${RESET}"
}

function warn() {
  echo -e "${BOLD}${YELLOW}$1${RESET}"
}

function error() {
  echo -e "${BOLD}${RED}$1${RESET}"
}

# Find and gracefully terminate Node.js processes related to ProofPay
info "Looking for Node.js processes related to ProofPay..."

# Find all Node.js processes related to the app 
NODE_PIDS=$(pgrep -f "node.*proofpay")

if [ -n "$NODE_PIDS" ]; then
  info "Found Node.js processes: $NODE_PIDS"
  
  # First try SIGTERM for graceful shutdown
  echo "Sending SIGTERM signal for graceful shutdown..."
  kill -15 $NODE_PIDS
  
  # Wait a bit to let the processes terminate gracefully
  echo "Waiting for processes to shut down gracefully..."
  sleep 5
  
  # Check if any processes are still running
  REMAINING_PIDS=$(pgrep -f "node.*proofpay")
  if [ -n "$REMAINING_PIDS" ]; then
    warn "Some processes are still running. Sending SIGKILL..."
    kill -9 $REMAINING_PIDS
    sleep 2
  fi
  
  # Final check
  if pgrep -f "node.*proofpay" > /dev/null; then
    error "Failed to kill all processes. Please check manually with 'ps aux | grep node'"
    exit 1
  else
    success "All Node.js processes have been terminated."
  fi
else
  info "No Node.js processes related to ProofPay were found."
fi

# Also explicitly check and kill processes on the application ports
info "Checking for processes on application ports..."

# Check port 5001 (API server)
PORT_5001_PID=$(lsof -i :5001 -t 2>/dev/null)
if [ -n "$PORT_5001_PID" ]; then
  info "Found process on port 5001 (API server): $PORT_5001_PID"
  echo "Sending SIGTERM signal..."
  kill -15 $PORT_5001_PID
  sleep 2
  
  # Check if it's still running and force kill if necessary
  if lsof -i :5001 -t > /dev/null 2>&1; then
    warn "Process on port 5001 is still running. Sending SIGKILL..."
    kill -9 $(lsof -i :5001 -t)
    sleep 1
  fi
  
  if ! lsof -i :5001 -t > /dev/null 2>&1; then
    success "Process on port 5001 has been terminated."
  else
    error "Failed to kill process on port 5001."
  fi
else
  info "No process found on port 5001."
fi

# Check port 5173 (Vite dev server)
PORT_5173_PID=$(lsof -i :5173 -t 2>/dev/null)
if [ -n "$PORT_5173_PID" ]; then
  info "Found process on port 5173 (Vite dev server): $PORT_5173_PID"
  echo "Sending SIGTERM signal..."
  kill -15 $PORT_5173_PID
  sleep 2
  
  # Check if it's still running and force kill if necessary
  if lsof -i :5173 -t > /dev/null 2>&1; then
    warn "Process on port 5173 is still running. Sending SIGKILL..."
    kill -9 $(lsof -i :5173 -t)
    sleep 1
  fi
  
  if ! lsof -i :5173 -t > /dev/null 2>&1; then
    success "Process on port 5173 has been terminated."
  else
    error "Failed to kill process on port 5173."
  fi
else
  info "No process found on port 5173."
fi

# Check for any background TSX watch processes
TSX_PIDS=$(pgrep -f "tsx.*--watch")
if [ -n "$TSX_PIDS" ]; then
  info "Found TSX watch processes: $TSX_PIDS"
  echo "Sending SIGTERM signal..."
  kill -15 $TSX_PIDS
  sleep 2
  
  # Check if any are still running and force kill if necessary
  REMAINING_TSX=$(pgrep -f "tsx.*--watch")
  if [ -n "$REMAINING_TSX" ]; then
    warn "Some TSX processes are still running. Sending SIGKILL..."
    kill -9 $REMAINING_TSX
    sleep 1
  fi
  
  if ! pgrep -f "tsx.*--watch" > /dev/null; then
    success "All TSX watch processes have been terminated."
  else
    error "Failed to kill all TSX watch processes."
  fi
else
  info "No TSX watch processes found."
fi

# Final check for any remaining concurrently processes
CONCURRENTLY_PIDS=$(pgrep -f "concurrently")
if [ -n "$CONCURRENTLY_PIDS" ]; then
  info "Found concurrently processes: $CONCURRENTLY_PIDS"
  echo "Sending SIGTERM signal..."
  kill -15 $CONCURRENTLY_PIDS
  sleep 2
  
  if ! pgrep -f "concurrently" > /dev/null; then
    success "All concurrently processes have been terminated."
  else
    warn "Sending SIGKILL to remaining concurrently processes..."
    kill -9 $(pgrep -f "concurrently")
  fi
else
  info "No concurrently processes found."
fi

# Summarize shutdown status
if pgrep -f "node.*proofpay" > /dev/null || \
   lsof -i :5001 -t > /dev/null 2>&1 || \
   lsof -i :5173 -t > /dev/null 2>&1 || \
   pgrep -f "tsx.*--watch" > /dev/null || \
   pgrep -f "concurrently" > /dev/null; then
  error "\nSome processes could not be terminated. Check running processes manually."
  exit 1
else
  success "\nProofPay has been successfully shut down."
  exit 0
fi 
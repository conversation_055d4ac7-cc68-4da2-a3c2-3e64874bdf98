# ProofPay Test Suite

This directory contains the comprehensive test suite for the ProofPay application, covering components, hooks, and utilities.

## Testing Technology Stack

- **Vitest**: Fast Vite-based test runner
- **React Testing Library**: DOM testing utilities
- **MSW (Mock Service Worker)**: API mocking
- **User Event**: User interaction simulation

## Directory Structure

```
tests/
├── components/         # Component tests
├── hooks/              # Custom hook tests
├── mocks/              # Mock data and handlers
├── utils/              # Test utilities
├── setup.ts            # Global test setup
└── README.md           # This file
```

## Running Tests

To run all tests:

```bash
npm test
```

To run specific tests:

```bash
npm test -- -t "PaymentCard"  # Run tests with "PaymentCard" in the name
```

To run tests in watch mode (for development):

```bash
npm test -- --watch
```

To run tests with UI:

```bash
npm test -- --ui
```

## Mock Data

Mock data for tests is stored in `mocks/mockData.ts`. This includes:

- Payments
- Invoices
- Received Payments
- Remittances

## API Mocking

API requests are mocked using MSW in `setup.ts`. The mock server intercepts all API requests during tests and returns predefined responses based on the mock data.

## Adding New Tests

### Component Tests

1. Create a new file in `components/` named `[ComponentName].test.tsx`
2. Import the component and necessary test utilities
3. Write tests using the `describe` and `it` functions

Example:

```tsx
import { describe, it, expect } from 'vitest';
import { render, screen } from '../utils/test-utils';
import YourComponent from '@/components/YourComponent';

describe('YourComponent', () => {
  it('renders correctly', () => {
    render(<YourComponent />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });
});
```

### Hook Tests

1. Create a new file in `hooks/` named `use[HookName].test.tsx`
2. Use `renderHook` to test the hook behavior

Example:

```tsx
import { renderHook, act } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import useYourHook from '@/hooks/useYourHook';
import { createWrapper } from '../utils/test-utils';

describe('useYourHook', () => {
  it('returns the expected values', () => {
    const { result } = renderHook(() => useYourHook(), {
      wrapper: createWrapper(),
    });
    
    expect(result.current.someValue).toBe(expectedValue);
  });
});
```

## Best Practices

1. **Test behavior, not implementation**: Focus on what the user sees and interacts with.
2. **Use data-testid sparingly**: Prefer using accessible roles, labels, and text content.
3. **Mock dependencies**: Use vi.mock() to mock dependencies like API calls.
4. **Test edge cases**: Include tests for loading states, errors, and edge cases.
5. **Keep tests independent**: Each test should be able to run independently.

## Common Testing Patterns

### Testing Component Rendering

```tsx
it('renders the component correctly', () => {
  render(<Component prop="value" />);
  expect(screen.getByText('Expected Text')).toBeInTheDocument();
});
```

### Testing User Interactions

```tsx
it('responds to user interaction', async () => {
  const handleClick = vi.fn();
  const { user } = render(<Button onClick={handleClick}>Click Me</Button>);
  
  await user.click(screen.getByRole('button', { name: /click me/i }));
  expect(handleClick).toHaveBeenCalledTimes(1);
});
```

### Testing Async Operations

```tsx
it('loads data asynchronously', async () => {
  render(<DataComponent />);
  
  // Test loading state
  expect(screen.getByText('Loading...')).toBeInTheDocument();
  
  // Wait for data to be loaded
  await screen.findByText('Data Loaded');
  
  // Test loaded state
  expect(screen.getByText('Data Loaded')).toBeInTheDocument();
  expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
});
```
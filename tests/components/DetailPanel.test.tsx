import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '../utils/test-utils';
import DetailPanel from '@/components/DetailPanel';
import { mockPayments } from '../mocks/mockData';

describe('DetailPanel', () => {
  it('renders with correct title and content', () => {
    const payment = mockPayments[0];
    const onCloseMock = vi.fn();
    
    render(
      <DetailPanel 
        title="Payment Details"
        isOpen={true}
        onClose={onCloseMock}
      >
        <div data-testid="test-content">Test content</div>
      </DetailPanel>
    );
    
    expect(screen.getByText('Payment Details')).toBeInTheDocument();
    expect(screen.getByTestId('test-content')).toBeInTheDocument();
  });
  
  it('calls onClose when close button is clicked', async () => {
    const onCloseMock = vi.fn();
    
    const { user } = render(
      <DetailPanel 
        title="Payment Details"
        isOpen={true}
        onClose={onCloseMock}
      >
        <div>Test content</div>
      </DetailPanel>
    );
    
    const closeButton = screen.getByRole('button', { name: /close/i });
    await user.click(closeButton);
    
    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });
  
  it('renders children content correctly', () => {
    const onCloseMock = vi.fn();
    
    render(
      <DetailPanel 
        title="Payment Details"
        isOpen={true}
        onClose={onCloseMock}
      >
        <div data-testid="custom-content">
          <h3>Custom Title</h3>
          <p>Custom description</p>
        </div>
      </DetailPanel>
    );
    
    expect(screen.getByTestId('custom-content')).toBeInTheDocument();
    expect(screen.getByText('Custom Title')).toBeInTheDocument();
    expect(screen.getByText('Custom description')).toBeInTheDocument();
  });
  
  it('applies slide-in animation class when open', () => {
    const onCloseMock = vi.fn();
    
    render(
      <DetailPanel 
        title="Payment Details"
        isOpen={true}
        onClose={onCloseMock}
      >
        <div>Test content</div>
      </DetailPanel>
    );
    
    const panel = screen.getByTestId('detail-panel');
    expect(panel).toHaveClass('slide-in-left');
    expect(panel).not.toHaveClass('slide-out-left');
  });
  
  it('applies slide-out animation class when closed', () => {
    const onCloseMock = vi.fn();
    
    render(
      <DetailPanel 
        title="Payment Details"
        isOpen={false}
        onClose={onCloseMock}
      >
        <div>Test content</div>
      </DetailPanel>
    );
    
    const panel = screen.getByTestId('detail-panel');
    expect(panel).toHaveClass('slide-out-left');
    expect(panel).not.toHaveClass('slide-in-left');
  });
});
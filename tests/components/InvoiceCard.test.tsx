import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '../utils/test-utils';
import InvoiceCard from '@/components/InvoiceCard';
import { mockInvoices } from '../mocks/mockData';
import { format } from 'date-fns';

describe('InvoiceCard', () => {
  it('renders invoice information correctly', () => {
    const invoice = mockInvoices[0];
    const onClickMock = vi.fn();
    
    render(
      <InvoiceCard 
        invoice={invoice}
        onClick={onClickMock}
        showDetailPanel={false}
      />
    );
    
    // Check if essential invoice information is displayed
    expect(screen.getByText(invoice.reference)).toBeInTheDocument();
    expect(screen.getByText(invoice.customer)).toBeInTheDocument();
    expect(screen.getByText(`$${invoice.amount.toLocaleString()}`)).toBeInTheDocument();
    
    // Check status badge
    expect(screen.getByText('Open')).toBeInTheDocument();
    
    // Check for formatted due date
    const formattedDueDate = format(new Date(invoice.due_date), 'MMM d, yyyy');
    expect(screen.getByText(`Due: ${formattedDueDate}`)).toBeInTheDocument();
  });
  
  it('calls onClick handler when clicked', async () => {
    const invoice = mockInvoices[0];
    const onClickMock = vi.fn();
    
    const { user } = render(
      <InvoiceCard 
        invoice={invoice}
        onClick={onClickMock}
        showDetailPanel={false}
      />
    );
    
    const card = screen.getByTestId(`invoice-card-${invoice.id}`);
    await user.click(card);
    
    expect(onClickMock).toHaveBeenCalledTimes(1);
    expect(onClickMock).toHaveBeenCalledWith(invoice);
  });
  
  it('applies different styling for Overdue status', () => {
    const invoice = mockInvoices[1]; // Overdue invoice
    const onClickMock = vi.fn();
    
    render(
      <InvoiceCard 
        invoice={invoice}
        onClick={onClickMock}
        showDetailPanel={false}
      />
    );
    
    const statusBadge = screen.getByText('Overdue');
    expect(statusBadge).toHaveClass('bg-red-100');
  });
  
  it('applies different styling for Paid status', () => {
    const invoice = mockInvoices[2]; // Paid invoice
    const onClickMock = vi.fn();
    
    render(
      <InvoiceCard 
        invoice={invoice}
        onClick={onClickMock}
        showDetailPanel={false}
      />
    );
    
    const statusBadge = screen.getByText('Paid');
    expect(statusBadge).toHaveClass('bg-blue-100');
  });
  
  it('applies different styling for Reconciled status', () => {
    const invoice = mockInvoices[3]; // Reconciled invoice
    const onClickMock = vi.fn();
    
    render(
      <InvoiceCard 
        invoice={invoice}
        onClick={onClickMock}
        showDetailPanel={false}
      />
    );
    
    const statusBadge = screen.getByText('Reconciled');
    expect(statusBadge).toHaveClass('status-reconciled');
  });
  
  it('applies active class when showDetailPanel is true', () => {
    const invoice = mockInvoices[0];
    const onClickMock = vi.fn();
    
    render(
      <InvoiceCard 
        invoice={invoice}
        onClick={onClickMock}
        showDetailPanel={true}
      />
    );
    
    const card = screen.getByTestId(`invoice-card-${invoice.id}`);
    expect(card).toHaveClass('border-primary');
  });
});
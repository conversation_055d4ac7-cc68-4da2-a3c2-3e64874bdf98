import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '../utils/test-utils';
import KanbanBoard from '@/components/KanbanBoard';

describe('KanbanBoard', () => {
  it('renders columns with correct titles', () => {
    const columns = [
      { title: 'Column 1', id: 'col1' },
      { title: 'Column 2', id: 'col2' },
      { title: 'Column 3', id: 'col3' }
    ];
    
    render(
      <KanbanBoard columns={columns}>
        {(column) => (
          <div key={column.id}>
            {/* Column content */}
            <div data-testid={`column-content-${column.id}`}>
              Content for {column.title}
            </div>
          </div>
        )}
      </KanbanBoard>
    );
    
    // Check column titles
    expect(screen.getByText('Column 1')).toBeInTheDocument();
    expect(screen.getByText('Column 2')).toBeInTheDocument();
    expect(screen.getByText('Column 3')).toBeInTheDocument();
    
    // Check column content
    expect(screen.getByTestId('column-content-col1')).toBeInTheDocument();
    expect(screen.getByTestId('column-content-col2')).toBeInTheDocument();
    expect(screen.getByTestId('column-content-col3')).toBeInTheDocument();
  });
  
  it('renders children correctly for each column', () => {
    const columns = [
      { title: 'Column 1', id: 'col1' },
      { title: 'Column 2', id: 'col2' }
    ];
    
    render(
      <KanbanBoard columns={columns}>
        {(column) => (
          <div key={column.id}>
            <div data-testid={`custom-content-${column.id}`}>
              {column.id === 'col1' ? 'First column content' : 'Second column content'}
            </div>
          </div>
        )}
      </KanbanBoard>
    );
    
    expect(screen.getByText('First column content')).toBeInTheDocument();
    expect(screen.getByText('Second column content')).toBeInTheDocument();
  });
  
  it('applies column width style correctly', () => {
    const columns = [
      { title: 'Column 1', id: 'col1' },
      { title: 'Column 2', id: 'col2' }
    ];
    
    const columnWidth = 400; // px
    
    render(
      <KanbanBoard columns={columns} columnWidth={columnWidth}>
        {(column) => (
          <div key={column.id}>Column content</div>
        )}
      </KanbanBoard>
    );
    
    const columnElements = screen.getAllByTestId(/kanban-column/);
    
    // Check that each column has the correct width style
    columnElements.forEach(column => {
      expect(column).toHaveStyle(`width: ${columnWidth}px`);
    });
  });
  
  it('renders with default column width when not specified', () => {
    const columns = [
      { title: 'Column 1', id: 'col1' },
      { title: 'Column 2', id: 'col2' }
    ];
    
    render(
      <KanbanBoard columns={columns}>
        {(column) => (
          <div key={column.id}>Column content</div>
        )}
      </KanbanBoard>
    );
    
    const columnElements = screen.getAllByTestId(/kanban-column/);
    
    // Check that each column has the default width style (340px)
    columnElements.forEach(column => {
      expect(column).toHaveStyle('width: 340px');
    });
  });
  
  it('renders a scrollable container for the columns', () => {
    const columns = [
      { title: 'Column 1', id: 'col1' },
      { title: 'Column 2', id: 'col2' }
    ];
    
    render(
      <KanbanBoard columns={columns}>
        {(column) => (
          <div key={column.id}>Column content</div>
        )}
      </KanbanBoard>
    );
    
    const container = screen.getByTestId('kanban-container');
    expect(container).toHaveClass('kanban-container');
    expect(container).toHaveClass('custom-scrollbar');
  });
});
import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '../utils/test-utils';
import PaymentCard from '@/components/PaymentCard';
import { mockPayments } from '../mocks/mockData';

describe('PaymentCard', () => {
  it('renders payment information correctly', () => {
    const payment = mockPayments[0];
    const onClickMock = vi.fn();
    
    render(
      <PaymentCard 
        payment={payment}
        onClick={onClickMock}
        showDetailPanel={false}
      />
    );
    
    // Check if essential payment information is displayed
    expect(screen.getByText(payment.reference)).toBeInTheDocument();
    expect(screen.getByText(payment.recipient)).toBeInTheDocument();
    expect(screen.getByText(`$${payment.amount.toLocaleString()}`)).toBeInTheDocument();
    
    // Check status badge
    expect(screen.getByText('Not Approved')).toBeInTheDocument();
  });
  
  it('calls onClick handler when clicked', async () => {
    const payment = mockPayments[0];
    const onClickMock = vi.fn();
    
    const { user } = render(
      <PaymentCard 
        payment={payment}
        onClick={onClickMock}
        showDetailPanel={false}
      />
    );
    
    const card = screen.getByTestId(`payment-card-${payment.id}`);
    await user.click(card);
    
    expect(onClickMock).toHaveBeenCalledTimes(1);
    expect(onClickMock).toHaveBeenCalledWith(payment);
  });
  
  it('applies different styling for Approved status', () => {
    const payment = mockPayments[1]; // Approved payment
    const onClickMock = vi.fn();
    
    render(
      <PaymentCard 
        payment={payment}
        onClick={onClickMock}
        showDetailPanel={false}
      />
    );
    
    const statusBadge = screen.getByText('Approved');
    expect(statusBadge).toHaveClass('bg-green-100');
  });
  
  it('applies different styling for Paid status', () => {
    const payment = mockPayments[2]; // Paid payment
    const onClickMock = vi.fn();
    
    render(
      <PaymentCard 
        payment={payment}
        onClick={onClickMock}
        showDetailPanel={false}
      />
    );
    
    const statusBadge = screen.getByText('Paid');
    expect(statusBadge).toHaveClass('status-paid');
  });
  
  it('applies different styling for Reconciled status', () => {
    const payment = mockPayments[3]; // Reconciled payment
    const onClickMock = vi.fn();
    
    render(
      <PaymentCard 
        payment={payment}
        onClick={onClickMock}
        showDetailPanel={false}
      />
    );
    
    const statusBadge = screen.getByText('Reconciled');
    expect(statusBadge).toHaveClass('status-reconciled');
  });
  
  it('applies active class when showDetailPanel is true', () => {
    const payment = mockPayments[0];
    const onClickMock = vi.fn();
    
    render(
      <PaymentCard 
        payment={payment}
        onClick={onClickMock}
        showDetailPanel={true}
      />
    );
    
    const card = screen.getByTestId(`payment-card-${payment.id}`);
    expect(card).toHaveClass('border-primary');
  });
});
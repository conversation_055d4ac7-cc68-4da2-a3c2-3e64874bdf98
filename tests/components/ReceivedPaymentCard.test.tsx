import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '../utils/test-utils';
import ReceivedPaymentCard from '@/components/ReceivedPaymentCard';
import { mockReceivedPayments } from '../mocks/mockData';

describe('ReceivedPaymentCard', () => {
  it('renders received payment information correctly', () => {
    const payment = mockReceivedPayments[0];
    const onClickMock = vi.fn();
    
    render(
      <ReceivedPaymentCard 
        payment={payment}
        onClick={onClickMock}
        showDetailPanel={false}
      />
    );
    
    // Check if essential payment information is displayed
    expect(screen.getByText(payment.reference)).toBeInTheDocument();
    expect(screen.getByText(payment.sender)).toBeInTheDocument();
    expect(screen.getByText(`$${payment.amount.toLocaleString()}`)).toBeInTheDocument();
    
    // Check status badge
    expect(screen.getByText('Unlinked')).toBeInTheDocument();
  });
  
  it('calls onClick handler when clicked', async () => {
    const payment = mockReceivedPayments[0];
    const onClickMock = vi.fn();
    
    const { user } = render(
      <ReceivedPaymentCard 
        payment={payment}
        onClick={onClickMock}
        showDetailPanel={false}
      />
    );
    
    const card = screen.getByTestId(`received-payment-card-${payment.id}`);
    await user.click(card);
    
    expect(onClickMock).toHaveBeenCalledTimes(1);
    expect(onClickMock).toHaveBeenCalledWith(payment);
  });
  
  it('applies different styling for Linked status', () => {
    const payment = mockReceivedPayments[1]; // Linked payment
    const onClickMock = vi.fn();
    
    render(
      <ReceivedPaymentCard 
        payment={payment}
        onClick={onClickMock}
        showDetailPanel={false}
      />
    );
    
    const statusBadge = screen.getByText('Linked');
    expect(statusBadge).toHaveClass('status-linked');
  });
  
  it('applies different styling for Remitted status', () => {
    const payment = mockReceivedPayments[2]; // Remitted payment
    const onClickMock = vi.fn();
    
    render(
      <ReceivedPaymentCard 
        payment={payment}
        onClick={onClickMock}
        showDetailPanel={false}
      />
    );
    
    const statusBadge = screen.getByText('Remitted');
    expect(statusBadge).toHaveClass('status-reconciled');
  });
  
  it('applies active class when showDetailPanel is true', () => {
    const payment = mockReceivedPayments[0];
    const onClickMock = vi.fn();
    
    render(
      <ReceivedPaymentCard 
        payment={payment}
        onClick={onClickMock}
        showDetailPanel={true}
      />
    );
    
    const card = screen.getByTestId(`received-payment-card-${payment.id}`);
    expect(card).toHaveClass('border-primary');
  });
  
  it('shows linked invoice reference when available', () => {
    const payment = mockReceivedPayments[1]; // Linked payment with invoice
    const onClickMock = vi.fn();
    
    render(
      <ReceivedPaymentCard 
        payment={payment}
        onClick={onClickMock}
        showDetailPanel={false}
        linkedInvoiceRef="INV-20250410-001" // Mocked linked invoice reference
      />
    );
    
    expect(screen.getByText('Linked to: INV-20250410-001')).toBeInTheDocument();
  });
});
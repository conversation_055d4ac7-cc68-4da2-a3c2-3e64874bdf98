import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '../utils/test-utils';
import UploadModal from '@/components/UploadModal';

// Mock the file parsing functions
vi.mock('@/lib/fileHelpers', () => ({
  parsePaymentFile: vi.fn().mockResolvedValue({
    reference: 'REF-20250401-001',
    amount: 5000,
    recipient: 'Acme Corp',
    description: 'April services payment'
  }),
  parseInvoiceFile: vi.fn().mockResolvedValue({
    reference: 'INV-20250401-001',
    amount: 6000,
    customer: 'TechSolutions Inc',
    description: 'IT Support Services',
    due_date: '2025-04-15T00:00:00Z'
  })
}));

describe('UploadModal', () => {
  it('renders payment upload modal correctly', () => {
    const onCloseMock = vi.fn();
    const onSubmitMock = vi.fn();
    
    render(
      <UploadModal 
        isOpen={true}
        onClose={onCloseMock}
        onSubmit={onSubmitMock}
        type="payment"
      />
    );
    
    expect(screen.getByText('Upload Payment File')).toBeInTheDocument();
    expect(screen.getByText('Drag and drop a payment file here or click to browse')).toBeInTheDocument();
    expect(screen.getByText('Supported formats: JSON, XML, EDI')).toBeInTheDocument();
  });
  
  it('renders invoice upload modal correctly', () => {
    const onCloseMock = vi.fn();
    const onSubmitMock = vi.fn();
    
    render(
      <UploadModal 
        isOpen={true}
        onClose={onCloseMock}
        onSubmit={onSubmitMock}
        type="invoice"
      />
    );
    
    expect(screen.getByText('Upload Invoice File')).toBeInTheDocument();
    expect(screen.getByText('Drag and drop an invoice file here or click to browse')).toBeInTheDocument();
    expect(screen.getByText('Supported formats: JSON, XML, EDI')).toBeInTheDocument();
  });
  
  it('calls onClose when close button is clicked', async () => {
    const onCloseMock = vi.fn();
    const onSubmitMock = vi.fn();
    
    const { user } = render(
      <UploadModal 
        isOpen={true}
        onClose={onCloseMock}
        onSubmit={onSubmitMock}
        type="payment"
      />
    );
    
    const closeButton = screen.getByRole('button', { name: /close/i });
    await user.click(closeButton);
    
    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });
  
  it('handles file upload correctly', async () => {
    const onCloseMock = vi.fn();
    const onSubmitMock = vi.fn();
    
    render(
      <UploadModal 
        isOpen={true}
        onClose={onCloseMock}
        onSubmit={onSubmitMock}
        type="payment"
      />
    );
    
    // Create a mock file
    const file = new File(['test content'], 'test-payment.json', { type: 'application/json' });
    
    // Get the file input
    const fileInput = screen.getByTestId('file-upload-input');
    
    // Simulate a file upload
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    // Wait for the parsing to complete and the submit button to be enabled
    await screen.findByText('Upload');
    
    // Click the submit button
    const submitButton = screen.getByRole('button', { name: /upload/i });
    fireEvent.click(submitButton);
    
    // Check that onSubmit was called with the correct data
    expect(onSubmitMock).toHaveBeenCalledTimes(1);
  });
  
  it('shows error message when file validation fails', async () => {
    // Mock failed validation
    vi.mock('@/lib/fileHelpers', () => ({
      parsePaymentFile: vi.fn().mockRejectedValue(new Error('Invalid file format')),
      parseInvoiceFile: vi.fn().mockRejectedValue(new Error('Invalid file format'))
    }));
    
    const onCloseMock = vi.fn();
    const onSubmitMock = vi.fn();
    
    render(
      <UploadModal 
        isOpen={true}
        onClose={onCloseMock}
        onSubmit={onSubmitMock}
        type="payment"
      />
    );
    
    // Create a mock file
    const file = new File(['invalid content'], 'test-payment.txt', { type: 'text/plain' });
    
    // Get the file input
    const fileInput = screen.getByTestId('file-upload-input');
    
    // Simulate a file upload
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    // Check that error message is displayed
    await screen.findByText('Error: Invalid file format');
  });
  
  it('shows a disabled upload button when no file is selected', () => {
    const onCloseMock = vi.fn();
    const onSubmitMock = vi.fn();
    
    render(
      <UploadModal 
        isOpen={true}
        onClose={onCloseMock}
        onSubmit={onSubmitMock}
        type="payment"
      />
    );
    
    // Get the upload button
    const uploadButton = screen.getByRole('button', { name: /upload/i });
    
    // Check that it's disabled
    expect(uploadButton).toBeDisabled();
  });
});
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import useInvoices from '@/hooks/useInvoices';
import { server } from '../setup';
import { rest } from 'msw';
import { mockInvoices } from '../mocks/mockData';

// Create wrapper with QueryClientProvider
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useInvoices', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  
  it('fetches invoices successfully', async () => {
    const { result } = renderHook(() => useInvoices(), {
      wrapper: createWrapper(),
    });
    
    // Initially loading
    expect(result.current.isLoading).toBe(true);
    
    // Wait for data to be loaded
    await waitFor(() => expect(result.current.isLoading).toBe(false));
    
    // Check that invoices data is available
    expect(result.current.invoices).toHaveLength(mockInvoices.length);
    
    // Check filtered arrays
    expect(result.current.openInvoices.every(
      invoice => invoice.status === 'Open' || invoice.status === 'Overdue'
    )).toBe(true);
    
    expect(result.current.paidInvoices.every(
      invoice => invoice.status === 'Paid' || invoice.status === 'Reconciled'
    )).toBe(true);
  });
  
  it('returns invoice by ID correctly', async () => {
    const { result } = renderHook(() => useInvoices(), {
      wrapper: createWrapper(),
    });
    
    await waitFor(() => expect(result.current.isLoading).toBe(false));
    
    const testInvoiceId = 1;
    const invoice = result.current.getInvoiceById(testInvoiceId);
    
    expect(invoice).toBeDefined();
    expect(invoice?.id).toBe(testInvoiceId);
  });
  
  it('handles updateInvoiceStatus mutation correctly', async () => {
    const { result } = renderHook(() => useInvoices(), {
      wrapper: createWrapper(),
    });
    
    await waitFor(() => expect(result.current.isLoading).toBe(false));
    
    // Mock the response for the update status endpoint
    server.use(
      rest.post('/api/invoices/:id/update-status', (req, res, ctx) => {
        const { id } = req.params;
        const { status } = req.body;
        const invoice = mockInvoices.find(i => i.id === Number(id));
        
        if (!invoice) {
          return res(ctx.status(404));
        }
        
        const updatedInvoice = {
          ...invoice,
          status
        };
        
        return res(ctx.json(updatedInvoice));
      })
    );
    
    // Execute the mutation
    await act(async () => {
      result.current.updateInvoiceStatus({
        id: 1,
        status: 'Paid'
      });
    });
    
    // Check that the mutation was successful
    await waitFor(() => expect(result.current.updateStatusMutation.isSuccess).toBe(true));
  });
  
  it('handles generateRemittance mutation correctly', async () => {
    const { result } = renderHook(() => useInvoices(), {
      wrapper: createWrapper(),
    });
    
    await waitFor(() => expect(result.current.isLoading).toBe(false));
    
    // Mock the response for the generate remittance endpoint
    server.use(
      rest.post('/api/invoices/:id/generate-remittance', (req, res, ctx) => {
        const { id } = req.params;
        const { format } = req.body;
        const invoice = mockInvoices.find(i => i.id === Number(id));
        
        if (!invoice) {
          return res(ctx.status(404));
        }
        
        return res(ctx.json({
          id: 123,
          invoice_id: Number(id),
          format,
          content: "Sample remittance content",
          created_at: new Date().toISOString()
        }));
      })
    );
    
    // Execute the mutation
    await act(async () => {
      result.current.generateRemittance({
        id: 3,
        format: 'json'
      });
    });
    
    // Check that the mutation was successful
    await waitFor(() => expect(result.current.generateRemittanceMutation.isSuccess).toBe(true));
  });
});
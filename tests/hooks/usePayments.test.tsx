import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import usePayments from '@/hooks/usePayments';
import { server } from '../setup';
import { rest } from 'msw';
import { mockPayments } from '../mocks/mockData';

// Create wrapper with QueryClientProvider
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('usePayments', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  
  it('fetches payments successfully', async () => {
    const { result } = renderHook(() => usePayments(), {
      wrapper: createWrapper(),
    });
    
    // Initially loading
    expect(result.current.isLoading).toBe(true);
    
    // Wait for data to be loaded
    await waitFor(() => expect(result.current.isLoading).toBe(false));
    
    // Check that payments data is available
    expect(result.current.payments).toHaveLength(mockPayments.length);
    
    // Check filtered arrays
    expect(result.current.notApprovedPayments.every(
      payment => payment.status === 'Not Approved'
    )).toBe(true);
    
    expect(result.current.approvedPayments.every(
      payment => ['Approved', 'Paid', 'Reconciled'].includes(payment.status)
    )).toBe(true);
    
    expect(result.current.sentPayments.every(
      payment => ['Paid', 'Reconciled'].includes(payment.status)
    )).toBe(true);
  });
  
  it('returns payment by ID correctly', async () => {
    const { result } = renderHook(() => usePayments(), {
      wrapper: createWrapper(),
    });
    
    await waitFor(() => expect(result.current.isLoading).toBe(false));
    
    const testPaymentId = 1;
    const payment = result.current.getPaymentById(testPaymentId);
    
    expect(payment).toBeDefined();
    expect(payment?.id).toBe(testPaymentId);
  });
  
  it('handles approvePayment mutation correctly', async () => {
    const { result } = renderHook(() => usePayments(), {
      wrapper: createWrapper(),
    });
    
    await waitFor(() => expect(result.current.isLoading).toBe(false));
    
    // Mock the response for the approve payment endpoint
    server.use(
      rest.post('/api/payments/:id/approve', (req, res, ctx) => {
        const { id } = req.params;
        const payment = mockPayments.find(p => p.id === Number(id));
        
        if (!payment) {
          return res(ctx.status(404));
        }
        
        const updatedPayment = {
          ...payment,
          status: 'Approved',
          approved: true,
          approved_at: new Date().toISOString()
        };
        
        return res(ctx.json(updatedPayment));
      })
    );
    
    // Execute the mutation
    await act(async () => {
      result.current.approvePaymentMutation.mutate(1);
    });
    
    // Check that the mutation was successful
    await waitFor(() => expect(result.current.approvePaymentMutation.isSuccess).toBe(true));
  });
  
  it('handles revokePaymentApproval mutation correctly', async () => {
    const { result } = renderHook(() => usePayments(), {
      wrapper: createWrapper(),
    });
    
    await waitFor(() => expect(result.current.isLoading).toBe(false));
    
    // Mock the response for the revoke approval endpoint
    server.use(
      rest.post('/api/payments/:id/revoke', (req, res, ctx) => {
        const { id } = req.params;
        const payment = mockPayments.find(p => p.id === Number(id));
        
        if (!payment) {
          return res(ctx.status(404));
        }
        
        const updatedPayment = {
          ...payment,
          status: 'Not Approved',
          approved: false,
          approved_at: null
        };
        
        return res(ctx.json(updatedPayment));
      })
    );
    
    // Execute the mutation
    await act(async () => {
      result.current.revokePaymentApprovalMutation.mutate(2); // Using the approved payment
    });
    
    // Check that the mutation was successful
    await waitFor(() => expect(result.current.revokePaymentApprovalMutation.isSuccess).toBe(true));
  });
  
  it('handles sendPayment mutation correctly', async () => {
    const { result } = renderHook(() => usePayments(), {
      wrapper: createWrapper(),
    });
    
    await waitFor(() => expect(result.current.isLoading).toBe(false));
    
    // Mock the response for the send payment endpoint
    server.use(
      rest.post('/api/payments/:id/send', (req, res, ctx) => {
        const { id } = req.params;
        const payment = mockPayments.find(p => p.id === Number(id));
        
        if (!payment) {
          return res(ctx.status(404));
        }
        
        const updatedPayment = {
          ...payment,
          status: 'Paid',
          sent_at: new Date().toISOString()
        };
        
        return res(ctx.json(updatedPayment));
      })
    );
    
    // Execute the mutation
    await act(async () => {
      result.current.sendPaymentMutation.mutate(2); // Using the approved payment
    });
    
    // Check that the mutation was successful
    await waitFor(() => expect(result.current.sendPaymentMutation.isSuccess).toBe(true));
  });
});
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import useReceivedPayments from '@/hooks/useReceivedPayments';
import { server } from '../setup';
import { rest } from 'msw';
import { mockReceivedPayments, mockInvoices } from '../mocks/mockData';

// Create wrapper with QueryClientProvider
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useReceivedPayments', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  
  it('fetches received payments successfully', async () => {
    const { result } = renderHook(() => useReceivedPayments(), {
      wrapper: createWrapper(),
    });
    
    // Initially loading
    expect(result.current.isLoading).toBe(true);
    
    // Wait for data to be loaded
    await waitFor(() => expect(result.current.isLoading).toBe(false));
    
    // Check that received payments data is available
    expect(result.current.receivedPayments).toHaveLength(mockReceivedPayments.length);
    
    // Check filtered arrays
    expect(result.current.unlinkedPayments.every(
      payment => payment.status === 'Unlinked'
    )).toBe(true);
    
    expect(result.current.linkedPayments.every(
      payment => payment.status === 'Linked' || payment.status === 'Reconciled'
    )).toBe(true);
  });
  
  it('returns received payment by ID correctly', async () => {
    const { result } = renderHook(() => useReceivedPayments(), {
      wrapper: createWrapper(),
    });
    
    await waitFor(() => expect(result.current.isLoading).toBe(false));
    
    const testPaymentId = 1;
    const payment = result.current.getReceivedPaymentById(testPaymentId);
    
    expect(payment).toBeDefined();
    expect(payment?.id).toBe(testPaymentId);
  });
  
  it('handles linkInvoice mutation correctly', async () => {
    const { result } = renderHook(() => useReceivedPayments(), {
      wrapper: createWrapper(),
    });
    
    await waitFor(() => expect(result.current.isLoading).toBe(false));
    
    // Mock the response for the link invoice endpoint
    server.use(
      rest.post('/api/received-payments/:id/link-invoice', (req, res, ctx) => {
        const { id } = req.params;
        const { invoiceId } = req.body;
        const payment = mockReceivedPayments.find(p => p.id === Number(id));
        const invoice = mockInvoices.find(i => i.id === Number(invoiceId));
        
        if (!payment || !invoice) {
          return res(ctx.status(404));
        }
        
        const updatedPayment = {
          ...payment,
          status: 'Linked',
          invoice_id: invoiceId
        };
        
        return res(ctx.json(updatedPayment));
      })
    );
    
    // Execute the mutation
    await act(async () => {
      result.current.linkInvoice({
        paymentId: 1,
        invoiceId: 1
      });
    });
    
    // Check that the mutation was successful
    await waitFor(() => expect(result.current.linkInvoiceMutation.isSuccess).toBe(true));
  });
  
  it('handles generateRemittance mutation correctly', async () => {
    const { result } = renderHook(() => useReceivedPayments(), {
      wrapper: createWrapper(),
    });
    
    await waitFor(() => expect(result.current.isLoading).toBe(false));
    
    // Mock the response for the generate remittance endpoint
    server.use(
      rest.post('/api/received-payments/:id/generate-remittance', (req, res, ctx) => {
        const { id } = req.params;
        const { format } = req.body;
        const payment = mockReceivedPayments.find(p => p.id === Number(id));
        
        if (!payment) {
          return res(ctx.status(404));
        }
        
        return res(ctx.json({
          id: 123,
          received_payment_id: Number(id),
          format,
          content: "Sample remittance content",
          created_at: new Date().toISOString()
        }));
      })
    );
    
    // Execute the mutation
    await act(async () => {
      result.current.generateRemittance({
        id: 2, // Using linked payment
        format: 'json'
      });
    });
    
    // Check that the mutation was successful
    await waitFor(() => expect(result.current.generateRemittanceMutation.isSuccess).toBe(true));
  });
  
  it('returns linked invoice for a payment', async () => {
    const { result } = renderHook(() => useReceivedPayments(), {
      wrapper: createWrapper(),
    });
    
    // Mock the response for invoices
    server.use(
      rest.get('/api/invoices', (req, res, ctx) => {
        return res(ctx.json(mockInvoices));
      })
    );
    
    await waitFor(() => expect(result.current.isLoading).toBe(false));
    
    // Set up a payment with a linked invoice
    const paymentWithInvoice = {
      ...mockReceivedPayments[1],
      invoice_id: 1
    };
    
    const linkedInvoice = result.current.getLinkedInvoice(paymentWithInvoice);
    
    expect(linkedInvoice).toBeDefined();
    expect(linkedInvoice?.id).toBe(1);
  });
});
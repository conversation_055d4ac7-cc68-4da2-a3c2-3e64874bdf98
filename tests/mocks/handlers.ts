import { http, HttpResponse } from 'msw';
import {
  mockPayments,
  mockInvoices,
  mockReceivedPayments,
  mockRemittances
} from './mockData';

// MSW handlers for mocking API requests in tests
export const handlers = [
  // Payments API
  http.get('/api/payments', () => {
    return HttpResponse.json(mockPayments);
  }),
  
  http.get('/api/payments/:id', ({ params }) => {
    const { id } = params;
    const payment = mockPayments.find(p => p.id === Number(id));
    if (!payment) {
      return new HttpResponse(null, { status: 404 });
    }
    return HttpResponse.json(payment);
  }),
  
  http.post('/api/payments/:id/approve', async ({ params }) => {
    const { id } = params;
    const payment = mockPayments.find(p => p.id === Number(id));
    if (!payment) {
      return new HttpResponse(null, { status: 404 });
    }
    
    const updatedPayment = {
      ...payment,
      status: 'Approved',
      approved: true,
      approved_at: new Date().toISOString()
    };
    
    return HttpResponse.json(updatedPayment);
  }),
  
  http.post('/api/payments/:id/revoke', async ({ params }) => {
    const { id } = params;
    const payment = mockPayments.find(p => p.id === Number(id));
    if (!payment) {
      return new HttpResponse(null, { status: 404 });
    }
    
    const updatedPayment = {
      ...payment,
      status: 'Not Approved',
      approved: false,
      approved_at: null
    };
    
    return HttpResponse.json(updatedPayment);
  }),
  
  http.post('/api/payments/:id/send', async ({ params }) => {
    const { id } = params;
    const payment = mockPayments.find(p => p.id === Number(id));
    if (!payment) {
      return new HttpResponse(null, { status: 404 });
    }
    
    const updatedPayment = {
      ...payment,
      status: 'Paid',
      sent_at: new Date().toISOString()
    };
    
    return HttpResponse.json(updatedPayment);
  }),
  
  http.post('/api/payments/:id/generate-remittance', async ({ request, params }) => {
    const { id } = params;
    const { format } = await request.json();
    
    const payment = mockPayments.find(p => p.id === Number(id));
    if (!payment) {
      return new HttpResponse(null, { status: 404 });
    }
    
    const newRemittance = {
      id: mockRemittances.length + 1,
      payment_id: Number(id),
      format,
      status: 'Generated',
      created_at: new Date().toISOString(),
      file_path: `/files/remittance_${id}_${format.toLowerCase()}.txt`,
      amount: payment.amount,
      sender: payment.sender,
      recipient: payment.recipient,
      reference: payment.reference
    };
    
    const updatedPayment = {
      ...payment,
      status: 'Reconciled',
      remittance_generated: true,
      remittance_generated_at: new Date().toISOString(),
      remittance_id: newRemittance.id
    };
    
    return HttpResponse.json({
      payment: updatedPayment,
      remittance: newRemittance
    });
  }),
  
  http.post('/api/upload_payment', async () => {
    const newPayment = {
      id: mockPayments.length + 1,
      reference: `REF-${Date.now()}`,
      amount: 1000,
      sender: 'Test Company',
      recipient: 'Vendor XYZ',
      status: 'Not Approved',
      approved: false,
      file_type: 'PEXR2002',
      file_content: 'mock content',
      created_at: new Date().toISOString()
    };
    
    return HttpResponse.json(newPayment);
  }),
  
  // Invoices API
  http.get('/api/invoices', () => {
    return HttpResponse.json(mockInvoices);
  }),
  
  http.get('/api/invoices/:id', ({ params }) => {
    const { id } = params;
    const invoice = mockInvoices.find(i => i.id === Number(id));
    if (!invoice) {
      return new HttpResponse(null, { status: 404 });
    }
    return HttpResponse.json(invoice);
  }),
  
  http.post('/api/invoices', async ({ request }) => {
    const data = await request.json();
    
    const newInvoice = {
      id: mockInvoices.length + 1,
      ...data,
      status: 'Open',
      created_at: new Date().toISOString()
    };
    
    return HttpResponse.json(newInvoice);
  }),
  
  http.post('/api/invoices/:id/update-status', async ({ request, params }) => {
    const { id } = params;
    const { status } = await request.json();
    
    const invoice = mockInvoices.find(i => i.id === Number(id));
    if (!invoice) {
      return new HttpResponse(null, { status: 404 });
    }
    
    const updatedInvoice = {
      ...invoice,
      status
    };
    
    return HttpResponse.json(updatedInvoice);
  }),
  
  http.post('/api/upload_invoice', async () => {
    const newInvoice = {
      id: mockInvoices.length + 1,
      reference: `INV-${Date.now()}`,
      amount: 1500,
      customer: 'Test Customer',
      description: 'Test Invoice',
      due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      status: 'Open',
      file_type: 'EDI X12',
      file_content: 'mock content',
      created_at: new Date().toISOString()
    };
    
    return HttpResponse.json(newInvoice);
  }),
  
  // Received Payments API
  http.get('/api/received-payments', () => {
    return HttpResponse.json(mockReceivedPayments);
  }),
  
  http.get('/api/received-payments/:id', ({ params }) => {
    const { id } = params;
    const payment = mockReceivedPayments.find(p => p.id === Number(id));
    if (!payment) {
      return new HttpResponse(null, { status: 404 });
    }
    return HttpResponse.json(payment);
  }),
  
  http.post('/api/received-payments/:id/link-invoice', async ({ request, params }) => {
    const { id } = params;
    const { invoiceId } = await request.json();
    
    const payment = mockReceivedPayments.find(p => p.id === Number(id));
    if (!payment) {
      return new HttpResponse(null, { status: 404 });
    }
    
    const invoice = mockInvoices.find(i => i.id === Number(invoiceId));
    if (!invoice) {
      return new HttpResponse(null, { status: 404 });
    }
    
    const updatedPayment = {
      ...payment,
      status: 'Linked',
      invoice_id: Number(invoiceId)
    };
    
    const updatedInvoice = {
      ...invoice,
      status: 'Paid',
      payment_id: Number(id)
    };
    
    return HttpResponse.json({
      receivedPayment: updatedPayment,
      invoice: updatedInvoice
    });
  }),
  
  http.post('/api/simulate_received_payment', async ({ request }) => {
    const data = await request.json();
    
    const newPayment = {
      id: mockReceivedPayments.length + 1,
      ...data,
      status: 'Unlinked',
      created_at: new Date().toISOString()
    };
    
    return HttpResponse.json(newPayment);
  }),
  
  // Remittances API
  http.get('/api/remittances/:id', ({ params }) => {
    const { id } = params;
    const remittance = mockRemittances.find(r => r.id === Number(id));
    if (!remittance) {
      return new HttpResponse(null, { status: 404 });
    }
    return HttpResponse.json(remittance);
  }),
  
  http.get('/api/remittances/:id/download', ({ params }) => {
    const { id } = params;
    const remittance = mockRemittances.find(r => r.id === Number(id));
    if (!remittance) {
      return new HttpResponse(null, { status: 404 });
    }
    
    // Return mock file content
    return new HttpResponse(
      `Mock remittance file content for ${remittance.reference}`,
      {
        headers: {
          'Content-Type': 'text/plain',
          'Content-Disposition': `attachment; filename="remittance_${id}.txt"`
        }
      }
    );
  })
];
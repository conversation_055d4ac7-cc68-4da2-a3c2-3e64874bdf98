// Mock data for payments
export const mockPayments = [
  {
    id: 1,
    reference: "REF-20250401-001",
    amount: 5000,
    recipient: "Acme Corp",
    description: "April services payment",
    status: "Not Approved",
    approved: false,
    approved_at: null,
    sent_at: null,
    created_at: "2025-04-01T12:00:00Z",
    remittance_id: null
  },
  {
    id: 2,
    reference: "REF-20250402-001",
    amount: 7500,
    recipient: "Beta Supplies",
    description: "Q2 inventory purchase",
    status: "Approved",
    approved: true,
    approved_at: "2025-04-02T14:30:00Z",
    sent_at: null,
    created_at: "2025-04-02T10:00:00Z",
    remittance_id: null
  },
  {
    id: 3,
    reference: "REF-20250403-001",
    amount: 2000,
    recipient: "Gamma Services",
    description: "Consulting services",
    status: "Paid",
    approved: true,
    approved_at: "2025-04-03T09:15:00Z",
    sent_at: "2025-04-03T11:00:00Z",
    created_at: "2025-04-03T08:30:00Z",
    remittance_id: null
  },
  {
    id: 4,
    reference: "REF-20250404-001",
    amount: 3000,
    recipient: "Delta Inc",
    description: "Software licenses",
    status: "Reconciled",
    approved: true,
    approved_at: "2025-04-04T10:20:00Z",
    sent_at: "2025-04-04T13:45:00Z",
    created_at: "2025-04-04T09:00:00Z",
    remittance_id: 1
  }
];

// Mock data for invoices
export const mockInvoices = [
  {
    id: 1,
    reference: "INV-20250401-001",
    amount: 6000,
    customer: "TechSolutions Inc",
    description: "IT Support Services",
    status: "Open",
    payment_id: null,
    remittance_id: null,
    due_date: "2025-04-15T00:00:00Z",
    created_at: "2025-04-01T09:00:00Z"
  },
  {
    id: 2,
    reference: "INV-20250315-002",
    amount: 4500,
    customer: "InnovateSoft",
    description: "Software Development",
    status: "Overdue",
    payment_id: null,
    remittance_id: null,
    due_date: "2025-03-30T00:00:00Z",
    created_at: "2025-03-15T11:30:00Z"
  },
  {
    id: 3,
    reference: "INV-20250320-001",
    amount: 3000,
    customer: "GlobalTech",
    description: "Cloud Services Q2",
    status: "Paid",
    payment_id: 2,
    remittance_id: null,
    due_date: "2025-04-05T00:00:00Z",
    created_at: "2025-03-20T14:00:00Z"
  },
  {
    id: 4,
    reference: "INV-20250310-001",
    amount: 8000,
    customer: "DataSys Corp",
    description: "Database Migration",
    status: "Reconciled",
    payment_id: 3,
    remittance_id: 2,
    due_date: "2025-03-25T00:00:00Z",
    created_at: "2025-03-10T10:00:00Z"
  }
];

// Mock data for received payments
export const mockReceivedPayments = [
  {
    id: 1,
    reference: "REC-20250405-001",
    amount: 6000,
    sender: "TechSolutions Inc",
    description: "IT Support Payment",
    status: "Unlinked",
    invoice_id: null,
    remittance_id: null,
    received_at: "2025-04-05T15:30:00Z",
    created_at: "2025-04-05T15:35:00Z"
  },
  {
    id: 2,
    reference: "REC-20250406-001",
    amount: 4500,
    sender: "InnovateSoft",
    description: "Software Development Payment",
    status: "Linked",
    invoice_id: 2,
    remittance_id: null,
    received_at: "2025-04-06T09:20:00Z",
    created_at: "2025-04-06T09:25:00Z"
  },
  {
    id: 3,
    reference: "REC-20250407-001",
    amount: 3000,
    sender: "GlobalTech",
    description: "Cloud Services Payment",
    status: "Remitted",
    invoice_id: 3,
    remittance_id: 3,
    received_at: "2025-04-07T11:15:00Z",
    created_at: "2025-04-07T11:20:00Z"
  }
];

// Mock data for remittances
export const mockRemittances = [
  {
    id: 1,
    payment_id: 4,
    format: "json",
    content: `{
      "reference": "REM-20250404-001",
      "payment_reference": "REF-20250404-001",
      "amount": 3000,
      "recipient": "Delta Inc",
      "sender": "ProofPay",
      "created_at": "2025-04-04T14:00:00Z"
    }`,
    created_at: "2025-04-04T14:00:00Z"
  },
  {
    id: 2,
    invoice_id: 4,
    format: "json",
    content: `{
      "reference": "REM-20250320-001",
      "invoice_reference": "INV-20250310-001",
      "amount": 8000,
      "customer": "DataSys Corp",
      "created_at": "2025-03-22T10:30:00Z"
    }`,
    created_at: "2025-03-22T10:30:00Z"
  },
  {
    id: 3,
    received_payment_id: 3,
    format: "json",
    content: `{
      "reference": "REM-20250407-001",
      "payment_reference": "REC-20250407-001",
      "invoice_reference": "INV-20250320-001",
      "amount": 3000,
      "sender": "GlobalTech",
      "created_at": "2025-04-07T12:00:00Z"
    }`,
    created_at: "2025-04-07T12:00:00Z"
  }
];
import { vi, beforeAll, afterEach, afterAll } from 'vitest';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { mockPayments, mockInvoices, mockReceivedPayments, mockRemittances } from './mocks/mockData';

// Define handlers
const handlers = [
  // Payments endpoints
  http.get('/api/payments', () => {
    return HttpResponse.json(mockPayments);
  }),
  
  http.get('/api/payments/:id', ({ params }) => {
    const { id } = params;
    const payment = mockPayments.find(p => p.id === Number(id));
    
    if (!payment) {
      return new HttpResponse(null, { status: 404 });
    }
    
    return HttpResponse.json(payment);
  }),
  
  http.post('/api/payments/:id/approve', ({ params }) => {
    const { id } = params;
    const payment = mockPayments.find(p => p.id === Number(id));
    
    if (!payment) {
      return new HttpResponse(null, { status: 404 });
    }
    
    const updatedPayment = {
      ...payment,
      status: 'Approved',
      approved: true,
      approved_at: new Date().toISOString()
    };
    
    return HttpResponse.json(updatedPayment);
  }),
  
  http.post('/api/payments/:id/revoke', ({ params }) => {
    const { id } = params;
    const payment = mockPayments.find(p => p.id === Number(id));
    
    if (!payment) {
      return new HttpResponse(null, { status: 404 });
    }
    
    const updatedPayment = {
      ...payment,
      status: 'Not Approved',
      approved: false,
      approved_at: null
    };
    
    return HttpResponse.json(updatedPayment);
  }),
  
  http.post('/api/payments/:id/send', ({ params }) => {
    const { id } = params;
    const payment = mockPayments.find(p => p.id === Number(id));
    
    if (!payment) {
      return new HttpResponse(null, { status: 404 });
    }
    
    const updatedPayment = {
      ...payment,
      status: 'Paid',
      sent_at: new Date().toISOString()
    };
    
    return HttpResponse.json(updatedPayment);
  }),
  
  http.post('/api/payments/:id/generate-remittance', async ({ params, request }) => {
    const { id } = params;
    const data = await request.json();
    const { format } = data;
    const payment = mockPayments.find(p => p.id === Number(id));
    
    if (!payment) {
      return new HttpResponse(null, { status: 404 });
    }
    
    const remittance = {
      id: 100,
      payment_id: Number(id),
      format,
      content: `Sample remittance content for payment ${id} in ${format} format`,
      created_at: new Date().toISOString()
    };
    
    return HttpResponse.json(remittance);
  }),
  
  // Invoices endpoints
  http.get('/api/invoices', () => {
    return HttpResponse.json(mockInvoices);
  }),
  
  http.get('/api/invoices/:id', ({ params }) => {
    const { id } = params;
    const invoice = mockInvoices.find(i => i.id === Number(id));
    
    if (!invoice) {
      return new HttpResponse(null, { status: 404 });
    }
    
    return HttpResponse.json(invoice);
  }),
  
  http.post('/api/invoices/:id/update-status', async ({ params, request }) => {
    const { id } = params;
    const data = await request.json();
    const { status } = data;
    const invoice = mockInvoices.find(i => i.id === Number(id));
    
    if (!invoice) {
      return new HttpResponse(null, { status: 404 });
    }
    
    const updatedInvoice = {
      ...invoice,
      status
    };
    
    return HttpResponse.json(updatedInvoice);
  }),
  
  http.post('/api/invoices/:id/generate-remittance', async ({ params, request }) => {
    const { id } = params;
    const data = await request.json();
    const { format } = data;
    const invoice = mockInvoices.find(i => i.id === Number(id));
    
    if (!invoice) {
      return new HttpResponse(null, { status: 404 });
    }
    
    const remittance = {
      id: 101,
      invoice_id: Number(id),
      format,
      content: `Sample remittance content for invoice ${id} in ${format} format`,
      created_at: new Date().toISOString()
    };
    
    return HttpResponse.json(remittance);
  }),
  
  // Received payments endpoints
  http.get('/api/received-payments', () => {
    return HttpResponse.json(mockReceivedPayments);
  }),
  
  http.get('/api/received-payments/:id', ({ params }) => {
    const { id } = params;
    const payment = mockReceivedPayments.find(p => p.id === Number(id));
    
    if (!payment) {
      return new HttpResponse(null, { status: 404 });
    }
    
    return HttpResponse.json(payment);
  }),
  
  http.post('/api/received-payments/:id/link-invoice', async ({ params, request }) => {
    const { id } = params;
    const data = await request.json();
    const { invoiceId } = data;
    const payment = mockReceivedPayments.find(p => p.id === Number(id));
    const invoice = mockInvoices.find(i => i.id === Number(invoiceId));
    
    if (!payment || !invoice) {
      return new HttpResponse(null, { status: 404 });
    }
    
    const updatedPayment = {
      ...payment,
      status: 'Linked',
      invoice_id: Number(invoiceId)
    };
    
    return HttpResponse.json(updatedPayment);
  }),
  
  http.post('/api/received-payments/:id/generate-remittance', async ({ params, request }) => {
    const { id } = params;
    const data = await request.json();
    const { format } = data;
    const payment = mockReceivedPayments.find(p => p.id === Number(id));
    
    if (!payment) {
      return new HttpResponse(null, { status: 404 });
    }
    
    const remittance = {
      id: 102,
      received_payment_id: Number(id),
      format,
      content: `Sample remittance content for received payment ${id} in ${format} format`,
      created_at: new Date().toISOString()
    };
    
    return HttpResponse.json(remittance);
  }),
  
  // Remittances endpoints
  http.get('/api/remittances', () => {
    return HttpResponse.json(mockRemittances);
  }),
  
  http.get('/api/remittances/:id', ({ params }) => {
    const { id } = params;
    const remittance = mockRemittances.find(r => r.id === Number(id));
    
    if (!remittance) {
      return new HttpResponse(null, { status: 404 });
    }
    
    return HttpResponse.json(remittance);
  })
];

// Setup MSW server
export const server = setupServer(...handlers);

// Start server before all tests
beforeAll(() => server.listen({ onUnhandledRequest: 'error' }));

//  Close server after all tests
afterAll(() => server.close());

// Reset handlers after each test
afterEach(() => server.resetHandlers());

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Establish API mocking before all tests
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));